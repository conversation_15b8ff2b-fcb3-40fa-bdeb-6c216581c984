"""
查找有历史赔率数据的比赛

该脚本帮助用户快速找到有历史赔率数据的比赛，以便测试历史区间分析功能。
"""

import sys
import os
import sqlite3
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from football_analysis_system.config import DB_MATCHES
from football_analysis_system.db.database_manager import DatabaseManager

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def find_matches_with_historical_data():
    """查找有历史赔率数据的比赛"""
    try:
        print("🔍 查找有历史赔率数据的比赛...")
        print("=" * 60)
        
        # 检查数据库文件是否存在
        if not os.path.exists(DB_MATCHES):
            print(f"❌ 数据库文件不存在: {DB_MATCHES}")
            return []
        
        db_manager = DatabaseManager(DB_MATCHES)
        
        # 检查history_odds表是否存在
        tables = db_manager.get_table_names()
        if 'history_odds' not in tables:
            print("❌ history_odds表不存在")
            return []
        
        # 获取有历史数据的比赛信息
        query = """
        SELECT 
            h.match_id,
            m.home_team,
            m.away_team,
            m.league_name,
            m.start_time,
            COUNT(DISTINCT h.company_id) as company_count,
            COUNT(h.id) as record_count,
            MIN(h.update_time) as earliest_time,
            MAX(h.update_time) as latest_time
        FROM history_odds h
        LEFT JOIN matches m ON h.match_id = m.match_id
        GROUP BY h.match_id
        ORDER BY record_count DESC
        """
        
        results = db_manager.execute_query(query)
        
        if not results:
            print("❌ 没有找到任何历史赔率数据")
            return []
        
        print(f"✅ 找到 {len(results)} 场有历史数据的比赛:\n")
        
        matches_info = []
        
        for i, row in enumerate(results, 1):
            match_id = row[0]
            home_team = row[1] or "未知主队"
            away_team = row[2] or "未知客队"
            league_name = row[3] or "未知联赛"
            start_time = row[4] or "未知时间"
            company_count = row[5]
            record_count = row[6]
            earliest_time = row[7] or "未知"
            latest_time = row[8] or "未知"
            
            match_info = {
                'match_id': match_id,
                'home_team': home_team,
                'away_team': away_team,
                'league_name': league_name,
                'start_time': start_time,
                'company_count': company_count,
                'record_count': record_count,
                'time_range': f"{earliest_time} ~ {latest_time}"
            }
            matches_info.append(match_info)
            
            print(f"📊 比赛 {i}:")
            print(f"   比赛ID: {match_id}")
            print(f"   对阵: {home_team} vs {away_team}")
            print(f"   联赛: {league_name}")
            print(f"   比赛时间: {start_time}")
            print(f"   博彩公司数: {company_count} 家")
            print(f"   历史记录数: {record_count} 条")
            print(f"   数据时间范围: {earliest_time} ~ {latest_time}")
            print()
        
        # 显示每场比赛的博彩公司详情
        print("📋 各比赛的博彩公司详情:")
        print("-" * 60)
        
        for match_info in matches_info:
            match_id = match_info['match_id']
            print(f"\n🏆 比赛 {match_id} ({match_info['home_team']} vs {match_info['away_team']}):")
            
            # 获取该比赛的博彩公司信息
            company_query = """
            SELECT 
                company_id,
                company_name,
                COUNT(*) as record_count,
                MIN(update_time) as earliest,
                MAX(update_time) as latest
            FROM history_odds 
            WHERE match_id = ?
            GROUP BY company_id, company_name
            ORDER BY record_count DESC
            """
            
            companies = db_manager.execute_query(company_query, (match_id,))
            
            for company in companies:
                company_id = company[0]
                company_name = company[1]
                record_count = company[2]
                earliest = company[3]
                latest = company[4]
                
                print(f"   • {company_name} (ID: {company_id}): {record_count} 条记录 ({earliest} ~ {latest})")
        
        return matches_info
        
    except Exception as e:
        print(f"❌ 查找过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return []


def show_usage_instructions(matches_info):
    """显示使用说明"""
    if not matches_info:
        return
    
    print("\n" + "=" * 60)
    print("📖 使用说明:")
    print("=" * 60)
    
    print("\n1. 启动应用程序:")
    print("   python football_analysis_system/run_modern_app.py")
    
    print("\n2. 在左侧比赛列表中查找以下比赛ID:")
    for match_info in matches_info[:3]:  # 只显示前3个
        print(f"   • {match_info['match_id']} - {match_info['home_team']} vs {match_info['away_team']}")
    
    print("\n3. 选择比赛后，点击右侧的 '📊 历史区间' 标签页")
    
    print("\n4. 在博彩公司下拉框中选择公司，然后点击 '开始分析'")
    
    print("\n💡 推荐测试比赛:")
    if matches_info:
        best_match = matches_info[0]  # 记录最多的比赛
        print(f"   比赛ID: {best_match['match_id']}")
        print(f"   对阵: {best_match['home_team']} vs {best_match['away_team']}")
        print(f"   原因: 有 {best_match['company_count']} 家博彩公司，{best_match['record_count']} 条历史记录")


def main():
    """主函数"""
    print("历史赔率区间分析 - 比赛查找工具")
    print("=" * 60)
    
    # 查找有历史数据的比赛
    matches_info = find_matches_with_historical_data()
    
    # 显示使用说明
    show_usage_instructions(matches_info)
    
    if matches_info:
        print(f"\n✅ 总共找到 {len(matches_info)} 场有历史数据的比赛")
        print("现在您可以使用这些比赛ID来测试历史赔率区间分析功能了！")
    else:
        print("\n❌ 没有找到有历史数据的比赛")
        print("建议:")
        print("1. 运行数据抓取功能获取历史数据")
        print("2. 检查数据库文件是否正确")
        print("3. 确认odds_history表中有数据")
    
    return len(matches_info) > 0


if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)