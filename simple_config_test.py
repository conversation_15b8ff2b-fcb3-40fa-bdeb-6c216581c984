#!/usr/bin/env python3
"""
简单的配置测试
"""

import sys
import os
from pathlib import Path

print("开始测试...")

# 测试基本的Path操作
try:
    current_path = Path.cwd().resolve()
    print(f"当前路径: {current_path}")
    
    # 测试查找项目根目录
    markers = ["main.py", "football_analysis_system"]
    
    for marker in markers:
        marker_path = current_path / marker
        if marker_path.exists():
            print(f"找到标识文件: {marker_path}")
            break
    else:
        print("未找到标识文件")
    
    print("基本路径测试通过")
    
except Exception as e:
    print(f"基本路径测试失败: {e}")
    import traceback
    traceback.print_exc()

# 测试JSON配置文件
try:
    import json
    config_file = Path("football_analysis_system/config/config.json")
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("配置文件加载成功")
        print(f"路径配置: {config.get('paths', {})}")
    else:
        print(f"配置文件不存在: {config_file}")
        
except Exception as e:
    print(f"配置文件测试失败: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
