"""
高级泊松引擎 - 完整的足球进球预测系统
结合融合预测器的λ参数，生成详细的比赛预测结果
"""

import numpy as np
from scipy.stats import poisson
from typing import Dict, List, Optional, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

class AdvancedPoissonEngine:
    """高级泊松引擎 - 综合预测分析系统"""
    
    def __init__(self, hybrid_predictor):
        """
        初始化高级泊松引擎
        
        Args:
            hybrid_predictor: 融合预测器实例
        """
        self.hybrid_predictor = hybrid_predictor
        self.max_goals = 6  # 最大进球数计算范围
        
    def comprehensive_match_analysis(self, match_id, home_team, away_team, 
                                   strategy='auto', line=2.5):
        """
        全面的比赛分析
        
        Args:
            match_id: 比赛ID
            home_team: 主队名称
            away_team: 客队名称
            strategy: 预测策略
            line: 大小球盘口线
            
        Returns:
            dict: 完整的分析结果
        """
        try:
            print(f"🔍 AdvancedPoissonEngine 接收到的盘口: {line}")
            
            # 使用混合预测器进行分析
            fusion_result = self.hybrid_predictor.predict_match_lambdas(
                match_id, home_team, away_team, strategy, line
            )
            
            if not fusion_result['success']:
                return self._get_fallback_analysis(match_id, home_team, away_team, line)
            
            # 提取λ值
            lambda_estimates = fusion_result['lambda_estimates']
            lambda_home = lambda_estimates['home']
            lambda_away = lambda_estimates['away']
            
            # 生成比分矩阵
            score_matrix = self._generate_score_matrix(lambda_home, lambda_away)
            
            # 分析比赛结果概率
            match_outcomes = self._calculate_match_outcomes(score_matrix)
            
            # 分析进球区间
            goal_intervals = self._analyze_goal_intervals(lambda_home, lambda_away)
            
            # 分析大小球概率 - 使用实际的盘口线
            actual_line = fusion_result['match_info']['line']
            print(f"📊 融合分析返回的实际盘口: {actual_line}")
            print(f"🎯 将用于大小球概率分析的盘口: {actual_line}")
            
            overunder_analysis = self._analyze_overunder_probabilities(lambda_home, lambda_away, actual_line)
            
            # 最可能比分
            most_likely_scores = self._find_most_likely_scores(score_matrix)
            
            # 统计汇总
            statistics = self._generate_statistics_summary(lambda_home, lambda_away, match_outcomes, goal_intervals)
            
            # 确保融合分析结果包含市场分析信息
            fusion_analysis = fusion_result.copy()
            
            # 如果有市场分析数据，确保包含庄家利润剔除信息
            component_results = fusion_result.get('component_results', {})
            market_analysis = component_results.get('market_analysis', {})
            if market_analysis and ('selected_methods' in market_analysis or 'margin_analysis' in market_analysis):
                # 将市场分析信息添加到method_comparison中
                if 'method_comparison' not in fusion_analysis:
                    fusion_analysis['method_comparison'] = {}
                fusion_analysis['method_comparison']['market_analysis'] = market_analysis
            
            print(f"✅ 最终分析结果中的盘口线: {fusion_result['match_info']['line']}")
            
            return {
                'success': True,
                'match_info': fusion_result['match_info'],
                'fusion_analysis': fusion_analysis,
                'poisson_predictions': {
                    'lambda_parameters': {
                        'home': lambda_home,
                        'away': lambda_away,
                        'total': lambda_home + lambda_away
                    },
                    'score_matrix': score_matrix,
                    'match_outcomes': match_outcomes,
                    'goal_intervals': goal_intervals,
                    'overunder_analysis': overunder_analysis,
                    'most_likely_scores': most_likely_scores,
                    'statistics_summary': statistics
                }
            }
            
        except Exception as e:
            print(f"高级泊松分析失败: {e}")
            return self._get_fallback_analysis(match_id, home_team, away_team, line)
    
    def _generate_score_matrix(self, lambda_home, lambda_away):
        """
        生成比分概率矩阵
        """
        matrix = []
        
        for home_goals in range(self.max_goals + 1):
            row = []
            for away_goals in range(self.max_goals + 1):
                # 泊松概率
                home_prob = poisson.pmf(home_goals, lambda_home)
                away_prob = poisson.pmf(away_goals, lambda_away)
                score_prob = home_prob * away_prob
                
                # 计算赔率
                odds = 1 / score_prob if score_prob > 0 else float('inf')
                
                row.append({
                    'home_goals': home_goals,
                    'away_goals': away_goals,
                    'probability': score_prob,
                    'percentage': score_prob * 100,
                    'odds': odds,
                    'score_string': f"{home_goals}-{away_goals}"
                })
            matrix.append(row)
        
        return matrix
    
    def _calculate_match_outcomes(self, score_matrix):
        """
        计算比赛结果概率
        """
        home_win = 0.0
        draw = 0.0
        away_win = 0.0
        
        # 遍历比分矩阵
        for row in score_matrix:
            for score in row:
                h_goals = score['home_goals']
                a_goals = score['away_goals']
                prob = score['probability']
                
                if h_goals > a_goals:
                    home_win += prob
                elif h_goals == a_goals:
                    draw += prob
                else:
                    away_win += prob
        
        return {
            'home_win': {
                'probability': home_win,
                'percentage': home_win * 100,
                'odds': 1 / home_win if home_win > 0 else float('inf')
            },
            'draw': {
                'probability': draw,
                'percentage': draw * 100,
                'odds': 1 / draw if draw > 0 else float('inf')
            },
            'away_win': {
                'probability': away_win,
                'percentage': away_win * 100,
                'odds': 1 / away_win if away_win > 0 else float('inf')
            }
        }
    
    def _analyze_goal_intervals(self, lambda_home, lambda_away):
        """
        分析进球区间概率
        """
        lambda_total = lambda_home + lambda_away
        
        intervals = {
            '0-1_goals': poisson.cdf(1, lambda_total),
            '2-3_goals': poisson.cdf(3, lambda_total) - poisson.cdf(1, lambda_total),
            '4-5_goals': poisson.cdf(5, lambda_total) - poisson.cdf(3, lambda_total),
            '6+_goals': 1 - poisson.cdf(5, lambda_total)
        }
        
        # 转换为百分比和赔率
        result = {}
        for interval, prob in intervals.items():
            result[interval] = {
                'probability': prob,
                'percentage': prob * 100,
                'odds': 1 / prob if prob > 0 else float('inf')
            }
        
        return result
    
    def _analyze_overunder_probabilities(self, lambda_home, lambda_away, line):
        """
        分析大小球概率
        """
        lambda_total = lambda_home + lambda_away
        
        # 计算小球概率（≤ line）
        under_prob = poisson.cdf(int(line), lambda_total)
        over_prob = 1 - under_prob
        
        # 如果line是小数（如2.5），需要特殊处理
        if line != int(line):
            # 对于2.5球，小球是≤2球
            under_prob = poisson.cdf(int(line), lambda_total)
            over_prob = 1 - under_prob
        
        return {
            f'under_{line}': {
                'probability': under_prob,
                'percentage': under_prob * 100,
                'odds': 1 / under_prob if under_prob > 0 else float('inf')
            },
            f'over_{line}': {
                'probability': over_prob,
                'percentage': over_prob * 100,
                'odds': 1 / over_prob if over_prob > 0 else float('inf')
            },
            'line': line,
            'expected_total_goals': lambda_total
        }
    
    def _find_most_likely_scores(self, score_matrix, top_n=10):
        """
        找出最可能的比分
        """
        # 提取所有比分概率
        all_scores = []
        for row in score_matrix:
            all_scores.extend(row)
        
        # 按概率排序
        all_scores.sort(key=lambda x: x['probability'], reverse=True)
        
        return all_scores[:top_n]
    
    def _generate_statistics_summary(self, lambda_home, lambda_away, match_outcomes, goal_intervals):
        """
        生成统计汇总
        """
        lambda_total = lambda_home + lambda_away
        
        # 期望值
        expected_home_goals = lambda_home
        expected_away_goals = lambda_away
        expected_total_goals = lambda_total
        
        # 概率最高的结果
        max_outcome = max(match_outcomes.items(), key=lambda x: x[1]['probability'])
        most_likely_outcome = max_outcome[0]
        
        # 最可能的进球区间
        max_interval = max(goal_intervals.items(), key=lambda x: x[1]['probability'])
        most_likely_interval = max_interval[0]
        
        return {
            'expected_goals': {
                'home': expected_home_goals,
                'away': expected_away_goals,
                'total': expected_total_goals,
                'goal_difference': expected_home_goals - expected_away_goals
            },
            'most_likely': {
                'outcome': most_likely_outcome,
                'outcome_probability': match_outcomes[most_likely_outcome]['percentage'],
                'goal_interval': most_likely_interval,
                'interval_probability': goal_intervals[most_likely_interval]['percentage']
            },
            'variance': {
                'home': lambda_home,  # 泊松分布的方差等于λ
                'away': lambda_away,
                'total': lambda_total
            },
            'standard_deviation': {
                'home': np.sqrt(lambda_home),
                'away': np.sqrt(lambda_away),
                'total': np.sqrt(lambda_total)
            }
        }
    
    def generate_detailed_report(self, analysis_result):
        """
        生成详细的分析报告
        """
        if not analysis_result['success']:
            return "❌ 分析失败，无法生成报告"
        
        match_info = analysis_result['match_info']
        fusion_analysis = analysis_result['fusion_analysis']
        predictions = analysis_result['poisson_predictions']
        
        report = []
        
        # 标题
        report.append("🏆 高级足球进球预测分析报告")
        report.append("=" * 50)
        
        # 比赛信息
        report.append(f"⚽ 比赛: {match_info['home_team']} vs {match_info['away_team']}")
        report.append(f"🆔 比赛ID: {match_info['match_id']}")
        report.append(f"🎯 大小球盘口: {match_info['line']}")
        report.append("")
        
        # 融合分析结果
        report.append("📊 融合分析结果:")
        weights = fusion_analysis['fusion_weights']
        report.append(f"   权重配置: 历史数据 {weights['historical']:.1%} + 市场数据 {weights['market']:.1%}")
        
        consistency = fusion_analysis['lambda_estimates']['method_consistency']
        report.append(f"   方法一致性: {consistency['assessment']} ({consistency['score']:.1%})")
        
        assessment = fusion_analysis['comprehensive_assessment']
        report.append(f"   综合置信度: {assessment['confidence_level']}")
        report.append(f"   使用建议: {assessment['recommendation']}")
        report.append("")
        
        # λ参数
        lambdas = predictions['lambda_parameters']
        report.append("🎯 泊松参数 (λ):")
        report.append(f"   主队进球期望: {lambdas['home']:.3f}")
        report.append(f"   客队进球期望: {lambdas['away']:.3f}")
        report.append(f"   总进球期望: {lambdas['total']:.3f}")
        report.append("")
        
        # 比赛结果概率
        outcomes = predictions['match_outcomes']
        report.append("🏁 比赛结果概率:")
        report.append(f"   主队获胜: {outcomes['home_win']['percentage']:.1f}% (赔率 {outcomes['home_win']['odds']:.2f})")
        report.append(f"   平局: {outcomes['draw']['percentage']:.1f}% (赔率 {outcomes['draw']['odds']:.2f})")
        report.append(f"   客队获胜: {outcomes['away_win']['percentage']:.1f}% (赔率 {outcomes['away_win']['odds']:.2f})")
        report.append("")
        
        # 进球区间
        intervals = predictions['goal_intervals']
        report.append("⚽ 进球区间概率:")
        for interval, data in intervals.items():
            report.append(f"   {interval.replace('_', ' ')}: {data['percentage']:.1f}% (赔率 {data['odds']:.2f})")
        report.append("")
        
        # 大小球分析
        overunder = predictions['overunder_analysis']
        line = overunder['line']
        report.append(f"🎲 大小球 {line} 分析:")
        report.append(f"   小球 (≤{line}): {overunder[f'under_{line}']['percentage']:.1f}% (赔率 {overunder[f'under_{line}']['odds']:.2f})")
        report.append(f"   大球 (>{line}): {overunder[f'over_{line}']['percentage']:.1f}% (赔率 {overunder[f'over_{line}']['odds']:.2f})")
        report.append("")
        
        # 最可能比分
        most_likely = predictions['most_likely_scores'][:5]
        report.append("🎯 最可能比分 (前5名):")
        for i, score in enumerate(most_likely, 1):
            report.append(f"   {i}. {score['score_string']}: {score['percentage']:.1f}% (赔率 {score['odds']:.1f})")
        report.append("")
        
        # 统计汇总
        stats = predictions['statistics_summary']
        report.append("📈 统计汇总:")
        report.append(f"   预期进球差: {stats['expected_goals']['goal_difference']:+.2f}")
        report.append(f"   最可能结果: {stats['most_likely']['outcome']} ({stats['most_likely']['outcome_probability']:.1f}%)")
        report.append(f"   最可能区间: {stats['most_likely']['goal_interval']} ({stats['most_likely']['interval_probability']:.1f}%)")
        
        return "\n".join(report)
    
    def _get_fallback_analysis(self, match_id, home_team, away_team, line=2.5):
        """
        获取备用分析结果
        """
        return {
            'success': False,
            'error': '分析失败，数据不足',
            'match_info': {
                'match_id': match_id,
                'home_team': home_team,
                'away_team': away_team,
                'line': line
            }
        }

if __name__ == "__main__":
    # 测试高级泊松引擎
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    
    from data_manager import DataManager
    from market_analyzer import MarketAnalyzer
    from historical_analyzer import HistoricalAnalyzer
    from hybrid_predictor import HybridPredictor
    
    print("=== 高级泊松引擎测试 ===")
    
    # 初始化完整系统
    dm = DataManager()
    ma = MarketAnalyzer()
    ha = HistoricalAnalyzer(dm)
    hp = HybridPredictor(ha, ma)
    engine = AdvancedPoissonEngine(hp)
    
    # 测试比赛
    match_id = 2702969
    home_team = "哥德堡盖斯"
    away_team = "天狼星"
    
    # 进行全面分析
    analysis = engine.comprehensive_match_analysis(match_id, home_team, away_team)
    
    if analysis['success']:
        # 生成详细报告
        report = engine.generate_detailed_report(analysis)
        print(report)
    else:
        print("❌ 分析失败") 