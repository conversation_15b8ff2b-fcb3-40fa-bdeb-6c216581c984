import logging
from football_analysis_system.db.database_manager import DatabaseManager

class OddsAnalyzer:
    """赔率分析类"""

    def __init__(self, standard_odds_db_path):
        """
        初始化赔率分析器

        Args:
            standard_odds_db_path: 标准赔率数据库路径
        """
        self.db_manager = DatabaseManager(standard_odds_db_path)
        self.standard_odds_data = []
        self.load_standard_odds_data()

    def load_standard_odds_data(self):
        """从数据库加载标准赔率数据"""
        try:
            # 获取表名
            tables = self.db_manager.get_table_names()
            if not tables:
                return

            # 查找满水返还赔率表
            target_table = None
            for table in tables:
                if "满水" in table or "返还" in table or "赔率" in table:
                    target_table = table
                    break

            # 如果没找到特定表，使用第一个表
            if not target_table:
                target_table = tables[0]

            # 获取表结构
            columns = self.db_manager.get_column_names(target_table)
            if not columns:
                return

            # 确认表结构是否包含胜平负
            if '胜' in columns and '平' in columns and '负' in columns:
                # 查询所有数据
                all_rows = self.db_manager.execute_query(f"SELECT * FROM {target_table}")

                # 构建标准赔率数据
                self.standard_odds_data = []

                # 预先获取列索引，避免重复查找
                win_idx = columns.index('胜') if '胜' in columns else -1
                draw_idx = columns.index('平') if '平' in columns else -1
                loss_idx = columns.index('负') if '负' in columns else -1
                gap_idx = columns.index('距') if '距' in columns else -1

                # 一次性预分配内存
                self.standard_odds_data = []

                for row in all_rows:
                    try:
                        # 主队胜赔率和档距
                        if win_idx >= 0 and win_idx < len(row) and self._is_valid_number(row[win_idx]):
                            odds_win = float(row[win_idx])
                            gap_win = row[gap_idx] if gap_idx >= 0 and gap_idx < len(row) else None
                            self.standard_odds_data.append({
                                'odds': odds_win,
                                'gap': gap_win,
                                'type': 'win'
                            })

                        # 平局赔率和档距
                        if draw_idx >= 0 and draw_idx < len(row) and self._is_valid_number(row[draw_idx]):
                            odds_draw = float(row[draw_idx])
                            gap_draw = row[gap_idx] if gap_idx >= 0 and gap_idx < len(row) else None
                            self.standard_odds_data.append({
                                'odds': odds_draw,
                                'gap': gap_draw,
                                'type': 'draw'
                            })

                        # 客队胜赔率和档距
                        if loss_idx >= 0 and loss_idx < len(row) and self._is_valid_number(row[loss_idx]):
                            odds_loss = float(row[loss_idx])
                            gap_loss = row[gap_idx] if gap_idx >= 0 and gap_idx < len(row) else None
                            self.standard_odds_data.append({
                                'odds': odds_loss,
                                'gap': gap_loss,
                                'type': 'loss'
                            })
                    except Exception:
                        continue

                # 按赔率排序
                self.standard_odds_data.sort(key=lambda x: x.get('odds', float('inf')))

        except Exception as e:
            logging.error(f"加载标准赔率数据时出错: {e}")

    def _is_valid_number(self, value):
        """
        检查值是否为有效数字

        Args:
            value: 要检查的值

        Returns:
            bool: 是否为有效数字
        """
        if isinstance(value, (int, float)):
            return True

        if isinstance(value, str):
            return value.replace('.', '', 1).isdigit()

        return False

    def calculate_true_odds(self, odds_value, payout_rate):
        """
        计算满水赔率 (真实赔率)
        满水赔率 = 原始赔率 * (100 / 返还率)

        Args:
            odds_value: 原始赔率值
            payout_rate: 返还率

        Returns:
            float: 满水赔率
        """
        if odds_value is None or payout_rate is None:
            return None

        try:
            odds_float = float(odds_value)
            payout_float = float(payout_rate)

            # 确保返还率大于0，避免除零错误
            if payout_float <= 0:
                return None

            # 计算满水赔率，并保留小数点后两位
            true_odds = odds_float * (100 / payout_float)
            return round(true_odds, 2)
        except (ValueError, TypeError):
            logging.error(f"无法计算满水赔率: odds_value={odds_value}, payout_rate={payout_rate}")
            return None

    def find_gap_for_odds(self, odds_value, odds_type="win"):
        """
        根据赔率值在标准赔率表中查找对应的档距

        Args:
            odds_value: 赔率值
            odds_type: 赔率类型，可以是 "win"(主队胜)、"draw"(平局)、"loss"(客队胜)

        Returns:
            值: 对应的档距，如果未找到则返回None
        """
        if not odds_value or not self.standard_odds_data:
            return "0"  # 直接返回默认值

        try:
            odds_float = float(odds_value)

            # 过滤出对应类型的赔率数据 - 预先筛选而不是每次查询
            if not hasattr(self, '_filtered_data_cache'):
                self._filtered_data_cache = {}

            # 使用缓存避免重复过滤
            if odds_type not in self._filtered_data_cache:
                self._filtered_data_cache[odds_type] = [
                    entry for entry in self.standard_odds_data
                    if entry.get('type') == odds_type and 'odds' in entry
                ]

            filtered_data = self._filtered_data_cache[odds_type]

            if not filtered_data:
                # 如果没有指定类型的数据，使用所有类型的数据
                if 'all' not in self._filtered_data_cache:
                    self._filtered_data_cache['all'] = [
                        entry for entry in self.standard_odds_data if 'odds' in entry
                    ]
                filtered_data = self._filtered_data_cache['all']

            if not filtered_data:
                return "0"  # 没有数据可查，返回默认值

            # 找到最接近的赔率和对应的档距 - 使用二分查找提高性能
            closest_entry = None
            min_diff = float('inf')

            # 对于小型数据集，直接遍历可能更快
            if len(filtered_data) <= 50:
                for entry in filtered_data:
                    entry_odds = entry.get('odds')
                    if entry_odds is not None:
                        try:
                            entry_odds_float = float(entry_odds)
                            diff = abs(entry_odds_float - odds_float)
                            if diff < min_diff:
                                min_diff = diff
                                closest_entry = entry
                        except (ValueError, TypeError):
                            continue
            else:
                # 对于大型数据集，使用更高效的算法
                # 先按赔率值排序
                if not hasattr(self, '_sorted_data_cache'):
                    self._sorted_data_cache = {}

                if odds_type not in self._sorted_data_cache:
                    # 确保数据按赔率值排序
                    self._sorted_data_cache[odds_type] = sorted(
                        filtered_data,
                        key=lambda x: float(x.get('odds', float('inf')))
                    )

                sorted_data = self._sorted_data_cache[odds_type]

                # 二分查找最接近的值
                low, high = 0, len(sorted_data) - 1

                while low <= high:
                    mid = (low + high) // 2
                    mid_odds = float(sorted_data[mid].get('odds', float('inf')))

                    if mid_odds == odds_float:
                        closest_entry = sorted_data[mid]
                        break
                    elif mid_odds < odds_float:
                        low = mid + 1
                    else:
                        high = mid - 1

                # 找到插入位置，然后检查相邻的点
                if not closest_entry:
                    insert_pos = low

                    # 检查插入位置及其相邻点
                    candidates = []
                    if insert_pos > 0:
                        candidates.append(sorted_data[insert_pos - 1])
                    if insert_pos < len(sorted_data):
                        candidates.append(sorted_data[insert_pos if insert_pos < len(sorted_data) else insert_pos - 1])

                    # 找最接近的
                    for entry in candidates:
                        entry_odds = float(entry.get('odds', float('inf')))
                        diff = abs(entry_odds - odds_float)
                        if diff < min_diff:
                            min_diff = diff
                            closest_entry = entry

            if closest_entry:
                # 返回对应的档距
                result = closest_entry.get('gap')
                # 确保返回的档距不为None
                if result is None:
                    return "0"  # 返回默认值
                return result
            else:
                return "0"  # 返回默认值

        except Exception:
            return "0"  # 出错时返回默认值

    # 平赔分析相关方法
    def find_standard_draw_odds(self, home_win_odds, away_win_odds):
        """
        根据胜负中较小的赔率值，找到对应的标准平赔

        Args:
            home_win_odds: 主胜满水赔率
            away_win_odds: 客胜满水赔率

        Returns:
            float: 标准平赔
        """
        if not home_win_odds or not away_win_odds:
            return None

        try:
            # 转换为浮点数
            home_float = float(home_win_odds)
            away_float = float(away_win_odds)

            # 获取较小的赔率
            min_odds = min(home_float, away_float)
            min_type = "win" if min_odds == home_float else "loss"

            logging.debug(f"使用{min_type}赔率 {min_odds} 查找标准平赔")

            # 在标准赔率表中查找最接近的赔率
            closest_entry = None
            min_diff = float('inf')

            for entry in self.standard_odds_data:
                if entry.get('type') == min_type and 'odds' in entry:
                    entry_odds = float(entry.get('odds', 0))
                    diff = abs(entry_odds - min_odds)
                    if diff < min_diff:
                        min_diff = diff
                        closest_entry = entry

            if closest_entry:
                # 查找对应的标准平赔
                standard_draw = None
                draw_entries = [e for e in self.standard_odds_data if e.get('type') == 'draw']

                if 'gap' in closest_entry:
                    closest_gap = closest_entry.get('gap')

                    # 找到对应相同档距的平赔
                    for draw_entry in draw_entries:
                        if draw_entry.get('gap') == closest_gap:
                            standard_draw = draw_entry.get('odds')
                            break

                if standard_draw:
                    return float(standard_draw)
                elif draw_entries:
                    # 如果找不到对应档距的平赔，取平均值
                    draw_odds = [float(e.get('odds', 0)) for e in draw_entries if 'odds' in e]
                    if draw_odds:
                        return sum(draw_odds) / len(draw_odds)

            # 如果在标准赔率表中找不到，使用默认值
            return 3.325  # 默认标准平赔

        except (ValueError, TypeError) as e:
            logging.error(f"查找标准平赔时出错: {str(e)}")
            return None

    def calculate_draw_odds_intervals(self, standard_draw_odds):
        """
        根据标准平赔计算平赔区间

        Args:
            standard_draw_odds: 标准平赔

        Returns:
            dict: 平赔区间范围
        """
        if not standard_draw_odds:
            return {}

        try:
            # 计算各个系数对应的边界点
            coefficients = [0.01, 0.05, 0.10, 0.16, 0.23]
            boundaries = {}
            for coef in coefficients:
                boundaries[f'lower_{coef}'] = standard_draw_odds * (1 - coef)
                boundaries[f'upper_{coef}'] = standard_draw_odds * (1 + coef)

            # 构建区间
            intervals = {}
            intervals["超深平"] = (0, boundaries['lower_0.23'])
            intervals["超散平"] = (boundaries['lower_0.23'], boundaries['lower_0.16'])
            intervals["超实平"] = (boundaries['lower_0.16'], boundaries['lower_0.1'])
            intervals["低开平"] = (boundaries['lower_0.1'], boundaries['lower_0.05'])
            intervals["平实低"] = (boundaries['lower_0.05'], boundaries['lower_0.01'])
            intervals["平中庸下"] = (boundaries['lower_0.01'], standard_draw_odds)
            intervals["平中庸上"] = (standard_draw_odds, boundaries['upper_0.01'])
            intervals["平实高"] = (boundaries['upper_0.01'], boundaries['upper_0.05'])
            intervals["高开平"] = (boundaries['upper_0.05'], boundaries['upper_0.1'])
            intervals["韬光平"] = (boundaries['upper_0.1'], boundaries['upper_0.16'])
            intervals["超韬平"] = (boundaries['upper_0.16'], float('inf'))

            return intervals

        except Exception as e:
            logging.error(f"计算平赔区间时出错: {str(e)}")
            return {}

    def determine_draw_interval(self, actual_draw_odds, intervals):
        """
        确定实际平赔落在哪个区间

        Args:
            actual_draw_odds: 实际平赔
            intervals: 平赔区间

        Returns:
            str: 所在区间名称
        """
        if not actual_draw_odds or not intervals:
            return "未知区间"

        try:
            draw_float = float(actual_draw_odds)

            for name, (lower, upper) in intervals.items():
                if lower <= draw_float < upper:
                    return name

            return "超出区间范围"

        except (ValueError, TypeError) as e:
            logging.error(f"判断平赔区间时出错: {str(e)}")
            return "未知区间"