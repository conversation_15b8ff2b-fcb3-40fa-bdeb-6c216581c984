import logging
from football_analysis_system.db.database_manager import DatabaseManager

class IntervalAnalyzer:
    """区间分析类"""

    def __init__(self, strength_matchup_db_path):
        """
        初始化区间分析器

        Args:
            strength_matchup_db_path: 实力对阵表数据库路径
        """
        self.db_manager = DatabaseManager(strength_matchup_db_path)
        self.strength_matchup_table = {}
        self.load_strength_matchup_table()

    def load_strength_matchup_table(self):
        """从数据库加载实力对阵表"""
        try:
            # 获取表名
            tables = self.db_manager.get_table_names()
            if not tables:
                logging.warning(f"在数据库中未找到任何表")
                return

            # 预期表名应为 "match_odds" 或类似名称
            target_table = None
            for table in tables:
                if "match" in table.lower() or "odds" in table.lower() or "实力" in table or "对阵" in table:
                    target_table = table
                    break

            if not target_table and tables:
                # 如果没有找到预期表名但有其他表，使用第一个表
                target_table = tables[0]
                logging.info(f"未找到预期的实力对阵表名，使用第一个表: {target_table}")

            if not target_table:
                logging.warning(f"在数据库中未找到有效的表")
                return

            # 获取表结构
            columns = self.db_manager.get_column_names(target_table)
            logging.debug(f"表 {target_table} 的列名: {columns}")

            # 查询所有数据
            all_rows = self.db_manager.execute_query(f"SELECT * FROM {target_table}")
            logging.info(f"加载了 {len(all_rows)} 行数据")

            if not all_rows:
                logging.warning("未找到任何实力对阵数据")
                return

            # 从第一行数据推断结构 - 通常第一行表示列头
            # 检查第一列是否包含"主队"或类似字样
            first_row_has_header = False
            if all_rows[0][0] and ("主" in str(all_rows[0][0]) or "home" in str(all_rows[0][0]).lower()):
                first_row_has_header = True
                logging.info("第一行可能是标题行，将跳过")

            # 确定客队实力的列映射
            away_team_levels = []
            if first_row_has_header and len(all_rows) > 0 and len(all_rows[0]) > 1:
                # 如果第一行是标题，用它来确定客队实力级别
                away_team_levels = [str(col).strip() if col else f"列{i+1}" for i, col in enumerate(all_rows[0][1:])]
                # 跳过第一行处理数据
                data_rows = all_rows[1:]
            else:
                # 直接使用列名
                away_team_levels = [col for col in columns[1:]]
                data_rows = all_rows

            logging.debug(f"识别到的客队实力级别: {away_team_levels}")

            # 重新构建实力对阵表 - 使用(主队实力,客队实力)作为键，对阵区间作为值
            self.strength_matchup_table = {}

            for row in data_rows:
                if not row[0]:  # 跳过主队实力为空的行
                    continue

                home_level = str(row[0]).strip()

                # 对每个客队实力级别，创建映射
                for i, away_level in enumerate(away_team_levels):
                    if i+1 < len(row):  # 确保索引有效
                        interval = str(row[i+1]).strip() if row[i+1] else "未知区间"
                        self.strength_matchup_table[(home_level, away_level)] = interval

            logging.info(f"成功加载 {len(self.strength_matchup_table)} 组实力对阵区间数据")

            # 打印部分匹配示例以验证
            sample_keys = list(self.strength_matchup_table.keys())[:3]
            for key in sample_keys:
                logging.debug(f"示例匹配: {key[0]} vs {key[1]} => {self.strength_matchup_table[key]}")

        except Exception as e:
            logging.error(f"加载实力对阵表时出错: {e}")

    def get_strength_matchup(self, home_power, away_power):
        """
        获取主客队实力对应的对阵区间

        Args:
            home_power: 主队广义实力
            away_power: 客队广义实力

        Returns:
            str: 对阵区间描述
        """
        if not home_power or not away_power:
            return "未知区间"

        # 尝试直接匹配
        key = (home_power, away_power)
        if key in self.strength_matchup_table:
            return self.strength_matchup_table[key]

        # 如果没有直接匹配，尝试在表中找到客队实力列
        available_away_powers = set(key[1] for key in self.strength_matchup_table.keys())
        logging.debug(f"可用的客队实力: {available_away_powers}")

        # 检查客队列标题是否包含away_power
        for away_power_key in available_away_powers:
            if away_power in away_power_key or away_power_key in away_power:
                # 尝试使用这个客队实力列
                new_key = (home_power, away_power_key)
                if new_key in self.strength_matchup_table:
                    logging.info(f"找到近似匹配: {home_power} vs {away_power} => 使用 {home_power} vs {away_power_key}")
                    return self.strength_matchup_table[new_key]

        # 如果还是没找到，输出调试信息
        logging.warning(f"未找到实力对阵: {home_power} vs {away_power}")
        return "未找到对应区间"

    def find_interval_for_gap(self, gap_value, gap_difference, home_team=True):
        """
        根据距值和档距D计算区间范围和规则号

        Args:
            gap_value: 从赔率表查到的距值
            gap_difference: 档距D（主队评分 - 客队评分）
            home_team: 是否为主队 (True) 或客队 (False)

        Returns:
            dict: 包含区间和规则号的字典
        """
        if gap_value is None or gap_difference is None:
            return {"low": None, "high": None, "rule_value": None, "interval_type": "未知"}

        try:
            gap_float = float(gap_value)      # 这是从赔率表查到的距值
            d_float = float(gap_difference)    # 这是档距D

            # 计算区间值 = 对应档距 - 档距差
            interval_value = round(gap_float - d_float, 1)
            logging.debug(f"计算区间值: 档距{gap_float} - 档距差{d_float} = {interval_value}")

            # 定义区间规则
            interval_rules = {
                "超韬利盘": {
                    "rule_value": 5.5,  # 规则值
                    "home_offset_low": -999,  # 无下限，用一个很小的数表示
                    "home_offset_high": -3.1,
                    "away_offset_low": 4.0,
                    "away_offset_high": 999  # 无上限，用一个很大的数表示
                },
                "超韬盘中上水": {
                    "rule_value": 5,  # 规则值
                    "home_offset_low": -3.0,
                    "home_offset_high": -2.6,
                    "away_offset_low": 3.5,
                    "away_offset_high": 3.9
                },
                "超韬盘中下水": {
                    "rule_value": 4.5,  # 规则值
                    "home_offset_low": -2.5,
                    "home_offset_high": -2.1,
                    "away_offset_low": 3.0,
                    "away_offset_high": 3.4
                },
                "韬光盘高水": {
                    "rule_value": 4,  # 规则值
                    "home_offset_low": -2.0,
                    "home_offset_high": -1.9,
                    "away_offset_low": 2.8,
                    "away_offset_high": 2.9
                },
                "韬光盘中高水": {
                    "rule_value": 3.5,  # 规则值
                    "home_offset_low": -1.8,
                    "home_offset_high": -1.7,
                    "away_offset_low": 2.6,
                    "away_offset_high": 2.7
                },
                "韬光盘中水": {
                    "rule_value": 3,  # 规则值
                    "home_offset_low": -1.6,
                    "home_offset_high": -1.5,
                    "away_offset_low": 2.4,
                    "away_offset_high": 2.5
                },
                "韬光盘中低水": {
                    "rule_value": 2.5,  # 规则值
                    "home_offset_low": -1.4,
                    "home_offset_high": -1.3,
                    "away_offset_low": 2.2,
                    "away_offset_high": 2.3
                },
                "韬光盘低水": {
                    "rule_value": 2,  # 规则值
                    "home_offset_low": -1.2,
                    "home_offset_high": -1.1,
                    "away_offset_low": 2.0,
                    "away_offset_high": 2.1
                },
                "韬光盘超低水": {
                    "rule_value": 3,  # 规则值
                    "home_offset_low": -1.0,
                    "home_offset_high": -0.9,
                    "away_offset_low": 1.8,
                    "away_offset_high": 1.9
                },
                "高开盘中高水": {
                    "rule_value": 4,  # 规则值
                    "home_offset_low": -0.8,
                    "home_offset_high": -0.7,
                    "away_offset_low": 1.6,
                    "away_offset_high": 1.7
                },
                "高开盘中水": {
                    "rule_value": 4.5,  # 规则值
                    "home_offset_low": -0.6,
                    "home_offset_high": -0.5,
                    "away_offset_low": 1.4,
                    "away_offset_high": 1.5
                },
                "高开盘中低水": {
                    "rule_value": 4,  # 规则值
                    "home_offset_low": -0.4,
                    "home_offset_high": -0.3,
                    "away_offset_low": 1.2,
                    "away_offset_high": 1.3
                },
                "实开盘超高水": {
                    "rule_value": 3.5,  # 规则值
                    "home_offset_low": -0.2,
                    "home_offset_high": -0.1,
                    "away_offset_low": 1.0,
                    "away_offset_high": 1.1
                },
                "实开盘高水": {
                    "rule_value": 4,  # 规则值
                    "home_offset_low": 0.0,
                    "home_offset_high": 0.1,
                    "away_offset_low": 0.8,
                    "away_offset_high": 0.9
                },
                "实开盘中高水": {
                    "rule_value": 3.5,  # 规则值
                    "home_offset_low": 0.2,
                    "home_offset_high": 0.3,
                    "away_offset_low": 0.6,
                    "away_offset_high": 0.7
                },
                "实开盘中水": {
                    "rule_value": 3,  # 规则值
                    "home_offset_low": 0.4,
                    "home_offset_high": 0.5,
                    "away_offset_low": 0.4,
                    "away_offset_high": 0.5
                },
                "实开盘中低水": {
                    "rule_value": 2.5,  # 规则值
                    "home_offset_low": 0.6,
                    "home_offset_high": 0.7,
                    "away_offset_low": 0.2,
                    "away_offset_high": 0.3
                },
                "实开盘低水": {
                    "rule_value": 2,  # 规则值
                    "home_offset_low": 0.8,
                    "home_offset_high": 0.9,
                    "away_offset_low": 0.0,
                    "away_offset_high": 0.1
                },
                "实开盘超低水": {
                    "rule_value": 3,  # 规则值
                    "home_offset_low": 1.0,
                    "home_offset_high": 1.1,
                    "away_offset_low": -0.2,
                    "away_offset_high": -0.1
                },
                "低开盘中高水": {
                    "rule_value": 3.5,  # 规则值
                    "home_offset_low": 1.2,
                    "home_offset_high": 1.3,
                    "away_offset_low": -0.4,
                    "away_offset_high": -0.3
                },
                "低开盘中水": {
                    "rule_value": 4,  # 规则值
                    "home_offset_low": 1.4,
                    "home_offset_high": 1.5,
                    "away_offset_low": -0.6,
                    "away_offset_high": -0.5
                },
                "低开盘中低水": {
                    "rule_value": 3,  # 规则值
                    "home_offset_low": 1.6,
                    "home_offset_high": 1.7,
                    "away_offset_low": -0.8,
                    "away_offset_high": -0.7
                },
                "超实盘超高水": {
                    "rule_value": 2.5,  # 规则值
                    "home_offset_low": 1.8,
                    "home_offset_high": 1.9,
                    "away_offset_low": -1.0,
                    "away_offset_high": -0.9
                },
                "超实盘高水": {
                    "rule_value": 0.5,  # 规则值
                    "home_offset_low": 2.0,
                    "home_offset_high": 2.1,
                    "away_offset_low": -1.2,
                    "away_offset_high": -1.1
                },
                "超实盘中高水": {
                    "rule_value": 1,  # 规则值
                    "home_offset_low": 2.2,
                    "home_offset_high": 2.3,
                    "away_offset_low": -1.4,
                    "away_offset_high": -1.3
                },
                "超实盘中水": {
                    "rule_value": 1.5,  # 规则值
                    "home_offset_low": 2.4,
                    "home_offset_high": 2.5,
                    "away_offset_low": -1.6,
                    "away_offset_high": -1.5
                },
                "超实盘中低水": {
                    "rule_value": 2,  # 规则值
                    "home_offset_low": 2.6,
                    "home_offset_high": 2.7,
                    "away_offset_low": -1.8,
                    "away_offset_high": -1.7
                },
                "超实盘低水": {
                    "rule_value": 2.5,  # 规则值
                    "home_offset_low": 2.8,
                    "home_offset_high": 2.9,
                    "away_offset_low": -2.0,
                    "away_offset_high": -1.9
                },
                "超实盘超低水": {
                    "rule_value": 3,  # 规则值
                    "home_offset_low": 3.0,
                    "home_offset_high": 3.1,
                    "away_offset_low": -2.2,
                    "away_offset_high": -2.1
                },
                "超散盘中高水": {
                    "rule_value": 3.5,  # 规则值
                    "home_offset_low": 3.2,
                    "home_offset_high": 3.3,
                    "away_offset_low": -2.4,
                    "away_offset_high": -2.3
                },
                "超散盘中水": {
                    "rule_value": 4,  # 规则值
                    "home_offset_low": 3.4,
                    "home_offset_high": 3.5,
                    "away_offset_low": -2.6,
                    "away_offset_high": -2.5
                },
                "超散盘中低水": {
                    "rule_value": 3.5,  # 规则值
                    "home_offset_low": 3.6,
                    "home_offset_high": 3.7,
                    "away_offset_low": -2.8,
                    "away_offset_high": -2.7
                },
                "超散盘低水": {
                    "rule_value": 3,  # 规则值
                    "home_offset_low": 3.8,
                    "home_offset_high": 3.9,
                    "away_offset_low": -3.0,
                    "away_offset_high": -2.9
                },
                "超深盘中上水": {
                    "rule_value": 2,  # 规则值
                    "home_offset_low": 4.0,
                    "home_offset_high": 4.4,
                    "away_offset_low": -3.5,
                    "away_offset_high": -3.1
                },
                "超深盘中下水": {
                    "rule_value": 3,  # 规则值
                    "home_offset_low": 4.5,
                    "home_offset_high": 4.9,
                    "away_offset_low": -4.0,
                    "away_offset_high": -3.6
                },
                "超深诱盘": {
                    "rule_value": 4,  # 规则值
                    "home_offset_low": 5.0,
                    "home_offset_high": 999,  # 无上限
                    "away_offset_low": -999,  # 无下限
                    "away_offset_high": -4.1
                }
            }

            # 找到匹配的盘口类型
            matched_type = None
            matched_offsets = None

            for odds_type, offsets in interval_rules.items():
                if home_team:
                    low = offsets["home_offset_low"]
                    high = offsets["home_offset_high"]
                else:
                    low = offsets["away_offset_low"]
                    high = offsets["away_offset_high"]

                # 检查区间值是否在区间范围内（增加些微容错空间）
                if low - 0.001 <= interval_value <= high + 0.001:
                    matched_type = odds_type
                    matched_offsets = offsets
                    logging.debug(f"找到匹配区间: {odds_type}, 规则值: {offsets['rule_value']}, 范围: {low} ~ {high}")
                    break

            # 如果没有找到精确匹配，尝试最近邻匹配
            if not matched_type:
                min_distance = float('inf')
                for odds_type, offsets in interval_rules.items():
                    if home_team:
                        low = offsets["home_offset_low"]
                        high = offsets["home_offset_high"]
                        center = (low + high) / 2
                    else:
                        low = offsets["away_offset_low"]
                        high = offsets["away_offset_high"]
                        center = (low + high) / 2

                    # 计算到区间中心的距离
                    distance = abs(interval_value - center)
                    if distance < min_distance and distance < 0.4:  # 限制最大距离
                        min_distance = distance
                        matched_type = odds_type
                        matched_offsets = offsets

                if matched_type:
                    logging.info(f"使用最近邻匹配找到区间: {matched_type}, 规则值: {matched_offsets['rule_value']}, 距离: {min_distance:.2f}")

            if matched_type and matched_offsets:
                return {
                    "low": interval_value,
                    "high": interval_value,
                    "rule_value": matched_offsets["rule_value"],
                    "interval_type": matched_type
                }

            logging.warning(f"未找到匹配区间，区间值={interval_value}")
            return {
                "low": None,
                "high": None,
                "rule_value": None,
                "interval_type": "未知"
            }

        except (ValueError, TypeError) as e:
            logging.error(f"计算区间出错: {str(e)}")
            return {"low": None, "high": None, "rule_value": None, "interval_type": "未知"}

    def get_first_choice(self, rule_diff, gap_diff=None):
        """
        根据规差值和档距差返回首选推荐

        Args:
            rule_diff: 规差值（主队规则值 - 客队规则值）
            gap_diff: 档距差（主队评分 - 客队评分）

        Returns:
            str: 首选推荐
        """
        if not isinstance(rule_diff, (int, float)):
            try:
                rule_diff = float(rule_diff)
            except (ValueError, TypeError):
                return "未知"

        # 检查档距差是否有效
        has_valid_gap_diff = False
        if gap_diff is not None:
            try:
                gap_diff = float(gap_diff)
                has_valid_gap_diff = True
            except (ValueError, TypeError):
                has_valid_gap_diff = False

        # 根据档距差和规值差的组合确定首选值
        # 档距差 >= -1.5 的映射关系
        rule_diff_map_ge_neg1_5 = {
            -5: "主让2胜",
            -4.5: "主让2平",
            -4: "主让2平",
            -3.5: "主让1胜",
            -3: "主让1胜",
            -2.5: "主让1胜",
            -2: "主让1平",
            -1.5: "主让1平",
            -1: "双平偏让平",
            -0.5: "双平",
            0: "双平偏平",
            0.5: "平",
            1: "主让1负",
            1.5: "客让1平",
            2: "客让1平",
            2.5: "客让1胜",
            3: "客让1胜",
            3.5: "客让1胜",
            4: "客让2平",
            4.5: "客让2平",
            5: "客让2胜"
        }

        # 档距差 < -1.5 的映射关系
        rule_diff_map_lt_neg1_5 = {
            -5: "主让2胜",
            -4.5: "主让2平",
            -4: "主让2平",
            -3.5: "主让1胜",
            -3: "主让1胜",
            -2.5: "主让1胜",
            -2: "主让1平",
            -1.5: "主让1平",
            -1: "主受1胜",
            -0.5: "平",
            0: "双平偏平",
            0.5: "双平",
            1: "双平偏让平",
            1.5: "客让1平",
            2: "客让1平",
            2.5: "客让1胜",
            3: "客让1胜",
            3.5: "客让1胜",
            4: "客让2平",
            4.5: "客让2平",
            5: "客让2胜"
        }

        # 基于档距差选择映射表
        if has_valid_gap_diff:
            if gap_diff >= -1.5:
                return rule_diff_map_ge_neg1_5.get(rule_diff, "未知")
            else:
                return rule_diff_map_lt_neg1_5.get(rule_diff, "未知")
        else:
            # 如果没有有效的档距差，默认使用档距差≥-1.5的映射规则
            return rule_diff_map_ge_neg1_5.get(rule_diff, "未知")

    def get_risk_level(self, rule_value):
        """
        根据规则号获取风险等级

        Args:
            rule_value: 规则号

        Returns:
            str: 风险等级 (危险区/高风险区/警示区/安全区)
        """
        try:
            if rule_value is None:
                return "未知区间"

            rule_value = float(rule_value)

            # 按规则号分类风险等级
            if rule_value in [5.5, 5]:
                return "危险区"
            elif rule_value == 4.5:
                return "高风险区"
            elif rule_value in [3, 3.5, 4]:
                return "警示区"
            elif rule_value in [0.5, 1, 1.5, 2, 2.5]:
                return "安全区"
            else:
                return "未知区间"

        except (ValueError, TypeError):
            return "未知区间"