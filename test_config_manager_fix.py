#!/usr/bin/env python3
"""
Test script to verify ConfigManager circular import fix
"""

import sys
import os

# Add the football_analysis_system to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'football_analysis_system'))

def test_config_manager_import():
    """Test that ConfigManager can be imported without circular import errors"""
    try:
        print("Testing ConfigManager import...")
        from football_analysis_system.core.config_manager import ConfigManager
        print("✓ ConfigManager imported successfully")
        
        # Test initialization
        print("Testing ConfigManager initialization...")
        config = ConfigManager()
        print("✓ ConfigManager initialized successfully")
        
        # Test directory creation
        print("Testing directory paths...")
        data_dir = config.get('paths.data_dir')
        logs_dir = config.get('paths.logs_dir')
        temp_dir = config.get('paths.temp_dir')
        backup_dir = config.get('paths.backup_dir')
        
        print(f"Data directory: {data_dir}")
        print(f"Logs directory: {logs_dir}")
        print(f"Temp directory: {temp_dir}")
        print(f"Backup directory: {backup_dir}")
        
        # Test path resolution without PathResolver dependency
        print("Testing path resolution...")
        test_path = config.resolve_database_path("matches_and_odds.db")
        print(f"Resolved database path: {test_path}")
        
        # Test relative path resolution
        relative_path = config.resolve_database_path("data/test.db")
        print(f"Resolved relative path: {relative_path}")
        
        print("✓ All ConfigManager tests passed!")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        return False

def test_path_resolver_import():
    """Test that PathResolver can still be imported after ConfigManager"""
    try:
        print("\nTesting PathResolver import after ConfigManager...")
        from football_analysis_system.core.path_resolver import PathResolver
        print("✓ PathResolver imported successfully")
        
        # Test PathResolver functionality
        print("Testing PathResolver functionality...")
        db_path = PathResolver.get_database_path("test.db")
        print(f"PathResolver database path: {db_path}")
        
        print("✓ PathResolver tests passed!")
        return True
        
    except ImportError as e:
        print(f"✗ PathResolver import error: {e}")
        return False
    except Exception as e:
        print(f"✗ PathResolver error: {e}")
        return False

if __name__ == "__main__":
    print("=== Testing ConfigManager Circular Import Fix ===\n")
    
    success1 = test_config_manager_import()
    success2 = test_path_resolver_import()
    
    if success1 and success2:
        print("\n🎉 All tests passed! Circular import issue has been resolved.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)