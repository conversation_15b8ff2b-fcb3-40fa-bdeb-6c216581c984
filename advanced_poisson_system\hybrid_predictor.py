"""
融合预测器 - 智能结合历史分析和市场分析的高级λ参数估算器
实现论文级别的动态权重分配和置信度评估
"""

import numpy as np
from scipy.stats import poisson
from typing import Dict, List, Optional, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

class HybridPredictor:
    """融合预测器 - 结合多种方法的智能λ参数估算"""
    
    def __init__(self, historical_analyzer, market_analyzer):
        """
        初始化融合预测器
        
        Args:
            historical_analyzer: 历史分析器实例
            market_analyzer: 市场分析器实例
        """
        self.historical_analyzer = historical_analyzer
        self.market_analyzer = market_analyzer
        
        # 权重配置
        self.weight_strategies = {
            'conservative': {  # 保守策略 - 更依赖历史数据
                'historical_base': 0.7,
                'market_base': 0.3
            },
            'balanced': {     # 平衡策略 - 均衡权重
                'historical_base': 0.5,
                'market_base': 0.5
            },
            'aggressive': {   # 激进策略 - 更依赖市场数据
                'historical_base': 0.3,
                'market_base': 0.7
            }
        }
        
        self.default_strategy = 'balanced'
    
    def predict_match_lambdas(self, match_id, home_team, away_team, 
                            strategy='auto', line=2.5):
        """
        综合预测比赛的λ参数
        
        Args:
            match_id: 比赛ID
            home_team: 主队名称
            away_team: 客队名称
            strategy: 融合策略 ('auto', 'conservative', 'balanced', 'aggressive')
            line: 大小球盘口线
            
        Returns:
            dict: 综合预测结果
        """
        try:
            print(f"🔄 开始融合预测: {home_team} vs {away_team}, 盘口: {line}")
            
            # 1. 历史分析
            historical_result = self.historical_analyzer.calculate_historical_lambdas(
                home_team, away_team
            )
            
            # 2. 获取市场数据（带智能回退）
            overunder_data, actual_line = self.historical_analyzer.data_manager.get_overunder_odds_with_fallback(match_id, line)
            odds_1x2_data = self.historical_analyzer.data_manager.get_1x2_odds(match_id)
            
            # 更新使用的实际盘口
            if actual_line != line:
                print(f"💡 盘口调整: 请求 {line} -> 实际使用 {actual_line}")
                line = actual_line
            
            # 3. 市场分析 - 确保不返回None
            market_result = self.market_analyzer.comprehensive_market_analysis(
                overunder_data, odds_1x2_data, line
            )
            
            # 安全检查市场分析结果
            if market_result is None:
                print("⚠️ 市场分析返回None，使用默认结果")
                market_result = self._get_default_market_result(line)
            elif not isinstance(market_result, dict):
                print("⚠️ 市场分析结果格式异常，使用默认结果")
                market_result = self._get_default_market_result(line)
            
            # 确保必要字段存在
            if 'lambda_estimates' not in market_result:
                market_result['lambda_estimates'] = {'home': 1.4, 'away': 1.1, 'total': 2.5}
            if 'success' not in market_result:
                market_result['success'] = False
            
            print(f"📊 市场分析结果: {'成功' if market_result.get('success') else '失败'}")
            print(f"📊 获得λ估值: home={market_result['lambda_estimates']['home']:.2f}, away={market_result['lambda_estimates']['away']:.2f}")
            
            # 4. 智能权重分配
            weights = self._calculate_dynamic_weights(
                historical_result, market_result, strategy
            )
            
            print(f"⚖️ 权重分配: 历史={weights['historical']:.2f}, 市场={weights['market']:.2f}")
            
            # 5. 融合λ参数
            final_lambdas = self._fuse_lambda_estimates(
                historical_result, market_result, weights
            )
            
            # 6. 综合评估
            comprehensive_assessment = self._comprehensive_assessment(
                historical_result, market_result, weights, final_lambdas
            )
            
            print(f"🎯 最终λ值: home={final_lambdas['home']:.2f}, away={final_lambdas['away']:.2f}, total={final_lambdas['total']:.2f}")
            
            return {
                'success': True,
                'match_info': {
                    'match_id': match_id,
                    'home_team': home_team,
                    'away_team': away_team,
                    'line': line
                },
                'lambda_estimates': final_lambdas,
                'method_comparison': {
                    'historical': historical_result.get('lambda_estimates', {'home': 1.4, 'away': 1.1, 'total': 2.5}),
                    'market': market_result.get('lambda_estimates', {'home': 1.4, 'away': 1.1, 'total': 2.5})
                },
                'fusion_weights': weights,
                'comprehensive_assessment': comprehensive_assessment,
                'component_results': {
                    'historical_analysis': historical_result,
                    'market_analysis': market_result
                }
            }
            
        except Exception as e:
            print(f"融合预测失败: {e}")
            import traceback
            traceback.print_exc()
            return self._get_fallback_result(home_team, away_team, line)
    
    def _calculate_dynamic_weights(self, historical_result, market_result, strategy):
        """
        计算动态权重
        """
        if strategy == 'auto':
            # 自动策略 - 基于数据质量动态调整
            hist_quality = historical_result['data_quality']['overall_score']
            market_quality = self._assess_market_quality(market_result)
            
            # 基于质量的权重调整
            total_quality = hist_quality + market_quality
            if total_quality > 0:
                hist_weight = hist_quality / total_quality
                market_weight = market_quality / total_quality
            else:
                hist_weight = 0.5
                market_weight = 0.5
                
            # 置信度调整
            hist_confidence = self._confidence_to_factor(historical_result['confidence_level'])
            market_confidence = self._market_confidence_factor(market_result)
            
            # 最终权重（结合质量和置信度）
            hist_weight = hist_weight * 0.7 + hist_confidence * 0.3
            market_weight = market_weight * 0.7 + market_confidence * 0.3
            
            # 标准化
            total_weight = hist_weight + market_weight
            if total_weight > 0:
                hist_weight /= total_weight
                market_weight /= total_weight
            
        else:
            # 使用预设策略
            strategy_config = self.weight_strategies.get(strategy, self.weight_strategies[self.default_strategy])
            hist_weight = strategy_config['historical_base']
            market_weight = strategy_config['market_base']
        
        return {
            'historical': hist_weight,
            'market': market_weight,
            'strategy_used': strategy,
            'auto_adjustment': strategy == 'auto'
        }
    
    def _assess_market_quality(self, market_result):
        """
        评估市场数据质量
        """
        # 安全获取model_validation，如果不存在则使用默认值
        model_validation = market_result.get('model_validation', {})
        data_quality = model_validation.get('data_quality', {})
        
        # 基础分数
        base_score = 0.0
        
        # 大小球赔率可用性
        if data_quality.get('overunder_available', False):
            base_score += 0.4
            
        # 胜平负赔率可用性
        if data_quality.get('1x2_available', False):
            base_score += 0.4
            
        # 庄家利润率评估
        overround_ou = abs(data_quality.get('overround_ou', 0))
        overround_1x2 = abs(data_quality.get('overround_1x2', 0))
        
        # 利润率越低，质量越高
        margin_penalty = min((overround_ou + overround_1x2) / 2, 0.2)
        
        # 拟合质量
        fit_quality = model_validation.get('fit_quality', 0.5)
        
        final_score = base_score - margin_penalty + fit_quality * 0.2
        return max(0.0, min(1.0, final_score))
    
    def _confidence_to_factor(self, confidence_level):
        """
        将置信度转换为权重因子
        """
        confidence_map = {
            'very_low': 0.1,
            'low': 0.4,
            'medium': 0.7,
            'high': 1.0
        }
        return confidence_map.get(confidence_level, 0.5)
    
    def _market_confidence_factor(self, market_result):
        """
        市场数据的置信度因子
        """
        base_confidence = 0.5
        
        # 安全获取分析结果
        overunder_analysis = market_result.get('overunder_analysis', {})
        odds_1x2_analysis = market_result.get('odds_1x2_analysis', {})
        model_validation = market_result.get('model_validation', {})
        
        # 数据可用性加分
        if overunder_analysis.get('success', False):
            base_confidence += 0.2
        if odds_1x2_analysis.get('success', False):
            base_confidence += 0.2
            
        # 拟合质量加分
        fit_quality = model_validation.get('fit_quality', 0.5)
        base_confidence += fit_quality * 0.1
        
        return min(1.0, base_confidence)
    
    def _fuse_lambda_estimates(self, historical_result, market_result, weights):
        """
        融合λ参数估算
        """
        hist_lambdas = historical_result['lambda_estimates']
        market_lambdas = market_result['lambda_estimates']
        
        # 加权融合
        fused_home = (hist_lambdas['home'] * weights['historical'] + 
                     market_lambdas['home'] * weights['market'])
        fused_away = (hist_lambdas['away'] * weights['historical'] + 
                     market_lambdas['away'] * weights['market'])
        fused_total = fused_home + fused_away
        
        # 计算一致性
        consistency = self._calculate_method_consistency(hist_lambdas, market_lambdas)
        
        return {
            'home': fused_home,
            'away': fused_away,
            'total': fused_total,
            'method_consistency': consistency,
            'fusion_method': 'weighted_average'
        }
    
    def _calculate_method_consistency(self, hist_lambdas, market_lambdas):
        """
        计算方法间一致性
        """
        # 计算相对差异
        home_diff = abs(hist_lambdas['home'] - market_lambdas['home']) / max(hist_lambdas['home'], market_lambdas['home'])
        away_diff = abs(hist_lambdas['away'] - market_lambdas['away']) / max(hist_lambdas['away'], market_lambdas['away'])
        total_diff = abs(hist_lambdas['total'] - market_lambdas['total']) / max(hist_lambdas['total'], market_lambdas['total'])
        
        # 一致性分数（差异越小，一致性越高）
        avg_diff = (home_diff + away_diff + total_diff) / 3
        consistency_score = max(0, 1 - avg_diff)
        
        return {
            'score': consistency_score,
            'home_difference': home_diff,
            'away_difference': away_diff,
            'total_difference': total_diff,
            'assessment': self._consistency_assessment(consistency_score)
        }
    
    def _consistency_assessment(self, score):
        """
        一致性评估
        """
        if score >= 0.8:
            return 'very_high'
        elif score >= 0.6:
            return 'high'
        elif score >= 0.4:
            return 'medium'
        elif score >= 0.2:
            return 'low'
        else:
            return 'very_low'
    
    def _comprehensive_assessment(self, historical_result, market_result, weights, final_lambdas):
        """
        综合评估
        """
        # 安全获取分析结果
        overunder_analysis = market_result.get('overunder_analysis', {})
        odds_1x2_analysis = market_result.get('odds_1x2_analysis', {})
        
        # 数据可用性评分
        data_availability = {
            'historical_data': historical_result['success'],
            'market_data': overunder_analysis.get('success', False) and odds_1x2_analysis.get('success', False),
            'both_available': historical_result['success'] and overunder_analysis.get('success', False)
        }
        
        # 预测置信度
        hist_confidence = self._confidence_to_factor(historical_result['confidence_level'])
        market_confidence = self._market_confidence_factor(market_result)
        
        overall_confidence = (hist_confidence * weights['historical'] + 
                            market_confidence * weights['market'])
        
        # 风险评估
        risk_factors = []
        if not data_availability['both_available']:
            risk_factors.append('single_method_dependency')
        if final_lambdas['method_consistency']['score'] < 0.5:
            risk_factors.append('low_method_consistency')
        if overall_confidence < 0.6:
            risk_factors.append('low_overall_confidence')
        
        return {
            'overall_confidence': overall_confidence,
            'confidence_level': self._score_to_confidence_level(overall_confidence),
            'data_availability': data_availability,
            'risk_factors': risk_factors,
            'recommendation': self._generate_recommendation(overall_confidence, risk_factors)
        }
    
    def _score_to_confidence_level(self, score):
        """
        分数转置信度等级
        """
        if score >= 0.8:
            return 'very_high'
        elif score >= 0.6:
            return 'high'
        elif score >= 0.4:
            return 'medium'
        elif score >= 0.2:
            return 'low'
        else:
            return 'very_low'
    
    def _generate_recommendation(self, confidence, risk_factors):
        """
        生成使用建议
        """
        if confidence >= 0.8 and not risk_factors:
            return 'high_confidence_prediction'
        elif confidence >= 0.6:
            return 'reliable_for_analysis'
        elif confidence >= 0.4:
            return 'use_with_caution'
        else:
            return 'insufficient_data_quality'
    
    def _get_fallback_result(self, home_team, away_team, line=2.5):
        """
        获取备用结果
        """
        return {
            'success': False,
            'error': '融合预测失败，使用默认值',
            'lambda_estimates': {
                'home': line * 0.55,
                'away': line * 0.45,
                'total': line
            },
            'comprehensive_assessment': {
                'overall_confidence': 0.1,
                'confidence_level': 'very_low',
                'recommendation': 'insufficient_data'
            }
        }
    
    def _get_default_market_result(self, line=2.5):
        """
        获取默认的市场分析结果
        
        Args:
            line: 盘口线
            
        Returns:
            dict: 默认市场分析结果
        """
        return {
            'success': False,
            'lambda_estimates': {
                'home': line * 0.55,
                'away': line * 0.45,
                'total': line
            },
            'overunder_analysis': {'success': False},
            'odds_1x2_analysis': {'success': False},
            'model_validation': {
                'data_quality': {
                    'overunder_available': False,
                    '1x2_available': False,
                    'overround_ou': 0,
                    'overround_1x2': 0
                },
                'fit_quality': 0.3
            },
            'warnings': ['使用默认市场分析结果']
        }

if __name__ == "__main__":
    # 测试融合预测器
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    
    from data_manager import DataManager
    from market_analyzer import MarketAnalyzer
    from historical_analyzer import HistoricalAnalyzer
    
    print("=== 融合预测器测试 ===")
    
    # 初始化
    dm = DataManager()
    ma = MarketAnalyzer()
    ha = HistoricalAnalyzer(dm)
    hp = HybridPredictor(ha, ma)
    
    # 测试比赛
    match_id = 2702969
    home_team = "哥德堡盖斯"
    away_team = "天狼星"
    
    # 进行融合预测
    result = hp.predict_match_lambdas(match_id, home_team, away_team, strategy='auto')
    
    print(f"预测成功: {result['success']}")
    print(f"最终λ估算: {result['lambda_estimates']}")
    print(f"方法比较:")
    print(f"  - 历史方法: {result['method_comparison']['historical']}")
    print(f"  - 市场方法: {result['method_comparison']['market']}")
    print(f"融合权重: {result['fusion_weights']}")
    print(f"一致性: {result['lambda_estimates']['method_consistency']['assessment']}")
    print(f"综合置信度: {result['comprehensive_assessment']['confidence_level']}")
    print(f"建议: {result['comprehensive_assessment']['recommendation']}") 