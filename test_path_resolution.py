#!/usr/bin/env python3
"""
测试路径解析功能
验证ConfigManager能否正确解析相对路径
"""

import sys
import os
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 确保能找到football_analysis_system模块
football_system_path = project_root / "football_analysis_system"
if football_system_path.exists():
    sys.path.insert(0, str(football_system_path))

def test_path_resolution():
    """测试路径解析功能"""
    print("=" * 60)
    print("测试路径解析功能")
    print("=" * 60)
    
    try:
        # 导入配置管理器
        from football_analysis_system.core.config_manager import config_manager
        
        print(f"当前工作目录: {os.getcwd()}")
        print(f"项目根目录: {config_manager.project_root}")
        print()
        
        # 测试各种路径解析
        test_paths = [
            "logs",
            "data", 
            "temp",
            "backup",
            "./logs",
            "../logs",
            "football_analysis_system/data"
        ]
        
        print("测试路径解析:")
        print("-" * 40)
        for path in test_paths:
            resolved = config_manager.resolve_path(path)
            exists = resolved.exists()
            print(f"原始路径: {path}")
            print(f"解析路径: {resolved}")
            print(f"路径存在: {exists}")
            print()
        
        # 测试配置路径获取
        print("测试配置路径获取:")
        print("-" * 40)
        
        # 日志路径
        log_path = config_manager.get_log_file_path('test')
        print(f"日志文件路径: {log_path}")
        
        # 数据库路径
        db_path = config_manager.get_database_path('matches')
        print(f"数据库路径: {db_path}")
        
        # 临时文件路径
        temp_path = config_manager.get_temp_file_path('test.tmp')
        print(f"临时文件路径: {temp_path}")
        
        print()
        print("测试创建目录:")
        print("-" * 40)
        
        # 测试创建目录
        test_dirs = ['logs', 'data', 'temp', 'backup']
        for dir_name in test_dirs:
            dir_path = config_manager.resolve_path(dir_name)
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"✓ 成功创建目录: {dir_path}")
            except Exception as e:
                print(f"✗ 创建目录失败: {dir_path} - {e}")
        
        print()
        print("✓ 路径解析功能测试完成!")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logger_creation():
    """测试日志记录器创建"""
    print("\n" + "=" * 60)
    print("测试日志记录器创建")
    print("=" * 60)
    
    try:
        from football_analysis_system.core.logger_manager import logger_manager
        
        # 获取不同类型的日志记录器
        loggers = {
            'app': logger_manager.get_logger('app'),
            'crawler': logger_manager.get_crawler_logger(),
            'analysis': logger_manager.get_analysis_logger(),
            'database': logger_manager.get_database_logger()
        }
        
        print("测试日志记录器:")
        print("-" * 40)
        
        for name, logger in loggers.items():
            try:
                logger.info(f"测试 {name} 日志记录器")
                print(f"✓ {name} 日志记录器工作正常")
            except Exception as e:
                print(f"✗ {name} 日志记录器失败: {e}")
        
        # 获取日志统计信息
        stats = logger_manager.get_log_stats()
        print(f"\n日志统计信息:")
        print(f"活跃日志记录器: {stats['active_loggers']}")
        print(f"活跃处理器: {stats['active_handlers']}")
        print(f"UI队列大小: {stats['ui_queue_size']}")
        print(f"日志级别: {stats['log_level']}")
        print(f"日志文件数量: {len(stats['log_files'])}")
        
        print("\n✓ 日志记录器测试完成!")
        return True
        
    except Exception as e:
        print(f"✗ 日志记录器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试路径解析和日志功能...")
    
    success1 = test_path_resolution()
    success2 = test_logger_creation()
    
    if success1 and success2:
        print("\n🎉 所有测试通过!")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败!")
        sys.exit(1)
