"""
统一日志管理器
提供项目范围内的统一日志配置和管理
"""

import os
import logging
import logging.handlers
from typing import Optional, Dict, Any
from datetime import datetime
import threading
import queue
from pathlib import Path

from .config_manager import config_manager


class ThreadSafeColoredFormatter(logging.Formatter):
    """线程安全的彩色日志格式化器"""
    
    # ANSI颜色码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def __init__(self, fmt=None, datefmt=None, use_colors=True):
        super().__init__(fmt, datefmt)
        self.use_colors = use_colors
    
    def format(self, record):
        if self.use_colors and record.levelname in self.COLORS:
            # 为日志级别添加颜色
            original_levelname = record.levelname
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
            result = super().format(record)
            record.levelname = original_levelname  # 恢复原始值
            return result
        return super().format(record)


class UILogHandler(logging.Handler):
    """UI日志处理器，将日志消息发送到队列供UI显示"""
    
    def __init__(self, message_queue=None):
        super().__init__()
        self.message_queue = message_queue or queue.Queue()
        self.lock = threading.Lock()
    
    def emit(self, record):
        try:
            with self.lock:
                msg = self.format(record)
                log_data = {
                    'message': msg,
                    'level': record.levelname,
                    'timestamp': datetime.fromtimestamp(record.created),
                    'module': record.module,
                    'funcName': record.funcName,
                    'lineno': record.lineno
                }
                
                if self.message_queue:
                    try:
                        # 使用非阻塞方式放入队列，避免UI卡顿
                        self.message_queue.put_nowait(log_data)
                    except queue.Full:
                        # 队列满了就丢弃旧消息
                        try:
                            self.message_queue.get_nowait()
                            self.message_queue.put_nowait(log_data)
                        except queue.Empty:
                            pass
        except Exception:
            self.handleError(record)


class LoggerManager:
    """统一日志管理器"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.config = config_manager
            self.loggers: Dict[str, logging.Logger] = {}
            self.handlers: Dict[str, logging.Handler] = {}
            self.ui_message_queue = queue.Queue(maxsize=1000)
            self._setup_default_logging()
            self._initialized = True
    
    def _setup_default_logging(self):
        """设置默认日志配置"""
        # 禁用现有的所有日志处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 设置根日志器级别
        log_level = getattr(logging, self.config.get('logging.level', 'INFO').upper())
        root_logger.setLevel(log_level)
        
        # 创建默认格式化器
        log_format = self.config.get('logging.format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        # 控制台处理器
        if self.config.get('logging.console_output', True):
            console_handler = logging.StreamHandler()
            console_formatter = ThreadSafeColoredFormatter(log_format, use_colors=True)
            console_handler.setFormatter(console_formatter)
            console_handler.setLevel(log_level)
            root_logger.addHandler(console_handler)
            self.handlers['console'] = console_handler
        
        # 文件处理器
        if self.config.get('logging.file_output', True):
            log_file = self.config.get_log_file_path('app')
            file_handler = self._create_rotating_file_handler(log_file)
            root_logger.addHandler(file_handler)
            self.handlers['main_file'] = file_handler
        
        # UI处理器
        ui_handler = UILogHandler(self.ui_message_queue)
        ui_formatter = logging.Formatter(log_format)
        ui_handler.setFormatter(ui_formatter)
        ui_handler.setLevel(log_level)
        root_logger.addHandler(ui_handler)
        self.handlers['ui'] = ui_handler
    
    def _create_rotating_file_handler(self, log_file: str) -> logging.Handler:
        """创建轮转文件处理器"""
        # 确保日志目录存在
        log_dir = Path(log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        max_bytes = self.config.get('logging.max_file_size', 10 * 1024 * 1024)
        backup_count = self.config.get('logging.backup_count', 5)
        
        if self.config.get('logging.rotate_daily', True):
            # 按日期轮转
            handler = logging.handlers.TimedRotatingFileHandler(
                log_file,
                when='D',
                interval=1,
                backupCount=backup_count,
                encoding='utf-8'
            )
        else:
            # 按大小轮转
            handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
        
        # 设置格式
        log_format = self.config.get('logging.format')
        formatter = logging.Formatter(log_format)
        handler.setFormatter(formatter)
        
        return handler
    
    def get_logger(self, name: str, log_file: Optional[str] = None) -> logging.Logger:
        """
        获取或创建日志记录器
        
        Args:
            name: 日志记录器名称
            log_file: 可选的专用日志文件
            
        Returns:
            配置好的日志记录器
        """
        if name in self.loggers:
            return self.loggers[name]
        
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, self.config.get('logging.level', 'INFO').upper()))
        
        # 如果指定了专用日志文件，添加文件处理器
        if log_file:
            handler_key = f"file_{name}"
            if handler_key not in self.handlers:
                file_handler = self._create_rotating_file_handler(log_file)
                logger.addHandler(file_handler)
                self.handlers[handler_key] = file_handler
        
        self.loggers[name] = logger
        return logger
    
    def get_crawler_logger(self) -> logging.Logger:
        """获取爬虫专用日志记录器"""
        log_file = self.config.get_log_file_path('crawler')
        return self.get_logger('crawler', log_file)
    
    def get_analysis_logger(self) -> logging.Logger:
        """获取分析模块专用日志记录器"""
        log_file = self.config.get_log_file_path('analysis')
        return self.get_logger('analysis', log_file)
    
    def get_ui_logger(self) -> logging.Logger:
        """获取UI专用日志记录器"""
        return self.get_logger('ui')
    
    def get_database_logger(self) -> logging.Logger:
        """获取数据库专用日志记录器"""
        log_file = self.config.get_log_file_path('database')
        return self.get_logger('database', log_file)
    
    def get_ui_message_queue(self) -> queue.Queue:
        """获取UI消息队列"""
        return self.ui_message_queue
    
    def set_log_level(self, level: str):
        """设置所有日志记录器的日志级别"""
        log_level = getattr(logging, level.upper())
        
        # 更新根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        # 更新所有处理器
        for handler in self.handlers.values():
            handler.setLevel(log_level)
        
        # 更新配置
        self.config.set('logging.level', level.upper())
    
    def add_file_handler(self, logger_name: str, log_file: str):
        """为指定日志记录器添加文件处理器"""
        logger = self.get_logger(logger_name)
        handler_key = f"file_{logger_name}_{Path(log_file).stem}"
        
        if handler_key not in self.handlers:
            file_handler = self._create_rotating_file_handler(log_file)
            logger.addHandler(file_handler)
            self.handlers[handler_key] = file_handler
    
    def remove_handler(self, handler_key: str):
        """移除指定的处理器"""
        if handler_key in self.handlers:
            handler = self.handlers[handler_key]
            
            # 从所有日志记录器中移除该处理器
            for logger in self.loggers.values():
                if handler in logger.handlers:
                    logger.removeHandler(handler)
            
            # 从根日志记录器中移除
            root_logger = logging.getLogger()
            if handler in root_logger.handlers:
                root_logger.removeHandler(handler)
            
            # 关闭处理器
            handler.close()
            del self.handlers[handler_key]
    
    def cleanup(self):
        """清理所有日志处理器"""
        for handler_key in list(self.handlers.keys()):
            self.remove_handler(handler_key)
        
        self.loggers.clear()
        self.handlers.clear()
    
    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            'active_loggers': len(self.loggers),
            'active_handlers': len(self.handlers),
            'ui_queue_size': self.ui_message_queue.qsize(),
            'log_level': self.config.get('logging.level'),
            'log_files': []
        }
        
        # 收集日志文件信息
        logs_dir = Path(self.config.get('paths.logs_dir'))
        if logs_dir.exists():
            for log_file in logs_dir.glob('*.log'):
                stats['log_files'].append({
                    'name': log_file.name,
                    'size': log_file.stat().st_size,
                    'modified': datetime.fromtimestamp(log_file.stat().st_mtime)
                })
        
        return stats


# 全局日志管理器实例
logger_manager = LoggerManager()

# 便捷函数
def get_logger(name: str = 'app', log_file: Optional[str] = None) -> logging.Logger:
    """获取日志记录器的便捷函数"""
    return logger_manager.get_logger(name, log_file)

def get_crawler_logger() -> logging.Logger:
    """获取爬虫日志记录器"""
    return logger_manager.get_crawler_logger()

def get_ui_logger() -> logging.Logger:
    """获取UI日志记录器"""
    return logger_manager.get_ui_logger()

def get_ui_message_queue() -> queue.Queue:
    """获取UI消息队列"""
    return logger_manager.get_ui_message_queue() 