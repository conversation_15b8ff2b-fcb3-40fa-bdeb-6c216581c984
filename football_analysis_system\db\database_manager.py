import os
import sqlite3
import logging

class DatabaseManager:
    """数据库管理基类，提供基本的数据库操作功能"""
    
    def __init__(self, db_path):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self._verify_db_path()
        
    def _verify_db_path(self):
        """验证数据库路径，尝试使用备用路径"""
        if not os.path.exists(self.db_path):
            alt_path = os.path.basename(self.db_path)
            logging.info(f"未在 {self.db_path} 找到数据库，尝试备用路径 {alt_path}")
            
            if os.path.exists(alt_path):
                self.db_path = alt_path
                logging.info(f"使用备用路径: {self.db_path}")
            else:
                logging.warning(f"找不到数据库文件: {self.db_path}")
    
    def connect(self):
        """
        创建数据库连接
        
        Returns:
            sqlite3.Connection: 数据库连接对象
        """
        try:
            return sqlite3.connect(self.db_path)
        except sqlite3.Error as e:
            logging.error(f"连接数据库 {self.db_path} 时出错: {e}")
            raise
    
    def execute_query(self, query, params=(), fetchall=True):
        """
        执行查询并获取结果
        
        Args:
            query: SQL查询语句
            params: 查询参数
            fetchall: 是否获取所有结果，False时仅获取一条
            
        Returns:
            list or dict: 查询结果
        """
        try:
            conn = self.connect()
            cursor = conn.cursor()
            cursor.execute(query, params)
            
            if fetchall:
                result = cursor.fetchall()
            else:
                result = cursor.fetchone()
                
            conn.close()
            return result
        except sqlite3.Error as e:
            logging.error(f"执行查询时出错: {e}")
            logging.error(f"查询: {query}")
            logging.error(f"参数: {params}")
            return None
    
    def execute_update(self, query, params=()):
        """
        执行更新操作（INSERT, UPDATE, DELETE）
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            bool: 操作是否成功
        """
        try:
            conn = self.connect()
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            affected_rows = cursor.rowcount
            conn.close()
            return affected_rows
        except sqlite3.Error as e:
            logging.error(f"执行更新操作时出错: {e}")
            logging.error(f"查询: {query}")
            logging.error(f"参数: {params}")
            return 0
    
    def execute_many(self, query, params_list):
        """
        执行批量操作
        
        Args:
            query: SQL查询语句
            params_list: 参数列表
            
        Returns:
            bool: 操作是否成功
        """
        if not params_list:
            return 0
            
        try:
            conn = self.connect()
            cursor = conn.cursor()
            cursor.executemany(query, params_list)
            conn.commit()
            affected_rows = cursor.rowcount
            conn.close()
            return affected_rows
        except sqlite3.Error as e:
            logging.error(f"执行批量操作时出错: {e}")
            logging.error(f"查询: {query}")
            return 0
    
    def get_table_names(self):
        """
        获取数据库中的所有表名
        
        Returns:
            list: 表名列表
        """
        query = "SELECT name FROM sqlite_master WHERE type='table';"
        result = self.execute_query(query)
        return [row[0] for row in result] if result else []
    
    def get_column_names(self, table_name):
        """
        获取指定表的列名
        
        Args:
            table_name: 表名
            
        Returns:
            list: 列名列表
        """
        query = f"PRAGMA table_info({table_name})"
        result = self.execute_query(query)
        return [row[1] for row in result] if result else []