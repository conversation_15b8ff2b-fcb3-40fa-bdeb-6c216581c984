"""
历史赔率数据库接口模块

该模块负责与odds_history表进行交互，提供数据查询和获取功能。
"""

import logging
import sqlite3
from typing import List, Dict, Optional, Tuple
from football_analysis_system.db.database_manager import DatabaseManager
from football_analysis_system.analysis.historical_data_processor import HistoricalOddsRecord


class HistoricalDatabaseInterface:
    """历史赔率数据库接口类"""
    
    def __init__(self, db_path: str):
        """
        初始化数据库接口
        
        Args:
            db_path: 数据库路径
        """
        self.db_manager = DatabaseManager(db_path)
        self.logger = logging.getLogger(__name__)
        
    def get_available_companies(self, match_id: str) -> List[Dict[str, str]]:
        """
        获取指定比赛的可用博彩公司
        
        Args:
            match_id: 比赛ID
            
        Returns:
            list: 公司列表，包含company_id和company_name
        """
        try:
            query = """
                SELECT DISTINCT company_id, company_name 
                FROM history_odds 
                WHERE match_id = ? 
                ORDER BY company_name
            """
            
            result = self.db_manager.execute_query(query, (match_id,))
            
            if not result:
                self.logger.warning(f"未找到比赛 {match_id} 的博彩公司数据")
                return []
            
            companies = [
                {
                    'company_id': row[0],
                    'company_name': row[1]
                }
                for row in result
            ]
            
            self.logger.info(f"找到 {len(companies)} 家博彩公司的数据")
            return companies
            
        except Exception as e:
            self.logger.error(f"获取可用博彩公司时出错: {e}")
            return []
    
    def get_historical_odds(self, match_id: str, company_id: str) -> List[HistoricalOddsRecord]:
        """
        获取指定公司的历史赔率数据
        
        Args:
            match_id: 比赛ID
            company_id: 公司ID
            
        Returns:
            list: 历史赔率数据，按时间排序
        """
        try:
            query = """
                SELECT 
                    match_id, company_id, company_name, record_id,
                    home_odds, draw_odds, away_odds, update_time,
                    kelly_home, kelly_draw, kelly_away
                FROM history_odds 
                WHERE match_id = ? AND company_id = ?
                ORDER BY update_time DESC
            """
            
            result = self.db_manager.execute_query(query, (match_id, company_id))
            
            if not result:
                self.logger.warning(f"未找到比赛 {match_id} 公司 {company_id} 的历史数据")
                return []
            
            historical_records = []
            for row in result:
                try:
                    record = HistoricalOddsRecord(
                        match_id=row[0],
                        company_id=row[1],
                        company_name=row[2],
                        home_odds=float(row[4]) if row[4] is not None else 0.0,
                        draw_odds=float(row[5]) if row[5] is not None else 0.0,
                        away_odds=float(row[6]) if row[6] is not None else 0.0,
                        update_time=row[7] or "",
                        record_id=row[3],
                        kelly_home=float(row[8]) if row[8] is not None else None,
                        kelly_draw=float(row[9]) if row[9] is not None else None,
                        kelly_away=float(row[10]) if row[10] is not None else None
                    )
                    historical_records.append(record)
                    
                except (ValueError, TypeError) as e:
                    self.logger.warning(f"解析历史记录时出错: {e}, 跳过该记录")
                    continue
            
            self.logger.info(f"成功获取 {len(historical_records)} 条历史记录")
            return historical_records
            
        except Exception as e:
            self.logger.error(f"获取历史赔率数据时出错: {e}")
            return []
    
    def get_all_matches_with_history(self) -> List[str]:
        """
        获取所有有历史数据的比赛ID
        
        Returns:
            list: 比赛ID列表
        """
        try:
            query = """
                SELECT DISTINCT match_id 
                FROM history_odds 
                ORDER BY match_id
            """
            
            result = self.db_manager.execute_query(query)
            
            if not result:
                self.logger.warning("未找到任何历史数据")
                return []
            
            match_ids = [row[0] for row in result]
            self.logger.info(f"找到 {len(match_ids)} 场比赛的历史数据")
            return match_ids
            
        except Exception as e:
            self.logger.error(f"获取比赛列表时出错: {e}")
            return []
    
    def get_company_history_count(self, match_id: str, company_id: str) -> int:
        """
        获取指定公司的历史记录数量
        
        Args:
            match_id: 比赛ID
            company_id: 公司ID
            
        Returns:
            int: 历史记录数量
        """
        try:
            query = """
                SELECT COUNT(*) 
                FROM history_odds 
                WHERE match_id = ? AND company_id = ?
            """
            
            result = self.db_manager.execute_query(query, (match_id, company_id), fetchall=False)
            
            if result:
                return result[0]
            else:
                return 0
                
        except Exception as e:
            self.logger.error(f"获取历史记录数量时出错: {e}")
            return 0
    
    def get_time_range(self, match_id: str, company_id: str) -> Tuple[Optional[str], Optional[str]]:
        """
        获取指定公司历史数据的时间范围
        
        Args:
            match_id: 比赛ID
            company_id: 公司ID
            
        Returns:
            tuple: (最早时间, 最晚时间)
        """
        try:
            query = """
                SELECT MIN(update_time), MAX(update_time)
                FROM history_odds 
                WHERE match_id = ? AND company_id = ?
            """
            
            result = self.db_manager.execute_query(query, (match_id, company_id), fetchall=False)
            
            if result:
                return result[0], result[1]
            else:
                return None, None
                
        except Exception as e:
            self.logger.error(f"获取时间范围时出错: {e}")
            return None, None
    
    def validate_database_connection(self) -> bool:
        """
        验证数据库连接和表结构
        
        Returns:
            bool: 连接是否有效
        """
        try:
            # 检查表是否存在
            tables = self.db_manager.get_table_names()
            if 'history_odds' not in tables:
                self.logger.error("history_odds表不存在")
                return False
            
            # 检查表结构
            columns = self.db_manager.get_column_names('history_odds')
            required_columns = [
                'match_id', 'company_id', 'company_name', 
                'home_odds', 'draw_odds', 'away_odds', 'update_time'
            ]
            
            missing_columns = [col for col in required_columns if col not in columns]
            if missing_columns:
                self.logger.error(f"history_odds表缺少必要列: {missing_columns}")
                return False
            
            self.logger.info("数据库连接和表结构验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"验证数据库连接时出错: {e}")
            return False
    
    def get_latest_odds(self, match_id: str, company_id: str) -> Optional[HistoricalOddsRecord]:
        """
        获取指定公司的最新赔率
        
        Args:
            match_id: 比赛ID
            company_id: 公司ID
            
        Returns:
            HistoricalOddsRecord: 最新赔率记录，如果没有则返回None
        """
        try:
            query = """
                SELECT 
                    match_id, company_id, company_name, record_id,
                    home_odds, draw_odds, away_odds, update_time,
                    kelly_home, kelly_draw, kelly_away
                FROM history_odds 
                WHERE match_id = ? AND company_id = ?
                ORDER BY update_time DESC
                LIMIT 1
            """
            
            result = self.db_manager.execute_query(query, (match_id, company_id), fetchall=False)
            
            if not result:
                return None
            
            row = result
            return HistoricalOddsRecord(
                match_id=row[0],
                company_id=row[1],
                company_name=row[2],
                home_odds=float(row[4]) if row[4] is not None else 0.0,
                draw_odds=float(row[5]) if row[5] is not None else 0.0,
                away_odds=float(row[6]) if row[6] is not None else 0.0,
                update_time=row[7] or "",
                record_id=row[3],
                kelly_home=float(row[8]) if row[8] is not None else None,
                kelly_draw=float(row[9]) if row[9] is not None else None,
                kelly_away=float(row[10]) if row[10] is not None else None
            )
            
        except Exception as e:
            self.logger.error(f"获取最新赔率时出错: {e}")
            return None