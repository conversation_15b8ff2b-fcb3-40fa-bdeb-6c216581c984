# Requirements Document

## Introduction

统一足球分析系统中的数据库路径配置，解决当前数据分散在两个不同位置的问题。主要针对odds_scraper相关代码使用外层data目录的问题，确保所有组件都使用内层的标准数据库路径（football_analysis_system/data/）。

## Requirements

### Requirement 1

**User Story:** 作为系统维护者，我希望所有数据库文件都存储在统一的位置，这样我就能更好地管理和备份数据。

#### Acceptance Criteria

1. WHEN 系统运行时 THEN 所有组件 SHALL 使用 football_analysis_system/data/ 目录作为数据库存储位置
2. WHEN odds_scraper 保存数据时 THEN 系统 SHALL 将数据保存到 football_analysis_system/data/matches_and_odds.db
3. WHEN 测试代码运行时 THEN 系统 SHALL 使用内层数据库路径而不是外层相对路径

### Requirement 2

**User Story:** 作为开发者，我希望odds_scraper和相关测试代码能够正确使用配置的数据库路径，这样就不会意外创建新的数据库文件。

#### Acceptance Criteria

1. WHEN odds_scraper初始化时 THEN 系统 SHALL 从统一配置获取数据库路径
2. WHEN 测试文件运行时 THEN 系统 SHALL 使用配置管理器提供的数据库路径
3. IF 代码中存在硬编码的"data/matches_and_odds.db"路径 THEN 系统 SHALL 替换为配置化的路径

### Requirement 3

**User Story:** 作为系统用户，我希望现有的数据不会丢失，并且系统能够继续正常工作，这样我就不需要重新爬取历史数据。

#### Acceptance Criteria

1. WHEN 路径统一完成后 THEN 系统 SHALL 保留所有现有的数据库数据
2. WHEN 外层data目录存在更多数据时 THEN 系统 SHALL 将外层数据合并到内层数据库
3. WHEN 路径修改完成后 THEN 原有的功能 SHALL 继续正常工作

### Requirement 4

**User Story:** 作为系统管理员，我希望能够清理不再使用的外层data目录，这样就能保持项目结构的整洁。

#### Acceptance Criteria

1. WHEN 数据迁移完成后 THEN 系统 SHALL 提供清理外层data目录的选项
2. WHEN 清理操作执行时 THEN 系统 SHALL 确认所有数据已成功迁移
3. WHEN 清理完成后 THEN 系统 SHALL 验证所有功能仍然正常工作

### Requirement 5

**User Story:** 作为开发者，我希望配置系统能够提供一致的数据库路径访问方式，这样新的代码就不会再出现路径不一致的问题。

#### Acceptance Criteria

1. WHEN 新代码需要访问数据库时 THEN 系统 SHALL 提供统一的路径获取方法
2. WHEN ConfigManager被调用时 THEN 系统 SHALL 返回正确的内层数据库路径
3. WHEN 代码使用相对路径时 THEN 系统 SHALL 自动解析为正确的绝对路径