#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试网络请求和基本功能
"""

import sys
import os
import time
import requests

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from football_analysis_system.config import ODDS_URL_TEMPLATE, ODDS_HEADERS_TEMPLATE

def test_network_request():
    """测试网络请求"""
    
    print("=== 测试网络请求 ===")
    
    # 使用真实的比赛ID
    match_id = "2696009"
    
    # 生成时间戳
    timestamp_part = str(int(time.time() * 1000))
    if len(timestamp_part) > 13:
        timestamp_part = timestamp_part[-13:]
    else:
        timestamp_part = timestamp_part.ljust(13, '0')
    timestamp = "007" + timestamp_part
    
    # 构建URL
    url = ODDS_URL_TEMPLATE.format(match_id=match_id, timestamp=timestamp)
    print(f"请求URL: {url}")
    
    # 构建请求头
    headers = ODDS_HEADERS_TEMPLATE.copy()
    headers['Referer'] = headers['Referer'].format(match_id=match_id)
    
    print(f"请求头: {headers}")
    
    try:
        print("\n正在发送请求...")
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            content = response.text
            print(f"响应内容前500字符:")
            print("-" * 50)
            print(content[:500])
            print("-" * 50)
            
            # 检查是否包含var game
            if "var game" in content:
                print("✅ 找到 'var game' 内容！")
                
                # 简单提取game数组内容
                import re
                game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', content, re.DOTALL)
                if game_match:
                    game_data = game_match.group(1)
                    print(f"✅ 成功提取game数组，长度: {len(game_data)} 字符")
                    print(f"game数组前200字符:")
                    print(game_data[:200])
                    
                    # 尝试提取记录数量
                    company_records = re.findall(r'"([^"]*)"', game_data)
                    print(f"✅ 找到 {len(company_records)} 条记录")
                    
                    if len(company_records) > 0:
                        print(f"第一条记录: {company_records[0][:100]}...")
                        
                        # 分析第一条记录的字段
                        fields = company_records[0].split('|')
                        print(f"第一条记录字段数: {len(fields)}")
                        print("前10个字段:")
                        for i, field in enumerate(fields[:10]):
                            print(f"  [{i}] {field}")
                            
                else:
                    print("❌ 无法提取game数组内容")
            else:
                print("❌ 响应中没有找到 'var game'")
                
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_network_request() 