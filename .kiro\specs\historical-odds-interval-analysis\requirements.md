# Requirements Document

## Introduction

本功能旨在为足球分析系统新增历史赔率区间分析模块。该功能将基于现有的区间分析标签页实现，从odds_history表中获取历史赔率数据，使用现有的calculate_true_odds、find_gap_for_odds、find_interval_for_gap等方法计算满水赔率并确定开盘区间，最终通过可视化折线图展示不同时间点主平客赔率在各区间的变化趋势。用户可以按博彩公司进行筛选，在新的标签页中查看分析结果。

## Requirements

### Requirement 1

**User Story:** 作为系统用户，我希望能够查看历史赔率的区间分析，以便了解不同时间点赔率在各开盘区间的分布和变化趋势。

#### Acceptance Criteria

1. WHEN 用户访问历史赔率区间分析页面 THEN 系统 SHALL 显示一个新的标签页界面
2. WHEN 系统加载页面 THEN 系统 SHALL 提供博彩公司选择下拉框，列出odds_history表中可用的公司
3. WHEN 用户选择特定博彩公司 THEN 系统 SHALL 从odds_history表获取该公司的历史赔率数据
4. WHEN 系统获取到赔率数据 THEN 系统 SHALL 先计算每个时间点的返还率（使用公式：返还率 = 胜赔率 × 平赔率 × 负赔率 / (胜赔率 × 平赔率 + 平赔率 × 负赔率 + 胜赔率 × 负赔率) × 100%），再使用现有的calculate_true_odds方法计算满水赔率

### Requirement 2

**User Story:** 作为系统用户，我希望系统能够自动计算赔率的开盘区间，以便准确分类不同的赔率水平。

#### Acceptance Criteria

1. WHEN 系统计算满水赔率后 THEN 系统 SHALL 使用现有的find_gap_for_odds方法查找对应档距
2. WHEN 确定开盘区间时 THEN 系统 SHALL 使用现有的find_interval_for_gap方法计算区间类型
3. WHEN 处理历史数据时 THEN 系统 SHALL 为每个时间点的主平客赔率分别计算对应区间（超深盘、深盘、中水、浅盘、超韬盘等）
4. WHEN 计算区间时 THEN 系统 SHALL 使用当前比赛的档距差数据作为计算基准
5. IF 赔率数据不完整或异常 THEN 系统 SHALL 跳过该时间点并记录日志

### Requirement 3

**User Story:** 作为系统用户，我希望通过可视化图表查看赔率区间的时间变化，以便直观分析赔率走势。

#### Acceptance Criteria

1. WHEN 系统完成区间计算 THEN 系统 SHALL 生成折线图显示赔率区间变化
2. WHEN 生成图表时 THEN 系统 SHALL 将Y轴设置为开盘区间（按顺序：超深盘、深盘、中水、浅盘、超韬盘等）
3. WHEN 设置图表轴线时 THEN 系统 SHALL 将X轴设置为时间轴，显示历史赔率的更新时间
4. WHEN 绘制折线时 THEN 系统 SHALL 分别绘制主胜、平局、客胜三条不同颜色的折线
5. WHEN 显示图表时 THEN 系统 SHALL 提供图例说明主平客三条线的含义
6. WHEN 区间数据为数值时 THEN 系统 SHALL 将区间类型转换为Y轴坐标进行绘制
7. WHEN 图表显示时 THEN 系统 SHALL 使用matplotlib或类似图表库进行可视化

### Requirement 4

**User Story:** 作为系统用户，我希望能够灵活选择不同的博彩公司进行分析，以便比较不同公司的赔率变化模式。

#### Acceptance Criteria

1. WHEN 页面初始化时 THEN 系统 SHALL 从数据库获取所有可用的博彩公司列表
2. WHEN 用户点击公司选择框 THEN 系统 SHALL 显示所有可选的博彩公司选项
3. WHEN 用户选择不同公司时 THEN 系统 SHALL 重新计算并更新图表显示
4. WHEN 切换公司时 THEN 系统 SHALL 显示加载状态直到新数据处理完成
5. IF 某公司没有足够的历史数据 THEN 系统 SHALL 显示相应提示信息

### Requirement 5

**User Story:** 作为系统用户，我希望新功能能够与现有系统无缝集成，以便在统一界面中使用所有分析功能。

#### Acceptance Criteria

1. WHEN 实现新功能时 THEN 系统 SHALL 复用现有区间分析模块的核心算法
2. WHEN 添加新标签页时 THEN 系统 SHALL 保持与现有UI风格的一致性
3. WHEN 访问数据库时 THEN 系统 SHALL 使用现有的数据库连接和配置
4. WHEN 处理错误时 THEN 系统 SHALL 使用统一的错误处理和日志记录机制
5. WHEN 加载页面时 THEN 系统 SHALL 确保不影响其他功能模块的正常运行