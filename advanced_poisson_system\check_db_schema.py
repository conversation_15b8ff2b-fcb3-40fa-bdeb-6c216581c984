"""
数据库结构检查脚本
"""

import sqlite3
import os

def check_db_schema():
    """检查数据库表结构"""
    
    # 数据库路径
    db_path = '../football_analysis_system/data/matches_and_odds.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    print(f"检查数据库: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取所有表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    print(f"\n发现 {len(tables)} 个表:")
    for table in tables:
        table_name = table[0]
        print(f"\n=== 表: {table_name} ===")
        
        # 获取表结构
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        print("列信息:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) {'主键' if col[5] else ''}")
        
        # 获取示例数据
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        print(f"数据行数: {count}")
        
        if count > 0:
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
            sample_data = cursor.fetchall()
            print("示例数据:")
            for i, row in enumerate(sample_data):
                print(f"  行{i+1}: {row}")
    
    conn.close()

if __name__ == "__main__":
    check_db_schema() 