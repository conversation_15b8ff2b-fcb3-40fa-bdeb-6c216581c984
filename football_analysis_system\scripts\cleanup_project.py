#!/usr/bin/env python3
"""
项目清理和优化脚本
执行项目结构优化、文件整理、配置统一等操作
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core import ConfigManager, LoggerManager, Utils
from core.logger_manager import get_logger


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 足球分析系统 - 项目清理和优化工具")
    print("=" * 60)
    
    # 初始化
    logger = get_logger('cleanup_tool')
    logger.info("项目清理工具启动")
    
    try:
        # 显示项目信息
        show_project_info()
        
        # 询问用户是否继续
        if not confirm_operation():
            logger.info("用户取消操作")
            return
        
        # 执行清理操作
        results = Utils.clean_project()
        
        # 显示结果
        show_cleanup_results(results)
        
        # 显示清理后的项目信息
        print("\n" + "=" * 60)
        print("📊 清理后的项目状态:")
        print("=" * 60)
        show_project_info()
        
        logger.info("项目清理完成")
        print("\n✅ 项目清理和优化完成!")
        
    except Exception as e:
        logger.error(f"清理过程中发生错误: {e}")
        print(f"\n❌ 清理失败: {e}")


def show_project_info():
    """显示项目信息"""
    info = Utils.get_project_info()
    
    print(f"\n📁 项目根目录: {info['project_root']}")
    print("\n📂 目录状态:")
    
    for dir_key, dir_info in info['directories'].items():
        status = "✅" if dir_info['exists'] else "❌"
        size_str = Utils.format_size(dir_info['total_size'])
        print(f"  {status} {dir_key}: {dir_info['path']}")
        print(f"     文件数: {dir_info['file_count']}, 大小: {size_str}")
    
    total_size = Utils.format_size(info['total_size'])
    print(f"\n📊 总大小: {total_size}")


def confirm_operation() -> bool:
    """确认操作"""
    print("\n⚠️  即将执行以下操作:")
    print("  1. 整理散落的文件到对应目录")
    print("  2. 清理重复的配置文件")
    print("  3. 统一数据库文件路径")
    print("  4. 清理旧日志文件")
    print("  5. 删除空目录")
    print("  6. 迁移配置文件格式")
    
    while True:
        choice = input("\n是否继续? (y/N): ").strip().lower()
        if choice in ['y', 'yes']:
            return True
        elif choice in ['n', 'no', '']:
            return False
        else:
            print("请输入 y 或 n")


def show_cleanup_results(results):
    """显示清理结果"""
    print("\n" + "=" * 60)
    print("📋 清理结果报告:")
    print("=" * 60)
    
    if results['moved_files']:
        print(f"\n📦 已移动文件 ({len(results['moved_files'])} 个):")
        for moved in results['moved_files'][:10]:  # 只显示前10个
            print(f"  • {moved}")
        if len(results['moved_files']) > 10:
            print(f"  ... 还有 {len(results['moved_files']) - 10} 个文件")
    
    if results['moved_databases']:
        print(f"\n🗄️  已移动数据库 ({len(results['moved_databases'])} 个):")
        for moved in results['moved_databases']:
            print(f"  • {moved}")
    
    if results['removed_directories']:
        print(f"\n🗑️  已删除空目录 ({len(results['removed_directories'])} 个):")
        for removed in results['removed_directories'][:5]:  # 只显示前5个
            print(f"  • {removed}")
        if len(results['removed_directories']) > 5:
            print(f"  ... 还有 {len(results['removed_directories']) - 5} 个目录")


def show_help():
    """显示帮助信息"""
    print("""
使用方法:
    python cleanup_project.py [选项]

选项:
    -h, --help     显示帮助信息
    -f, --force    强制执行，不询问确认
    -v, --verbose  详细输出
    --dry-run      模拟运行，不实际执行操作

示例:
    python cleanup_project.py              # 交互式清理
    python cleanup_project.py --force      # 强制清理
    python cleanup_project.py --dry-run    # 模拟运行
""")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            show_help()
            sys.exit(0)
        elif sys.argv[1] == '--dry-run':
            print("🔍 模拟运行模式 - 不会实际修改文件")
            # TODO: 实现模拟运行模式
        elif sys.argv[1] in ['-f', '--force']:
            print("⚡ 强制模式 - 跳过确认")
            # TODO: 实现强制模式
    
    main() 