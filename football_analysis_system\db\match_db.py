import logging
import json
import sqlite3
from datetime import datetime
from .database_manager import DatabaseManager
from football_analysis_system.models.match import Match
from football_analysis_system.models.odds import Odds

class MatchDatabase(DatabaseManager):
    """比赛和赔率数据库操作类"""

    def __init__(self, db_path):
        """
        初始化比赛数据库管理器

        Args:
            db_path: 数据库文件路径
        """
        super().__init__(db_path)

    def setup_database(self):
        """创建必要的表结构"""
        try:
            conn = self.connect()
            cursor = conn.cursor()

            # 创建比赛表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS matches (
                match_id TEXT PRIMARY KEY,
                league_name TEXT,
                home_team TEXT,
                away_team TEXT,
                start_time TEXT,
                scrape_time TEXT
            )
            ''')

            # 创建赔率表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS odds (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT,
                bookmaker TEXT,
                initial_home_win REAL,
                initial_draw REAL,
                initial_away_win REAL,
                instant_home_win REAL,
                instant_draw REAL,
                instant_away_win REAL,
                payout_rate REAL,
                initial_payout_rate REAL,
                earliest_open_time TEXT,
                scrape_time TEXT,
                FOREIGN KEY (match_id) REFERENCES matches (match_id)
            )
            ''')

            conn.commit()
            conn.close()

            # 更新表结构以兼容老版本
            self.update_odds_table()

            logging.info("成功创建或验证数据库表结构")
            return True
        except sqlite3.Error as e:
            logging.error(f"设置数据库表结构时出错: {e}")
            return False

    def update_odds_table(self):
        """安全地更新odds表结构，添加缺失的字段"""
        try:
            conn = self.connect()
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='odds'")
            if not cursor.fetchone():
                logging.info("odds表不存在，无需更新")
                conn.close()
                return

            # 获取表结构
            cursor.execute("PRAGMA table_info(odds)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            # 检查是否缺少earliest_open_time字段
            if 'earliest_open_time' not in column_names:
                logging.info("添加earliest_open_time字段到odds表")
                cursor.execute("ALTER TABLE odds ADD COLUMN earliest_open_time TEXT")
                conn.commit()
                logging.info("成功添加earliest_open_time字段")
            else:
                logging.info("earliest_open_time字段已存在，无需更新")

            conn.close()
        except sqlite3.Error as e:
            logging.error(f"更新表结构时出错: {e}")
            if conn:
                conn.close()

    def clear_data(self):
        """清空比赛和赔率数据"""
        try:
            conn = self.connect()
            cursor = conn.cursor()

            # 先删除外键表数据
            cursor.execute("DELETE FROM odds")
            cursor.execute("DELETE FROM matches")

            conn.commit()
            conn.close()
            logging.info("成功清空数据库数据")
            return True
        except sqlite3.Error as e:
            logging.error(f"清空数据库数据时出错: {e}")
            return False

    def load_matches_and_odds(self):
        """
        加载所有比赛和对应的赔率数据

        Returns:
            list: 比赛对象列表
        """
        try:
            # 加载基础比赛信息
            matches_query = "SELECT match_id, league_name, start_time, home_team, away_team FROM matches ORDER BY start_time DESC"
            matches_data = self.execute_query(matches_query)

            if not matches_data:
                logging.warning("未找到比赛数据")
                return []

            # 创建比赛ID到比赛对象的映射
            match_dict = {}
            for row in matches_data:
                match_id, league_name, start_time, home_team, away_team = row
                match_dict[match_id] = Match(
                    match_id=match_id,
                    league_name=league_name,
                    start_time=start_time,
                    home_team=home_team,
                    away_team=away_team,
                    odds={}
                )

            # 首先检查odds表中是否有earliest_open_time列
            has_earliest_open_time = False
            try:
                # 获取表结构
                table_info = self.execute_query("PRAGMA table_info(odds)")
                column_names = [col[1] for col in table_info]
                has_earliest_open_time = 'earliest_open_time' in column_names
                logging.info(f"检查odds表结构: {'包含' if has_earliest_open_time else '不包含'} earliest_open_time字段")
            except Exception as e:
                logging.warning(f"检查表结构时出错: {e}")

            # 根据表结构选择正确的查询语句
            if has_earliest_open_time:
                # 新版表结构
                odds_query = """
                SELECT match_id, bookmaker,
                       initial_home_win, initial_draw, initial_away_win,
                       instant_home_win, instant_draw, instant_away_win,
                       payout_rate, initial_payout_rate, earliest_open_time
                FROM odds
                """
            else:
                # 兼容旧版表结构
                odds_query = """
                SELECT match_id, bookmaker,
                       initial_home_win, initial_draw, initial_away_win,
                       instant_home_win, instant_draw, instant_away_win,
                       payout_rate, initial_payout_rate
                FROM odds
                """

            odds_data = self.execute_query(odds_query)

            if odds_data:
                for row in odds_data:
                    match_id, bookmaker = row[0], row[1]

                    if match_id in match_dict:
                        # 添加两种格式的字段名，确保与JSON格式一致
                        odds_dict = {
                            # 数据库格式字段名
                            'initial_home_win': row[2],
                            'initial_draw': row[3],
                            'initial_away_win': row[4],
                            'instant_home_win': row[5],
                            'instant_draw': row[6],
                            'instant_away_win': row[7],
                            'payout_rate': row[8],
                            'initial_payout_rate': row[9],
                            # JSON格式字段名（别名）
                            'Initial_W': row[2],
                            'Initial_D': row[3],
                            'Initial_L': row[4],
                            'Instant_W': row[5],
                            'Instant_D': row[6],
                            'Instant_L': row[7],
                            'Payout_Rate': row[8],
                            'Initial_Payout_Rate': row[9],
                        }

                        # 如果有earliest_open_time字段，添加到字典
                        if has_earliest_open_time and len(row) > 10:
                            odds_dict['earliest_open_time'] = ''  # 字段存在但使用空值
                            odds_dict['Earliest_Open_Time'] = ''  # 字段存在但使用空值
                        else:
                            odds_dict['earliest_open_time'] = ''
                            odds_dict['Earliest_Open_Time'] = ''

                        match_dict[match_id].odds[bookmaker] = odds_dict

            logging.info(f"成功加载 {len(match_dict)} 场比赛及其赔率")
            return list(match_dict.values())

        except Exception as e:
            logging.error(f"加载比赛和赔率数据时出错: {e}")
            return []

    def save_matches_and_odds(self, matches, odds_data):
        """
        保存比赛和赔率数据

        Args:
            matches: 包含比赛数据的DataFrame或列表
            odds_data: 字典 {match_id: {bookmaker: odds_dict}}

        Returns:
            bool: 是否保存成功
        """
        if not matches:
            logging.warning("没有比赛数据可保存")
            return False

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        try:
            conn = self.connect()
            cursor = conn.cursor()

            # 保存比赛数据
            for match in matches:
                if hasattr(match, 'to_dict'):  # 如果是Match对象
                    match_data = match.to_dict()
                    match_id = match_data['match_id']
                    league_name = match_data['league_name']
                    home_team = match_data['home_team']
                    away_team = match_data['away_team']
                    start_time = match_data['start_time']
                else:  # 如果是DataFrame的行
                    match_id = match['MatchID']
                    league_name = match['LeagueNameSimp']
                    home_team = match['HomeTeamSimp']
                    away_team = match['AwayTeamSimp']
                    start_time = match['StartTimeShort']

                cursor.execute(
                    '''INSERT OR REPLACE INTO matches
                       (match_id, league_name, home_team, away_team, start_time, scrape_time)
                       VALUES (?, ?, ?, ?, ?, ?)''',
                    (match_id, league_name, home_team, away_team, start_time, current_time)
                )

            # 保存赔率数据
            for match_id, bookmakers in odds_data.items():
                for bookmaker, odds in bookmakers.items():
                    # 从Initial_W映射到initial_home_win等字段
                    initial_home_win = odds.get('Initial_W', '')
                    initial_draw = odds.get('Initial_D', '')
                    initial_away_win = odds.get('Initial_L', '')
                    instant_home_win = odds.get('Instant_W', '')
                    instant_draw = odds.get('Instant_D', '')
                    instant_away_win = odds.get('Instant_L', '')
                    payout_rate = odds.get('Payout_Rate', '')
                    initial_payout_rate = odds.get('Initial_Payout_Rate', '')  # 获取初始返还率
                    # 最早开盘时间字段不再使用，但保留空值以维持表结构
                    earliest_open_time = ''

                    # 添加详细日志，特别关注澳门和香港马会的数据
                    if "澳门" in bookmaker or "香港马会" in bookmaker or "HKJC" in bookmaker or "Coolbet" in bookmaker or "iddaa" in bookmaker:
                        logging.info(f"保存{bookmaker}赔率 - 初赔: {initial_home_win}/{initial_draw}/{initial_away_win}, "
                                   f"即时赔率: {instant_home_win}/{instant_draw}/{instant_away_win}, "
                                   f"初始返还率: {initial_payout_rate}, 即时返还率: {payout_rate}")

                    cursor.execute(
                        '''INSERT INTO odds
                           (match_id, bookmaker, initial_home_win, initial_draw, initial_away_win,
                            instant_home_win, instant_draw, instant_away_win, payout_rate, initial_payout_rate,
                            earliest_open_time, scrape_time)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                        (
                            match_id, bookmaker,
                            initial_home_win, initial_draw, initial_away_win,
                            instant_home_win, instant_draw, instant_away_win,
                            payout_rate, initial_payout_rate,
                            earliest_open_time, current_time
                        )
                    )

            conn.commit()
            conn.close()
            logging.info(f"成功保存 {len(matches)} 场比赛及其赔率数据")
            return True

        except Exception as e:
            if conn:
                conn.rollback()
                conn.close()
            logging.error(f"保存比赛和赔率数据时出错: {e}")
            return False

    def save_to_json(self, matches, odds_data, filename):
        """
        保存比赛和赔率数据到JSON文件

        Args:
            matches: 包含比赛数据的DataFrame或对象列表
            odds_data: 赔率数据字典 {match_id: {bookmaker: odds_dict}}
            filename: 保存的JSON文件路径

        Returns:
            bool: 是否保存成功
        """
        try:
            # 将比赛数据转换为列表或字典
            if hasattr(matches, 'to_dict'):  # DataFrame
                matches_dict = matches.to_dict(orient='records')
            elif all(hasattr(m, 'to_dict') for m in matches):  # Match对象列表
                matches_dict = [m.to_dict() for m in matches]
            else:  # 假设已经是字典列表
                matches_dict = matches

            # 合并比赛和赔率数据
            combined_data = []
            for match in matches_dict:
                match_id = match.get('match_id') or match.get('MatchID')
                match_with_odds = match.copy()
                match_with_odds['odds'] = odds_data.get(match_id, {})

                # 添加日志记录，显示最早开盘时间是否正确保存
                match_odds = odds_data.get(match_id, {})
                for bookmaker, odds in match_odds.items():
                    if bookmaker == '香港马会' or bookmaker == '澳门' or bookmaker == 'Coolbet' or bookmaker == 'iddaa':
                        logging.info(f"保存到JSON - 比赛ID: {match_id}, 公司: {bookmaker}")

                combined_data.append(match_with_odds)

            # 保存到JSON文件
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(combined_data, f, ensure_ascii=False, indent=4)

            logging.info(f"成功保存合并数据到 {filename}")
            return True

        except Exception as e:
            logging.error(f"保存数据到JSON时出错: {e}")
            return False

    def save_overunder_odds(self, match_id, overunder_odds_list):
        """
        保存大小球赔率数据

        Args:
            match_id: 比赛ID
            overunder_odds_list: OverUnderOdds对象列表

        Returns:
            bool: 是否保存成功
        """
        if not overunder_odds_list:
            logging.warning("没有大小球赔率数据可保存")
            return False

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        try:
            conn = self.connect()
            cursor = conn.cursor()

            # 创建大小球赔率表（使用正确的表结构）
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS overunder_odds (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id INTEGER NOT NULL,
                matchid TEXT,
                company_id TEXT,
                company_name TEXT NOT NULL,
                initial_over REAL,
                initial_line TEXT,
                initial_under REAL,
                instant_over REAL,
                instant_line TEXT,
                instant_under REAL,
                line_numeric REAL,
                update_time TEXT NOT NULL,
                crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE (match_id, company_id, update_time)
            )
            ''')

            # 删除该比赛的旧数据
            cursor.execute("DELETE FROM overunder_odds WHERE match_id = ?", (match_id,))

            # 保存新的大小球赔率数据
            for odds in overunder_odds_list:
                # 获取数值化的盘口值
                line_numeric = None
                if hasattr(odds, 'line_numeric') and odds.line_numeric is not None:
                    line_numeric = odds.line_numeric
                elif odds.instant_line:
                    line_numeric = odds.convert_line_to_numeric(odds.instant_line)
                elif odds.initial_line:
                    line_numeric = odds.convert_line_to_numeric(odds.initial_line)

                # 使用正确的列名保存数据
                cursor.execute(
                    '''INSERT INTO overunder_odds
                       (match_id, matchid, company_name, initial_over, initial_line, initial_under,
                        instant_over, instant_line, instant_under, line_numeric, update_time)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                    (
                        match_id, str(match_id), odds.bookmaker,  # bookmaker存储到company_name
                        odds.initial_over, odds.initial_line, odds.initial_under,
                        odds.instant_over, odds.instant_line, odds.instant_under,
                        line_numeric, current_time
                    )
                )

            conn.commit()
            conn.close()

            logging.info(f"成功保存比赛 {match_id} 的 {len(overunder_odds_list)} 条大小球赔率数据")
            return True

        except Exception as e:
            if conn:
                conn.rollback()
                conn.close()
            logging.error(f"保存大小球赔率数据时出错: {e}")
            return False