import logging
from .utils import match_bookmaker

class OddsAnalysisLogic:
    """负责处理赔率分析的业务逻辑"""
    
    def get_bookmaker_type(self, bookmaker, league_name):
        """
        根据联赛和博彩公司名称确定赔率类型

        Args:
            bookmaker: 博彩公司名称
            league_name: 联赛名称

        Returns:
            str: 赔率类型（基准赔率/针对赔率/保守赔率/其他）
        """
        # 获取联赛对应的赔率配置
        config = self.get_league_odds_config(league_name)

        # 检查是否为基准赔率公司
        for base_odds in config["base_odds"]:
            company_name = base_odds.split(" (")[0].strip()
            if match_bookmaker(bookmaker, company_name):
                return "基准赔率"

        # 检查是否为针对赔率公司
        for target_odds in config["target_odds"]:
            company_name = target_odds.split(" (")[0].strip()
            if match_bookmaker(bookmaker, company_name):
                return "针对赔率"

        # 检查是否为保守赔率公司
        for cons_odds in config["conservative_odds"]:
            company_name = cons_odds.split(" (")[0].strip()
            if match_bookmaker(bookmaker, company_name):
                return "保守赔率"

        # 默认为其他
        return "其他"

    def get_league_odds_config(self, league_name):
        """
        获取指定联赛的赔率类型配置

        Args:
            league_name: 联赛名称

        Returns:
            dict: 赔率类型配置
        """
        # 标准基准赔率: 马会、iddaa、BetISn（移除澳门）
        base_odds = ["香港马会", "iddaa", "BetISn"]
        
        # 南美地区联赛 - 添加伟德为基准赔率
        south_american_leagues = ["巴西甲", "巴西乙", "阿根廷甲", "阿根廷乙", "智利甲", "智利乙"]
        is_south_american = any(league in league_name for league in south_american_leagues)
        
        # 东欧和亚洲地区联赛 - 添加18bet为基准赔率
        east_european_asian_leagues = ["俄超", "俄甲", "乌克兰", "波兰", "奥地利", "韩K", "韩K2"]
        is_east_european_asian = any(league in league_name for league in east_european_asian_leagues)
        
        # 根据联赛区域动态添加特定的基准赔率公司
        if is_south_american:
            base_odds.append("伟德")  # 为南美联赛添加伟德作为基准赔率
            logging.info(f"南美联赛 {league_name} - 添加伟德为基准赔率公司")
        
        if is_east_european_asian:
            base_odds.append("18BET")  # 为东欧和亚洲联赛添加18BET作为基准赔率
            logging.info(f"东欧/亚洲联赛 {league_name} - 添加18BET为基准赔率公司")

        # 针对赔率公司 - 伟德保持在这里以确保即时赔率也显示
        target_odds = ["易胜博 (即赔)", "BWIN (即赔)", "伟德 (即赔)"]

        # 保守赔率公司 - 添加马会和BetISN的即时赔率
        conservative_odds = ["威廉 (即赔)", "香港马会 (即赔)", "BetISn (即赔)"]

        # 返回配置
        return {
            "base_odds": base_odds,
            "target_odds": target_odds,
            "conservative_odds": conservative_odds
        }