2025-08-03 14:02:28,152 - root - INFO - 成功获取比赛ID 2696009 的赔率数据
2025-08-03 14:17:06,357 - root - INFO - ✅ 记录ID表创建成功
2025-08-03 14:50:03,174 - FootballCrawler - DEBUG - 请求URL: https://1x2d.titan007.com/2696009.js?r=0071754203803174&t=1754203803174
2025-08-03 14:50:03,176 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): 1x2d.titan007.com:443
2025-08-03 14:50:03,450 - urllib3.connectionpool - DEBUG - https://1x2d.titan007.com:443 "GET /2696009.js?r=0071754203803174&t=1754203803174 HTTP/1.1" 200 6090
2025-08-03 14:50:03,781 - FootballCrawler - DEBUG - 成功获取数据，响应长度: 25945 字符
2025-08-03 14:50:03,782 - root - INFO - 成功获取比赛ID 2696009 的赔率数据
2025-08-03 14:50:59,863 - root - INFO - 成功获取比赛ID 2696009 的赔率数据
2025-08-03 14:53:24,640 - root - INFO - 比赛 2696009 的历史赔率保存完成: 69 条记录
2025-08-03 14:57:48,394 - root - INFO - 比赛 2696009 的历史赔率保存完成: 9 条记录
2025-08-03 15:02:24,106 - root - INFO - 比赛 2696009 的历史赔率保存完成: 9 条记录
2025-08-03 21:39:21,306 - FootballCrawler - DEBUG - 请求URL: https://1x2d.titan007.com/2696009.js?r=0071754228361306&t=1754228361306
2025-08-03 21:39:21,308 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): 1x2d.titan007.com:443
2025-08-03 21:39:21,650 - urllib3.connectionpool - DEBUG - https://1x2d.titan007.com:443 "GET /2696009.js?r=0071754228361306&t=1754228361306 HTTP/1.1" 200 8931
2025-08-03 21:39:21,769 - FootballCrawler - DEBUG - 成功获取数据，响应长度: 25945 字符
2025-08-03 21:39:21,770 - root - INFO - 成功获取比赛ID 2696009 的赔率数据
2025-08-03 21:47:15,057 - FootballCrawler - DEBUG - 请求URL: https://1x2d.titan007.com/2696009.js?r=0071754228835057&t=1754228835057
2025-08-03 21:47:15,059 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): 1x2d.titan007.com:443
2025-08-03 21:47:16,130 - urllib3.connectionpool - DEBUG - https://1x2d.titan007.com:443 "GET /2696009.js?r=0071754228835057&t=1754228835057 HTTP/1.1" 200 6090
2025-08-03 21:47:16,412 - FootballCrawler - DEBUG - 成功获取数据，响应长度: 25945 字符
2025-08-03 21:47:16,413 - root - INFO - 成功获取比赛ID 2696009 的赔率数据
