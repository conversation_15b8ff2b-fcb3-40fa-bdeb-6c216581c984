"""
足球赔率爬虫模块 - 包含主爬虫类和命令行入口
此文件整合了原来分散在不同文件中的功能，避免循环导入问题
"""

import os
import sys
import time
import sqlite3
import logging
import traceback
import threading
import queue
import random
import argparse
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

# 从scrapers的配置模块中导入常量
from scrapers.config import LEAGUES, TARGET_COMPANIES, DB_FILE, logger, DEFAULT_CONFIG
from scrapers.database import DatabaseManager
from scrapers.crawler import ThreadSafeDict, CrawlerUtils

# 导出所有可能被外部引用的内容
__all__ = ['FootballCrawler', 'main', 'LEAGUES', 'TARGET_COMPANIES', 'DB_FILE', 'logger']


class FootballCrawler:
    def __init__(self):
        """初始化爬虫"""
        # 配置参数
        self.config = DEFAULT_CONFIG.copy()
        
        # 创建带有重试机制的会话
        self.session = CrawlerUtils.create_session(self.config["retry_times"])
        
        # 线程安全的共享状态
        self.shared_state = ThreadSafeDict()
        
        # 数据队列，用于线程间通信
        self.data_queue = queue.Queue()
        
        # 停止事件信号
        self.stop_event = threading.Event()
        
        # 进度条
        self.progress_bar = None
        
        # 目标联赛设置
        self.target_leagues = LEAGUES
        
        logger.info(f"数据库路径: {DB_FILE}")
        logger.info(f"脚本目录: {os.path.dirname(os.path.abspath(__file__))}")

    def process_match_odds(self, match_data):
        """处理单场比赛的赔率数据"""
        match_id, matchid = match_data["match_id"], match_data["matchid"]
        
        try:
            logger.debug(f"爬取比赛 {matchid} 的赔率数据")
            
            # 获取赔率数据
            odds_js = CrawlerUtils.fetch_odds_data(
                session=self.session,
                matchid=matchid,
                min_delay=self.config["min_delay"],
                max_delay=self.config["max_delay"],
                timeout=self.config["request_timeout"]
            )
            if not odds_js:
                self.data_queue.put({
                    'type': 'match_result',
                    'matchid': matchid,
                    'match_id': match_id,
                    'status': 'failed',
                    'error_message': '获取赔率数据失败',
                    'odds_data': []
                })
                return
            
            # 解析赔率数据
            odds_data = CrawlerUtils.parse_odds_data(odds_js, matchid)
            if not odds_data or 'odds_list' not in odds_data or not odds_data['odds_list']:
                logger.warning(f"比赛 {matchid} 无目标公司赔率数据")
                self.data_queue.put({
                    'type': 'match_result',
                    'matchid': matchid,
                    'match_id': match_id,
                    'status': 'no_data',
                    'error_message': '无目标公司赔率数据',
                    'odds_data': []
                })
                return
            
            # 输出所有公司ID日志，检查新增公司是否存在
            company_ids = [odds['company_id'] for odds in odds_data['odds_list']]
            logger.info(f"比赛 {matchid} 获取到的赔率公司ID: {company_ids}")
            target_companies = set(TARGET_COMPANIES.keys())
            found_companies = set(company_ids)
            missing_companies = target_companies - found_companies
            extra_companies = found_companies - target_companies
            if missing_companies:
                logger.warning(f"比赛 {matchid} 缺少以下目标公司的赔率数据: {missing_companies}")
            if extra_companies:
                logger.info(f"比赛 {matchid} 有额外的非目标公司赔率数据: {extra_companies}")
            
            # 准备批量插入数据
            batch_data = []
            for odds in odds_data['odds_list']:
                try:
                    row = {
                        'match_id': match_id,
                        'matchid': matchid,
                        'company_id': odds['company_id'],
                        'company_odds_id': odds['company_odds_id'],
                        'init_home_win': float(odds['init_home_win']),
                        'init_draw': float(odds['init_draw']),
                        'init_away_win': float(odds['init_away_win']),
                        'init_home_win_rate': float(odds['init_home_win_rate']),
                        'init_draw_rate': float(odds['init_draw_rate']),
                        'init_away_win_rate': float(odds['init_away_win_rate']),
                        'init_return_rate': float(odds['init_return_rate']),
                        'real_home_win': float(odds['real_home_win']),
                        'real_draw': float(odds['real_draw']),
                        'real_away_win': float(odds['real_away_win']),
                        'real_home_win_rate': float(odds['real_home_win_rate']),
                        'real_draw_rate': float(odds['real_draw_rate']),
                        'real_away_win_rate': float(odds['real_away_win_rate']),
                        'real_return_rate': float(odds['real_return_rate']),
                        'k_home': float(odds['k_home']),
                        'k_draw': float(odds['k_draw']),
                        'k_away': float(odds['k_away']),
                        'update_time': odds['update_time']
                    }
                    batch_data.append(row)
                except (ValueError, KeyError) as e:
                    logger.error(f"处理赔率数据失败: {e}")
            
            # 将结果放入队列
            self.data_queue.put({
                'type': 'match_result',
                'matchid': matchid,
                'match_id': match_id,
                'status': 'success',
                'error_message': None,
                'odds_data': batch_data
            })
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"处理比赛 {matchid} 时出错: {error_msg}")
            self.data_queue.put({
                'type': 'match_result',
                'matchid': matchid,
                'match_id': match_id,
                'status': 'failed',
                'error_message': error_msg,
                'odds_data': []
            })
    
    def db_worker(self):
        """数据库工作线程 - 处理数据并更新数据库"""
        try:
            # 为这个线程创建专用数据库连接
            db_conn = sqlite3.connect(DB_FILE)
            db_conn.row_factory = sqlite3.Row
            
            # 初始化状态
            processed_count = 0
            total_odds = 0
            total_new_odds = 0
            total_updated_odds = 0
            accumulated_data = []
            
            # 循环处理队列中的数据
            while not self.stop_event.is_set() or not self.data_queue.empty():
                try:
                    # 尝试从队列获取数据，但最多等待1秒
                    data = self.data_queue.get(timeout=1)
                    
                    if data['type'] == 'match_result':
                        matchid = data['matchid']
                        match_id = data['match_id']
                        
                        # 更新matches表中的odds_updated状态
                        if data['status'] == 'success':
                            cursor = db_conn.cursor()
                            cursor.execute("""
                            UPDATE matches SET odds_updated = 1 
                            WHERE matchid = ?
                            """, (matchid,))
                            
                            processed_count += 1
                        
                        # 累积插入数据
                        if data['status'] == 'success' and data['odds_data']:
                            accumulated_data.extend(data['odds_data'])
                        
                        # 达到批处理大小或队列为空时执行批量操作
                        if (len(accumulated_data) >= self.config["db_batch_size"] or 
                            (self.data_queue.empty() and accumulated_data)):
                            
                            # 批量插入赔率数据
                            if accumulated_data:
                                result = DatabaseManager.batch_insert_odds(db_conn, accumulated_data)
                                if isinstance(result, tuple) and len(result) == 2:
                                    # 如果返回值是一个元组(新插入数量, 更新数量)
                                    new_odds, updated_odds = result
                                    total_new_odds += new_odds
                                    total_updated_odds += updated_odds
                                    total_odds += (new_odds + updated_odds)
                                else:
                                    # 兼容旧的返回值格式
                                    total_odds += result
                                accumulated_data = []
                        
                        # 更新进度条
                        if self.progress_bar:
                            self.progress_bar.update(1)
                    
                    # 标记队列任务完成
                    self.data_queue.task_done()
                    
                except queue.Empty:
                    # 队列暂时为空，等待新数据
                    continue
                except Exception as e:
                    logger.error(f"数据库工作线程处理数据出错: {e}")
                    # 继续处理下一条数据
                    if not self.data_queue.empty():
                        self.data_queue.task_done()
            
            # 处理最后剩余的数据
            if accumulated_data:
                result = DatabaseManager.batch_insert_odds(db_conn, accumulated_data)
                if isinstance(result, tuple) and len(result) == 2:
                    # 如果返回值是一个元组(新插入数量, 更新数量)
                    new_odds, updated_odds = result
                    total_new_odds += new_odds
                    total_updated_odds += updated_odds
                    total_odds += (new_odds + updated_odds)
                else:
                    # 兼容旧的返回值格式
                    total_odds += result
            
            # 提交所有更改
            db_conn.commit()
            
            # 更新共享状态
            self.shared_state.set('processed_count', processed_count)
            self.shared_state.set('total_odds', total_odds)
            self.shared_state.set('total_new_odds', total_new_odds)
            self.shared_state.set('total_updated_odds', total_updated_odds)
            
            logger.info(f"数据库工作线程完成: 处理了 {processed_count} 场比赛，插入了 {total_new_odds} 条新赔率记录，更新了 {total_updated_odds} 条赔率记录")
            
        except Exception as e:
            logger.error(f"数据库工作线程发生异常: {e}")
        finally:
            # 关闭数据库连接
            if 'db_conn' in locals() and db_conn:
                db_conn.close()
    
    def need_update_league(self, league_id, league_name):
        """判断联赛是否需要更新"""
        # 如果设置了强制更新，则总是返回True
        if self.shared_state.get('force_update', False):
            logger.info(f"强制更新模式: 联赛 {league_name} (ID={league_id}) 将被更新")
            return True
            
        try:
            with sqlite3.connect(DB_FILE) as conn:
                cursor = conn.cursor()
                
                # 检查matches表是否有update_time列
                cursor.execute("PRAGMA table_info(matches)")
                columns = cursor.fetchall()
                column_names = [column[1] for column in columns]
                has_update_time = 'update_time' in column_names
                
                # 检查该联赛是否已存在于数据库
                cursor.execute("SELECT COUNT(*) FROM matches WHERE league_id = ?", (league_id,))
                match_count = cursor.fetchone()[0]
                
                if match_count == 0:
                    logger.info(f"联赛 {league_name} (ID={league_id}) 在数据库中无数据，需要更新")
                    return True
                
                # 检查该联赛的最后更新时间
                last_update = DatabaseManager.get_league_last_update(conn, league_id)
                if not last_update:
                    logger.info(f"联赛 {league_name} (ID={league_id}) 未找到更新时间，需要更新")
                    return True
                
                # 计算最后更新时间距今多少天
                try:
                    last_update_date = datetime.strptime(last_update, '%Y-%m-%d %H:%M:%S.%f')
                except ValueError:
                    try:
                        last_update_date = datetime.strptime(last_update, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        logger.warning(f"无法解析更新时间 {last_update}，需要更新")
                        return True
                
                now = datetime.now()
                days_since_update = (now - last_update_date).days
                
                # 如果超过1天没更新，则需要更新
                if days_since_update >= 1:
                    logger.info(f"联赛 {league_name} (ID={league_id}) 已 {days_since_update} 天未更新，需要更新")
                    return True
                
                # 检查是否有未完成的比赛
                cursor.execute("""
                SELECT COUNT(*) FROM matches 
                WHERE league_id = ? AND match_status != -1 AND match_time < datetime('now')
                """, (league_id,))
                unfinished_count = cursor.fetchone()[0]
                
                if unfinished_count > 0:
                    logger.info(f"联赛 {league_name} (ID={league_id}) 有 {unfinished_count} 场未完成的已过期比赛，需要更新")
                    return True
                
                # 检查是否有未更新赔率的比赛
                if 'odds_updated' in column_names:
                    cursor.execute("""
                    SELECT COUNT(*) FROM matches 
                    WHERE league_id = ? AND match_status = -1 
                    AND (odds_updated = 0 OR odds_updated IS NULL)
                    """, (league_id,))
                    no_odds_count = cursor.fetchone()[0]
                    
                    if no_odds_count > 0:
                        logger.info(f"联赛 {league_name} (ID={league_id}) 有 {no_odds_count} 场比赛未更新赔率，需要更新")
                        return True
                
                logger.info(f"联赛 {league_name} (ID={league_id}) 数据是最新的，不需要更新")
                return False
                
        except Exception as e:
            logger.error(f"检查联赛 {league_name} (ID={league_id}) 是否需要更新时出错: {e}")
            # 出错时默认需要更新
            return True
    
    def crawl_league(self, league_code, league_info):
        """爬取单个联赛的比赛数据"""
        try:
            league_name = league_info['name']
            league_url = league_info['url']
            
            logger.info(f"正在爬取 {league_name} 联赛数据...")
            logger.debug(f"使用URL: {league_url}")
            
            # 获取JavaScript数据
            js_content = CrawlerUtils.fetch_data(
                session=self.session,
                url=league_url,
                min_delay=self.config["min_delay"],
                max_delay=self.config["max_delay"],
                timeout=self.config["request_timeout"],
                retry_times=self.config["retry_times"]
            )
            if not js_content:
                logger.error(f"{league_name} 数据获取失败，跳过此联赛")
                return False
            
            # 解析JavaScript数据
            parsed_data = CrawlerUtils.parse_js_data(js_content)
            if not parsed_data or 'league' not in parsed_data or 'teams' not in parsed_data:
                logger.error(f"{league_name} 核心数据解析失败，跳过此联赛")
                return False
            
            # 添加联赛代码和名称到解析后的数据中
            parsed_data['league_code'] = league_code
            parsed_data['league_name'] = league_name
            
            # 验证解析结果
            match_count = sum(len(matches) for matches in parsed_data['matches'].values())
            logger.info(f"\n{league_name} 解析成功汇总:")
            logger.info(f"- 联赛ID: {parsed_data['league'][0]}")
            logger.info(f"- 联赛代码: {league_code}")
            logger.info(f"- 球队数量: {len(parsed_data['teams'])}")
            logger.info(f"- 轮次数: {len(parsed_data['matches'])}")
            logger.info(f"- 比赛总数: {match_count}")
            
            # 连接数据库并插入数据
            with sqlite3.connect(DB_FILE) as conn:
                DatabaseManager.insert_match_data(conn, parsed_data)
            
            logger.info(f"{league_name} 数据已成功导入数据库")
            return True
            
        except Exception as e:
            logger.error(f"爬取 {league_info['name']} 联赛时发生错误: {e}")
            traceback.print_exc()
            return False
    
    def crawl_all_leagues(self):
        """增量爬取需要更新的联赛比赛数据"""
        logger.info("===== 开始增量爬取足球比赛数据 =====")
        
        # 创建数据库
        DatabaseManager.create_database()
        
        # 跟踪成功和失败的联赛
        successful_leagues = []
        failed_leagues = []
        skipped_leagues = []
        
        # 检查每个联赛是否需要更新
        for league_code, league_info in self.target_leagues.items():
            league_name = league_info['name']
            # 从URL中提取联赛ID
            league_id = CrawlerUtils.get_league_id_from_url(league_info['url'])
            
            if not league_id:
                logger.warning(f"无法从URL中提取联赛ID: {league_info['url']}")
                # 无法确定是否需要更新，默认更新
                need_update = True
            else:
                # 检查该联赛是否需要更新
                need_update = self.need_update_league(league_id, league_name)
            
            if need_update:
                logger.info(f"\n===== 开始爬取 {league_name} 联赛数据 =====")
                success = self.crawl_league(league_code, league_info)
                if success:
                    successful_leagues.append(league_name)
                else:
                    failed_leagues.append(league_name)
            else:
                logger.info(f"跳过爬取 {league_name} 联赛，数据已是最新")
                skipped_leagues.append(league_name)
        
        # 显示联赛处理结果汇总
        logger.info("\n===== 联赛处理结果汇总 =====")
        logger.info(f"总联赛数: {len(self.target_leagues)}")
        logger.info(f"更新成功: {len(successful_leagues)} 个联赛")
        logger.info(f"无需更新: {len(skipped_leagues)} 个联赛")
        logger.info(f"更新失败: {len(failed_leagues)} 个联赛")
        
        if successful_leagues:
            logger.info("\n成功更新的联赛:")
            for i, league in enumerate(successful_leagues, 1):
                logger.info(f"  {i}. {league}")
        
        if skipped_leagues:
            logger.info("\n无需更新的联赛:")
            for i, league in enumerate(skipped_leagues, 1):
                logger.info(f"  {i}. {league}")
        
        if failed_leagues:
            logger.info("\n更新失败的联赛:")
            for i, league in enumerate(failed_leagues, 1):
                logger.info(f"  {i}. {league}")
        
        logger.info("\n===== 比赛数据爬取完成 =====")
    
    def crawl_odds(self, force_all=False):
        """爬取所有需要的比赛赔率数据
        
        Args:
            force_all: 是否强制爬取所有比赛的赔率，包括已爬取过的
        """
        logger.info("===== 开始爬取赔率数据 =====" + (" (强制爬取所有比赛)" if force_all else ""))
        
        try:
            # 获取需要爬取赔率的比赛
            with sqlite3.connect(DB_FILE) as conn:
                match_list = DatabaseManager.get_matches_needing_odds(conn, force_all)
            
            if not match_list:
                logger.info("没有找到需要爬取赔率的比赛")
                return
            
            logger.info(f"找到 {len(match_list)} 场需要爬取赔率的比赛")
            logger.info(f"目标博彩公司: {', '.join([info['cn'] for info in TARGET_COMPANIES.values()])}")
            logger.info(f"使用 {self.config['max_workers']} 个线程并发爬取")
            
            start_time = time.time()
            
            # 初始化共享状态
            self.shared_state.clear()
            
            # 清空数据队列
            while not self.data_queue.empty():
                self.data_queue.get()
                self.data_queue.task_done()
            
            # 重置停止事件
            self.stop_event.clear()
            
            # 创建进度条
            self.progress_bar = tqdm(total=len(match_list), desc="爬取赔率进度")
            
            # 启动数据库工作线程
            db_thread = threading.Thread(target=self.db_worker)
            db_thread.daemon = True
            db_thread.start()
            
            # 并发爬取数据
            with ThreadPoolExecutor(max_workers=self.config["max_workers"]) as executor:
                # 提交所有爬取任务
                futures = [executor.submit(self.process_match_odds, match) for match in match_list]
                
                # 等待所有任务完成
                for future in as_completed(futures):
                    try:
                        future.result()  # 获取结果，但数据已通过队列传递
                    except Exception as e:
                        logger.error(f"爬取任务异常: {e}")
            
            # 设置停止事件，通知数据库线程结束
            self.stop_event.set()
            
            # 等待数据库工作线程完成所有剩余任务
            db_thread.join(timeout=60)  # 最多等待60秒
            
            # 关闭进度条
            if self.progress_bar:
                self.progress_bar.close()
                self.progress_bar = None
            
            # 计算耗时
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            # 获取处理结果
            processed_count = self.shared_state.get('processed_count', 0)
            total_odds = self.shared_state.get('total_odds', 0)
            total_new_odds = self.shared_state.get('total_new_odds', 0)
            total_updated_odds = self.shared_state.get('total_updated_odds', 0)
            
            logger.info(f"===== 赔率数据爬取完成 =====")
            logger.info(f"成功爬取 {processed_count}/{len(match_list)} 场比赛的赔率数据")
            logger.info(f"共处理 {total_odds} 条赔率记录 (新增: {total_new_odds}, 更新: {total_updated_odds})")
            logger.info(f"总耗时: {elapsed_time:.2f} 秒")
            
        except KeyboardInterrupt:
            logger.info("爬取过程被用户中断")
            self.stop_event.set()
            if self.progress_bar:
                self.progress_bar.close()
                self.progress_bar = None
        except Exception as e:
            logger.error(f"爬取过程出错: {e}")
            self.stop_event.set()
            if self.progress_bar:
                self.progress_bar.close()
                self.progress_bar = None
    
    def run(self, crawl_matches=True, crawl_odds_data=True, force_update=False, force_odds=False):
        """运行爬虫，可选是否爬取比赛数据和赔率数据，是否强制更新
        
        Args:
            crawl_matches: 是否爬取比赛数据
            crawl_odds_data: 是否爬取赔率数据
            force_update: 是否强制更新所有联赛，忽略增量更新检查
            force_odds: 是否强制重新爬取所有比赛的赔率，包括已爬取过的
        """
        try:
            logger.info("===== 足球数据爬虫启动 =====")
            
            # 全局设置强制更新标志
            if force_update:
                logger.info("已启用强制更新模式，将忽略增量更新检查")
                self.shared_state.set('force_update', True)
            else:
                self.shared_state.set('force_update', False)
            
            # 爬取比赛数据
            if crawl_matches:
                self.crawl_all_leagues()
            else:
                logger.info("跳过爬取比赛数据")
            
            # 爬取赔率数据
            if crawl_odds_data:
                self.crawl_odds(force_all=force_odds)
            else:
                logger.info("跳过爬取赔率数据")
            
            logger.info("===== 足球数据爬虫完成 =====")
            
        except Exception as e:
            logger.error(f"爬虫运行时出错: {e}")
            traceback.print_exc()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="足球数据爬虫")
    parser.add_argument("--no-matches", action="store_true", help="不爬取比赛数据，只爬取赔率")
    parser.add_argument("--no-odds", action="store_true", help="不爬取赔率数据，只爬取比赛")
    parser.add_argument("--force-update", action="store_true", help="强制更新所有联赛数据，忽略增量更新检查")
    parser.add_argument("--force-odds", action="store_true", help="强制重新爬取所有比赛的赔率数据，包括已爬取过的")
    parser.add_argument("--leagues", type=str, help="只更新指定联赛，用逗号分隔联赛代码，例如：ENG1,ESP1,ITA1")
    parser.add_argument("--verbose", action="store_true", help="显示详细日志")
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    # 初始化爬虫
    crawler = FootballCrawler()
    
    # 如果指定了联赛代码，只更新这些联赛
    if args.leagues:
        league_codes = [code.strip() for code in args.leagues.split(',')]
        filtered_leagues = {}
        for code in league_codes:
            if code in LEAGUES:
                filtered_leagues[code] = LEAGUES[code]
            else:
                logger.warning(f"未找到联赛代码: {code}")
        
        if filtered_leagues:
            # 将过滤后的联赛设置为爬虫的属性，避免修改全局变量
            crawler.target_leagues = filtered_leagues
            
            # 运行爬虫
            crawler.run(
                crawl_matches=not args.no_matches,
                crawl_odds_data=not args.no_odds,
                force_update=args.force_update,
                force_odds=args.force_odds
            )
        else:
            logger.error("没有找到有效的联赛代码，退出")
    else:
        # 正常运行爬虫
        crawler.run(
            crawl_matches=not args.no_matches,
            crawl_odds_data=not args.no_odds,
            force_update=args.force_update,
            force_odds=args.force_odds
        )


# 为了兼容性，保留从其他模块延迟导入的函数
def get_football_crawler():
    """返回FootballCrawler类，用于兼容原有代码"""
    return FootballCrawler

def run_main():
    """运行main函数，用于兼容原有代码"""
    return main()

# 如果有代码直接运行此文件，保留原始入口点
if __name__ == "__main__":
    main() 