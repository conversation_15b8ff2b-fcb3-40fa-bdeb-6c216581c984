"""
统一数据库管理器
提供数据库连接、操作的统一接口
"""

import sqlite3
import os
from typing import Optional, Dict, Any, List
from pathlib import Path
import threading
from contextlib import contextmanager
import time
import queue
from weakref import WeakSet

from .config_manager import config_manager
from .logger_manager import get_logger
from .exception_handler import handle_exceptions, DatabaseError, safe_execute
from .validators import InputValidator, ValidationError


class ConnectionPool:
    """数据库连接池"""
    
    def __init__(self, db_path: str, max_connections: int = 10, 
                 connection_timeout: float = 30.0):
        """
        初始化连接池
        
        Args:
            db_path: 数据库路径
            max_connections: 最大连接数
            connection_timeout: 连接超时时间（秒）
        """
        self.db_path = db_path
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self._pool = queue.Queue(maxsize=max_connections)
        self._all_connections = WeakSet()  # 跟踪所有连接用于清理
        self._lock = threading.RLock()
        self._created_connections = 0
        
    def _create_connection(self) -> sqlite3.Connection:
        """创建新的数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 启用字典式访问
        
        self._all_connections.add(conn)
        return conn
    
    @contextmanager
    def get_connection(self):
        """
        获取连接池中的连接
        
        Yields:
            sqlite3.Connection: 数据库连接
        """
        conn = None
        try:
            # 尝试从池中获取连接
            try:
                conn = self._pool.get_nowait()
            except queue.Empty:
                # 如果池为空且未达到最大连接数，创建新连接
                with self._lock:
                    if self._created_connections < self.max_connections:
                        conn = self._create_connection()
                        self._created_connections += 1
                    else:
                        # 等待可用连接
                        conn = self._pool.get(timeout=self.connection_timeout)
            
            # 验证连接是否仍然有效
            try:
                conn.execute("SELECT 1")
            except sqlite3.Error:
                # 连接已损坏，创建新连接
                conn.close()
                conn = self._create_connection()
            
            yield conn
            
        except Exception as e:
            if conn:
                # 发生异常时回滚事务
                try:
                    conn.rollback()
                except sqlite3.Error:
                    pass
            raise
        finally:
            if conn:
                # 将连接返回到池中
                try:
                    self._pool.put_nowait(conn)
                except queue.Full:
                    # 池已满，关闭连接
                    conn.close()
                    with self._lock:
                        self._created_connections -= 1
    
    def close_all(self):
        """关闭所有连接"""
        with self._lock:
            # 关闭池中的连接
            while not self._pool.empty():
                try:
                    conn = self._pool.get_nowait()
                    conn.close()
                except queue.Empty:
                    break
            
            # 关闭所有其他连接
            for conn in list(self._all_connections):
                try:
                    conn.close()
                except sqlite3.Error:
                    pass
            
            self._created_connections = 0


class DatabaseManager:
    """统一数据库管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self.config = config_manager
            self.logger = get_logger('database')
            self._connection_pools = {}  # 每个数据库一个连接池
            self._pools_lock = threading.RLock()
            self._initialized = True
    
    def get_database_path(self, db_name: str) -> str:
        """获取数据库文件路径"""
        return self.config.get_database_path(db_name)
    
    def _get_connection_pool(self, db_name: str) -> ConnectionPool:
        """
        获取或创建数据库连接池
        
        Args:
            db_name: 数据库名称
            
        Returns:
            ConnectionPool: 连接池实例
        """
        with self._pools_lock:
            if db_name not in self._connection_pools:
                db_path = self.get_database_path(db_name)
                
                # 确保数据库目录存在
                os.makedirs(os.path.dirname(db_path), exist_ok=True)
                
                # 从配置获取连接池参数
                max_connections = self.config.get('database.max_connections', 10)
                connection_timeout = self.config.get('database.connection_timeout', 30.0)
                
                self._connection_pools[db_name] = ConnectionPool(
                    db_path=db_path,
                    max_connections=max_connections,
                    connection_timeout=connection_timeout
                )
                self.logger.info(f"创建数据库连接池: {db_name} (最大连接数: {max_connections})")
            
            return self._connection_pools[db_name]
    
    @contextmanager
    def get_connection(self, db_name: str):
        """
        获取数据库连接的上下文管理器
        
        Args:
            db_name: 数据库名称（在配置中定义）
        
        Yields:
            sqlite3.Connection: 数据库连接
        """
        pool = self._get_connection_pool(db_name)
        
        try:
            with pool.get_connection() as conn:
                self.logger.debug(f"从连接池获取连接: {db_name}")
                yield conn
        except Exception as e:
            self.logger.error(f"数据库连接错误 {db_name}: {e}")
            raise
    
    @handle_exceptions(fallback_value=[], reraise=True)
    def execute_query(self, db_name: str, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """
        执行查询语句
        
        Args:
            db_name: 数据库名称
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
            
        Raises:
            DatabaseError: 数据库操作失败时抛出
            ValidationError: 参数验证失败时抛出
        """
        # 验证输入参数
        db_name = InputValidator.validate_sql_identifier(db_name, "数据库名称")
        
        if not query or not isinstance(query, str):
            raise ValidationError("查询语句不能为空")
        
        if params and not isinstance(params, (tuple, list)):
            raise ValidationError("查询参数必须是元组或列表")
        
        try:
            with self.get_connection(db_name) as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                # 将结果转换为字典列表
                if cursor.description:
                    columns = [description[0] for description in cursor.description]
                    rows = cursor.fetchall()
                    return [dict(zip(columns, row)) for row in rows]
                else:
                    return []
                    
        except sqlite3.Error as e:
            raise DatabaseError(
                f"查询执行失败: {str(e)}", 
                operation="SELECT",
                context={"query": query[:100], "params": str(params)[:100]}
            )
    
    @handle_exceptions(fallback_value=0, reraise=True)
    def execute_update(self, db_name: str, query: str, params: tuple = None) -> int:
        """
        执行更新语句（INSERT, UPDATE, DELETE）
        
        Args:
            db_name: 数据库名称
            query: SQL语句
            params: 参数
            
        Returns:
            受影响的行数
            
        Raises:
            DatabaseError: 数据库操作失败时抛出
            ValidationError: 参数验证失败时抛出
        """
        # 验证输入参数
        db_name = InputValidator.validate_sql_identifier(db_name, "数据库名称")
        
        if not query or not isinstance(query, str):
            raise ValidationError("更新语句不能为空")
        
        if params and not isinstance(params, (tuple, list)):
            raise ValidationError("查询参数必须是元组或列表")
        
        try:
            with self.get_connection(db_name) as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                conn.commit()
                return cursor.rowcount
                
        except sqlite3.Error as e:
            raise DatabaseError(
                f"更新执行失败: {str(e)}", 
                operation="UPDATE",
                context={"query": query[:100], "params": str(params)[:100]}
            )
    
    def execute_many(self, db_name: str, query: str, params_list: List[tuple]) -> int:
        """
        批量执行语句
        
        Args:
            db_name: 数据库名称
            query: SQL语句
            params_list: 参数列表
            
        Returns:
            总的受影响行数
        """
        with self.get_connection(db_name) as conn:
            cursor = conn.cursor()
            cursor.executemany(query, params_list)
            conn.commit()
            return cursor.rowcount
    
    def table_exists(self, db_name: str, table_name: str) -> bool:
        """
        检查表是否存在
        
        Args:
            db_name: 数据库名称
            table_name: 表名
            
        Returns:
            表是否存在
        """
        query = """
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
        """
        
        result = self.execute_query(db_name, query, (table_name,))
        return len(result) > 0
    
    def get_table_info(self, db_name: str, table_name: str) -> List[Dict[str, Any]]:
        """
        获取表结构信息
        
        Args:
            db_name: 数据库名称
            table_name: 表名
            
        Returns:
            表结构信息
        """
        query = f"PRAGMA table_info({table_name})"
        return self.execute_query(db_name, query)
    
    def get_all_tables(self, db_name: str) -> List[str]:
        """
        获取数据库中所有表名
        
        Args:
            db_name: 数据库名称
            
        Returns:
            表名列表
        """
        query = """
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
        ORDER BY name
        """
        
        result = self.execute_query(db_name, query)
        return [row['name'] for row in result]
    
    def backup_database(self, db_name: str, backup_path: Optional[str] = None) -> str:
        """
        备份数据库
        
        Args:
            db_name: 数据库名称
            backup_path: 备份路径（可选）
            
        Returns:
            备份文件路径
        """
        import shutil
        from datetime import datetime
        
        source_path = self.get_database_path(db_name)
        
        if backup_path is None:
            backup_dir = Path(self.config.get('paths.backup_dir'))
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"{db_name}_{timestamp}.db"
            backup_path = str(backup_dir / backup_filename)
        
        try:
            shutil.copy2(source_path, backup_path)
            self.logger.info(f"数据库备份完成: {source_path} -> {backup_path}")
            return backup_path
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            raise
    
    def vacuum_database(self, db_name: str):
        """
        清理数据库，回收空间
        
        Args:
            db_name: 数据库名称
        """
        with self.get_connection(db_name) as conn:
            conn.execute("VACUUM")
            self.logger.info(f"数据库清理完成: {db_name}")
    
    def get_database_stats(self, db_name: str) -> Dict[str, Any]:
        """
        获取数据库统计信息
        
        Args:
            db_name: 数据库名称
            
        Returns:
            数据库统计信息
        """
        db_path = self.get_database_path(db_name)
        
        stats = {
            'database_name': db_name,
            'file_path': db_path,
            'file_exists': os.path.exists(db_path),
            'file_size': 0,
            'tables': [],
            'total_records': 0
        }
        
        if os.path.exists(db_path):
            stats['file_size'] = os.path.getsize(db_path)
            
            try:
                tables = self.get_all_tables(db_name)
                stats['tables'] = tables
                
                # 统计每个表的记录数
                for table in tables:
                    count_query = f"SELECT COUNT(*) as count FROM {table}"
                    result = self.execute_query(db_name, count_query)
                    table_count = result[0]['count'] if result else 0
                    stats['total_records'] += table_count
                    
            except Exception as e:
                self.logger.error(f"获取数据库统计信息失败: {e}")
        
        return stats
    
    def optimize_database(self, db_name: str):
        """
        优化数据库性能
        
        Args:
            db_name: 数据库名称
        """
        with self.get_connection(db_name) as conn:
            # 分析查询计划
            conn.execute("ANALYZE")
            
            # 重建索引
            conn.execute("REINDEX")
            
            # 清理数据库
            conn.execute("VACUUM")
            
            self.logger.info(f"数据库优化完成: {db_name}")
    
    def close_all_pools(self):
        """
        关闭所有数据库连接池
        """
        with self._pools_lock:
            for db_name, pool in self._connection_pools.items():
                try:
                    pool.close_all()
                    self.logger.info(f"关闭数据库连接池: {db_name}")
                except Exception as e:
                    self.logger.error(f"关闭连接池失败 {db_name}: {e}")
            
            self._connection_pools.clear()
    
    def get_pool_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有连接池的统计信息
        
        Returns:
            连接池统计信息
        """
        stats = {}
        with self._pools_lock:
            for db_name, pool in self._connection_pools.items():
                stats[db_name] = {
                    'max_connections': pool.max_connections,
                    'created_connections': pool._created_connections,
                    'available_connections': pool._pool.qsize(),
                    'db_path': pool.db_path
                }
        return stats


# 全局数据库管理器实例
database_manager = DatabaseManager()

# 便捷函数
def get_connection(db_name: str):
    """获取数据库连接的便捷函数"""
    return database_manager.get_connection(db_name)

def execute_query(db_name: str, query: str, params: tuple = None) -> List[Dict[str, Any]]:
    """执行查询的便捷函数"""
    return database_manager.execute_query(db_name, query, params)

def execute_update(db_name: str, query: str, params: tuple = None) -> int:
    """执行更新的便捷函数"""
    return database_manager.execute_update(db_name, query, params) 