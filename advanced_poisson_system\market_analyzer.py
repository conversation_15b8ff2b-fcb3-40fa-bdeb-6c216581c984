"""
市场分析器 - 实现基于庄家赔率的市场预期分析
根据研究文章的方法论实现赔率解析和λ参数估算
"""

import numpy as np
from scipy.stats import poisson
from scipy.optimize import fsolve, minimize
from typing import Dict, List, Optional, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

class MarketAnalyzer:
    """市场分析器 - 从庄家赔率中提取市场预期"""
    
    def __init__(self, preferred_bookmakers=None):
        """
        初始化市场分析器
        
        Args:
            preferred_bookmakers: 优先选择的博彩公司列表
        """
        # 优先选择的"精明"博彩公司（按优先级排序）- 香港马会最优先
        self.preferred_bookmakers = preferred_bookmakers or [
            '香港马会', '易胜博', 'Pinnacle', 'SBO', '澳门', 'Bet365', 'Crown', 'William Hill',
            '伟德', 'SNAI', '10Bet'
        ]
        
        # 博彩公司名称映射（处理缩写和变体）
        self.company_aliases = {
            '香港马会': ['香港马会', '香港马', '马会', 'HKJC', '港'],
            '易胜博': ['易胜博', '易胜', '易', 'EasyWin'],
            '澳门': ['澳门', '澳', 'Macau'],
            '伟德': ['伟德', '伟', 'Victor'],
            '威廉希尔': ['威廉希尔', '威廉', '威', 'William Hill', 'WillHill'],
            'Pinnacle': ['Pinnacle', 'Pin'],
            'SBO': ['SBO', 'SBOBET'],
            'Bet365': ['Bet365', '365'],
            'Crown': ['Crown', 'Crow']
        }
        
        print(f"📊 市场分析器初始化，博彩公司优先级: {self.preferred_bookmakers}")
        
        self.margin_removal_method = 'proportional'  # 默认使用比例法
    
    def select_best_odds(self, odds_data, odds_type='overunder'):
        """
        选择最佳的赔率数据
        
        Args:
            odds_data: 赔率数据列表
            odds_type: 赔率类型 ('overunder' 或 '1x2')
            
        Returns:
            dict: 选中的赔率数据
        """
        if not odds_data:
            print(f"❌ 没有{odds_type}赔率数据可供选择")
            return None
        
        print(f"🔍 选择最佳{odds_type}赔率，可用博彩公司: {[odds['company_name'] for odds in odds_data]}")
        
        # 按优先级选择博彩公司，使用别名匹配
        for preferred in self.preferred_bookmakers:
            # 获取该优先公司的所有别名
            aliases = self.company_aliases.get(preferred, [preferred])
            
            for odds in odds_data:
                company_name = odds['company_name']
                
                # 检查是否匹配任何别名
                for alias in aliases:
                    if alias.lower() in company_name.lower() or company_name.lower() in alias.lower():
                        print(f"✅ 选择优先博彩公司: {company_name} (匹配: {preferred} -> {alias})")
                        return odds
        
        # 如果没有找到优先的，选择第一个有效的
        selected_odds = odds_data[0] if odds_data else None
        if selected_odds:
            print(f"🔄 未找到优先博彩公司，默认选择: {selected_odds['company_name']}")
        
        return selected_odds
    
    def calculate_implied_probabilities(self, odds_values):
        """
        计算隐含概率
        
        Args:
            odds_values: 赔率值列表
            
        Returns:
            list: 隐含概率列表
        """
        try:
            implied_probs = []
            for odds in odds_values:
                if odds > 0:
                    implied_probs.append(1.0 / odds)
                else:
                    implied_probs.append(0.0)
            return implied_probs
        except Exception:
            return [0.33] * len(odds_values)  # 默认均等概率
    
    def remove_bookmaker_margin(self, implied_probabilities, method='proportional'):
        """
        剔除庄家利润，获得公平概率
        
        Args:
            implied_probabilities: 隐含概率列表
            method: 剔除方法 ('proportional', 'additive', 'multiplicative', 'shin', 'odds_ratio')
            
        Returns:
            list: 公平概率列表
        """
        if not implied_probabilities or sum(implied_probabilities) == 0:
            return implied_probabilities
        
        if method == 'proportional':
            # 比例法（标准化）- 文档推荐的基础方法
            total_prob = sum(implied_probabilities)
            return [p / total_prob for p in implied_probabilities]
        
        elif method == 'additive':
            # 加法法 - 均匀减去利润
            overround = sum(implied_probabilities) - 1.0
            margin_per_outcome = overround / len(implied_probabilities)
            return [max(0, p - margin_per_outcome) for p in implied_probabilities]
        
        elif method == 'multiplicative':
            # 乘法法 - 等比例缩放
            overround = sum(implied_probabilities)
            factor = 1.0 / overround
            return [p * factor for p in implied_probabilities]
        
        elif method == 'shin':
            # 辛氏调整法 (Shin's Method) - 进阶算法1
            return self._shin_method(implied_probabilities)
        
        elif method == 'odds_ratio':
            # 对数几率法 (Odds Ratio Method) - 进阶算法2
            return self._odds_ratio_method(implied_probabilities)
        
        else:
            return implied_probabilities
    
    def _shin_method(self, implied_probabilities, max_iterations=100, tolerance=1e-8):
        """
        辛氏调整法 - 考虑内幕交易风险的进阶庄家利润剔除方法
        
        Shin, H.S. (1992) "Prices of State Contingent Claims with Insider Traders, 
        and the Favourite-Longshot Bias"
        
        Args:
            implied_probabilities: 隐含概率列表
            max_iterations: 最大迭代次数
            tolerance: 收敛容差
            
        Returns:
            list: 调整后的公平概率
        """
        try:
            import numpy as np
            from scipy.optimize import fsolve
            
            n = len(implied_probabilities)
            if n < 2:
                return implied_probabilities
            
            # 将隐含概率转换为赔率
            odds = [1/p if p > 0 else float('inf') for p in implied_probabilities]
            
            # 计算总利润率
            overround = sum(implied_probabilities) - 1.0
            
            if overround <= 0:
                return implied_probabilities  # 没有利润需要剔除
            
            def shin_equations(z):
                """
                辛氏方程组
                z: 内幕交易者比例参数
                """
                if z <= 0 or z >= 1:
                    return 1000  # 惩罚无效值
                
                # 计算调整后的概率
                adjusted_probs = []
                for i, odd in enumerate(odds):
                    if odd == float('inf'):
                        adjusted_probs.append(0)
                    else:
                        # Shin公式
                        numerator = (np.sqrt(z**2 + 4*(1-z)/odd) - z)
                        denominator = 2*(1-z)
                        adj_prob = numerator / denominator
                        adjusted_probs.append(adj_prob)
                
                # 约束：调整后概率之和应为1
                return sum(adjusted_probs) - 1.0
            
            # 求解z参数
            try:
                z_solution = fsolve(shin_equations, 0.1)[0]
                
                # 计算最终的调整概率
                if 0 < z_solution < 1:
                    final_probs = []
                    for odd in odds:
                        if odd == float('inf'):
                            final_probs.append(0)
                        else:
                            numerator = (np.sqrt(z_solution**2 + 4*(1-z_solution)/odd) - z_solution)
                            denominator = 2*(1-z_solution)
                            final_probs.append(max(0, numerator / denominator))
                    
                    # 标准化确保和为1
                    total = sum(final_probs)
                    if total > 0:
                        return [p / total for p in final_probs]
                
            except:
                pass  # 如果求解失败，回退到比例法
            
            # 如果Shin方法失败，回退到比例法
            total_prob = sum(implied_probabilities)
            return [p / total_prob for p in implied_probabilities]
            
        except ImportError:
            # 如果缺少numpy，回退到比例法
            total_prob = sum(implied_probabilities)
            return [p / total_prob for p in implied_probabilities]
    
    def _odds_ratio_method(self, implied_probabilities):
        """
        对数几率法 - 在对数空间中处理庄家利润
        
        更适合处理极端赔率差异的情况
        
        Args:
            implied_probabilities: 隐含概率列表
            
        Returns:
            list: 调整后的公平概率
        """
        try:
            import numpy as np
            
            # 避免0概率导致的对数问题
            min_prob = 1e-6
            probs = [max(p, min_prob) for p in implied_probabilities]
            
            # 转换到对数几率空间
            log_odds = [np.log(p / (1 - p)) for p in probs]
            
            # 计算调整因子
            total_prob = sum(probs)
            overround = total_prob - 1.0
            
            if overround <= 0:
                return implied_probabilities
            
            # 在对数几率空间中均匀调整
            adjustment = overround / len(probs)
            adjusted_log_odds = [lo - adjustment for lo in log_odds]
            
            # 转换回概率空间
            adjusted_probs = []
            for alo in adjusted_log_odds:
                prob = np.exp(alo) / (1 + np.exp(alo))
                adjusted_probs.append(max(min_prob, min(1-min_prob, prob)))
            
            # 标准化确保和为1
            total = sum(adjusted_probs)
            if total > 0:
                return [p / total for p in adjusted_probs]
            else:
                # 如果失败，回退到比例法
                total_prob = sum(implied_probabilities)
                return [p / total_prob for p in implied_probabilities]
                
        except (ImportError, ZeroDivisionError, OverflowError):
            # 如果计算失败，回退到比例法
            total_prob = sum(implied_probabilities)
            return [p / total_prob for p in implied_probabilities]
    
    def detect_margin_bias(self, implied_probabilities):
        """
        检测热门-冷门偏差 (Favourite-Longshot Bias)
        
        Args:
            implied_probabilities: 隐含概率列表
            
        Returns:
            dict: 偏差分析结果
        """
        if len(implied_probabilities) < 2:
            return {'bias_detected': False, 'recommendation': 'proportional'}
        
        # 确保概率值都是数值类型
        try:
            numeric_probs = []
            for prob in implied_probabilities:
                if isinstance(prob, str):
                    numeric_probs.append(float(prob))
                else:
                    numeric_probs.append(float(prob))
            implied_probabilities = numeric_probs
        except (ValueError, TypeError) as e:
            print(f"⚠️ 概率值类型转换失败: {e}, 原始值: {implied_probabilities}")
            return {'bias_detected': False, 'recommendation': 'proportional'}
        
        # 计算概率范围
        min_prob = min(implied_probabilities)
        max_prob = max(implied_probabilities) 
        prob_range = max_prob - min_prob
        
        # 计算利润率
        overround = sum(implied_probabilities) - 1.0
        
        analysis = {
            'overround': overround,
            'prob_range': prob_range,
            'min_prob': min_prob,
            'max_prob': max_prob,
            'bias_detected': False,
            'bias_type': None,
            'recommendation': 'proportional'
        }
        
        # 检测偏差
        if prob_range > 0.4 and overround > 0.05:  # 概率差异大且利润率高
            analysis['bias_detected'] = True
            
            if min_prob < 0.15:  # 存在长线选项
                analysis['bias_type'] = 'longshot_bias'
                analysis['recommendation'] = 'shin'  # 推荐Shin方法
            elif prob_range > 0.6:  # 极端概率差异
                analysis['bias_type'] = 'extreme_odds'
                analysis['recommendation'] = 'odds_ratio'  # 推荐对数几率法
            else:
                analysis['bias_type'] = 'general_bias'
                analysis['recommendation'] = 'shin'
        
        return analysis
    
    def estimate_total_lambda_from_overunder(self, under_probability, line=2.5, max_iterations=100):
        """
        从大小球"小球"概率估算总λ值
        
        Args:
            under_probability: 小球公平概率
            line: 盘口线（如2.5）
            max_iterations: 最大迭代次数
            
        Returns:
            float: 总预期进球数λ
        """
        try:
            def objective(lambda_total):
                # P(总进球 ≤ line) = under_probability
                if lambda_total <= 0:
                    return 1000  # 惩罚负值
                cumulative_prob = poisson.cdf(int(line), lambda_total)
                return cumulative_prob - under_probability
            
            # 使用数值求解
            initial_guess = 2.5  # 初始猜测
            solution = fsolve(objective, initial_guess, maxfev=max_iterations)
            
            lambda_total = solution[0]
            
            # 确保结果合理
            if lambda_total <= 0 or lambda_total > 10:
                # 如果结果不合理，使用二分法备用
                lambda_total = self._binary_search_lambda(under_probability, line)
            
            return max(0.1, min(10.0, lambda_total))  # 限制在合理范围内
            
        except Exception as e:
            print(f"λ估算失败，使用默认值: {e}")
            return 2.5  # 默认值
    
    def _binary_search_lambda(self, target_prob, line, tolerance=1e-6):
        """
        二分法搜索λ值的备用方法
        """
        low, high = 0.1, 10.0
        
        for _ in range(100):  # 最大100次迭代
            mid = (low + high) / 2
            calc_prob = poisson.cdf(int(line), mid)
            
            if abs(calc_prob - target_prob) < tolerance:
                return mid
            elif calc_prob < target_prob:
                low = mid
            else:
                high = mid
        
        return (low + high) / 2
    
    def allocate_team_lambdas(self, lambda_total, fair_1x2_probabilities, max_iterations=1000):
        """
        将总λ分配给主客队（迭代匹配法）
        
        Args:
            lambda_total: 总预期进球数
            fair_1x2_probabilities: 公平的胜平负概率 [主胜, 平局, 客胜]
            max_iterations: 最大迭代次数
            
        Returns:
            tuple: (λ_home, λ_away)
        """
        if not fair_1x2_probabilities or len(fair_1x2_probabilities) != 3:
            # 如果没有1x2概率，使用简单分配
            return lambda_total * 0.55, lambda_total * 0.45
        
        try:
            def objective(params):
                lambda_home = params[0]
                lambda_away = lambda_total - lambda_home
                
                # 确保λ值为正
                if lambda_home <= 0.01 or lambda_away <= 0.01:
                    return 10000
                
                # 计算理论胜平负概率
                model_probs = self._calculate_theoretical_1x2(lambda_home, lambda_away)
                
                # 计算与市场概率的均方误差
                mse = sum((model_probs[i] - fair_1x2_probabilities[i])**2 for i in range(3))
                return mse
            
            # 优化搜索
            bounds = [(0.01, lambda_total - 0.01)]
            initial_guess = [lambda_total * 0.55]  # 主队通常稍强
            
            result = minimize(
                objective, 
                initial_guess, 
                bounds=bounds,
                method='L-BFGS-B',
                options={'maxiter': max_iterations}
            )
            
            if result.success:
                lambda_home = result.x[0]
                lambda_away = lambda_total - lambda_home
                return lambda_home, lambda_away
            else:
                # 优化失败，使用默认分配
                return self._default_lambda_allocation(lambda_total, fair_1x2_probabilities)
                
        except Exception as e:
            print(f"λ分配失败，使用默认方法: {e}")
            return self._default_lambda_allocation(lambda_total, fair_1x2_probabilities)
    
    def _default_lambda_allocation(self, lambda_total, fair_1x2_probabilities):
        """
        默认的λ分配方法
        """
        if len(fair_1x2_probabilities) == 3:
            home_prob, draw_prob, away_prob = fair_1x2_probabilities
            
            # 基于胜负概率的简单分配
            total_win_prob = home_prob + away_prob
            if total_win_prob > 0:
                home_ratio = home_prob / total_win_prob
                away_ratio = away_prob / total_win_prob
                
                # 调整比例，平局意味着两队实力接近
                if draw_prob > 0.3:  # 平局概率高，两队实力接近
                    home_ratio = 0.5 + (home_ratio - 0.5) * 0.7
                    away_ratio = 1 - home_ratio
                
                return lambda_total * home_ratio, lambda_total * away_ratio
        
        # 默认分配
        return lambda_total * 0.55, lambda_total * 0.45
    
    def _calculate_theoretical_1x2(self, lambda_home, lambda_away, max_goals=6):
        """
        计算理论胜平负概率
        
        Args:
            lambda_home: 主队预期进球数
            lambda_away: 客队预期进球数
            max_goals: 最大进球数
            
        Returns:
            list: [主胜概率, 平局概率, 客胜概率]
        """
        home_win, draw, away_win = 0.0, 0.0, 0.0
        
        for h in range(max_goals + 1):
            for a in range(max_goals + 1):
                # 计算具体比分概率
                prob_h = poisson.pmf(h, lambda_home)
                prob_a = poisson.pmf(a, lambda_away)
                score_prob = prob_h * prob_a
                
                # 分类到胜平负
                if h > a:
                    home_win += score_prob
                elif h == a:
                    draw += score_prob
                else:
                    away_win += score_prob
        
        return [home_win, draw, away_win]
    
    def analyze_overunder_market(self, overunder_odds_data, line=2.5):
        """
        分析大小球市场
        
        Args:
            overunder_odds_data: 大小球赔率数据
            line: 盘口线
            
        Returns:
            dict: 市场分析结果
        """
        # 选择最佳赔率
        best_odds = self.select_best_odds(overunder_odds_data, 'overunder')
        
        if not best_odds:
            return {
                'success': False,
                'error': '没有可用的大小球赔率数据',
                'lambda_total': 2.5  # 默认值
            }
        
        try:
            # 提取赔率
            over_odds = best_odds['over_odds']
            under_odds = best_odds['under_odds']
            
            # 计算隐含概率
            implied_probs = self.calculate_implied_probabilities([over_odds, under_odds])
            
            # 剔除庄家利润
            fair_probs = self.remove_bookmaker_margin(implied_probs)
            over_prob, under_prob = fair_probs
            
            # 估算总λ
            lambda_total = self.estimate_total_lambda_from_overunder(under_prob, line)
            
            return {
                'success': True,
                'selected_bookmaker': best_odds['company_name'],
                'original_odds': {'over': over_odds, 'under': under_odds},
                'implied_probabilities': {'over': implied_probs[0], 'under': implied_probs[1]},
                'fair_probabilities': {'over': over_prob, 'under': under_prob},
                'overround': sum(implied_probs) - 1.0,
                'lambda_total': lambda_total,
                'line': line
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'大小球分析失败: {e}',
                'lambda_total': 2.5
            }
    
    def analyze_1x2_market(self, odds_1x2_data):
        """
        分析胜平负市场
        
        Args:
            odds_1x2_data: 胜平负赔率数据
            
        Returns:
            dict: 市场分析结果
        """
        # 选择最佳赔率
        best_odds = self.select_best_odds(odds_1x2_data, '1x2')
        
        if not best_odds:
            return {
                'success': False,
                'error': '没有可用的胜平负赔率数据',
                'fair_probabilities': [0.45, 0.30, 0.25]  # 默认值
            }
        
        try:
            # 提取赔率
            home_odds = best_odds['home_win_odds']
            draw_odds = best_odds['draw_odds']
            away_odds = best_odds['away_win_odds']
            
            # 计算隐含概率
            implied_probs = self.calculate_implied_probabilities([home_odds, draw_odds, away_odds])
            
            # 剔除庄家利润
            fair_probs = self.remove_bookmaker_margin(implied_probs)
            
            return {
                'success': True,
                'selected_bookmaker': best_odds['company_name'],
                'original_odds': {'home': home_odds, 'draw': draw_odds, 'away': away_odds},
                'implied_probabilities': {'home': implied_probs[0], 'draw': implied_probs[1], 'away': implied_probs[2]},
                'fair_probabilities': fair_probs,
                'overround': sum(implied_probs) - 1.0
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'胜平负分析失败: {e}',
                'fair_probabilities': [0.45, 0.30, 0.25]
            }
    
    def comprehensive_market_analysis(self, overunder_data, odds_1x2_data, line=2.5):
        """
        综合市场分析 - 分析大小球和胜平负市场，估算λ参数
        
        Args:
            overunder_data: 大小球赔率数据列表
            odds_1x2_data: 胜平负赔率数据列表  
            line: 大小球盘口线
            
        Returns:
            dict: 综合分析结果
        """
        # 默认完整结果结构
        result = {
            'success': False,
            'lambda_estimates': {
                'home': 1.4,
                'away': 1.1, 
                'total': 2.5
            },
            'overunder_analysis': {'success': False},
            'odds_1x2_analysis': {'success': False},
            '1x2_analysis': {'success': False},  # 兼容性别名
            'margin_analysis': {},
            'selected_methods': {
                'ou_method': 'proportional',
                'x2_method': 'proportional',
                'ou_bias_analysis': {'bias_detected': False, 'recommendation': 'proportional'},
                'x2_bias_analysis': {'bias_detected': False, 'recommendation': 'proportional'}
            },
            'model_validation': {
                'data_quality': {
                    'overunder_available': False,
                    '1x2_available': False,
                    'overround_ou': 0,
                    'overround_1x2': 0,
                    'margin_method_ou': 'proportional',
                    'margin_method_1x2': 'proportional',
                    'bias_detected_ou': False,
                    'bias_detected_1x2': False
                },
                'fit_quality': 0.3
            },
            'warnings': []
        }
        
        try:
            # 步骤1: 分析大小球市场
            ou_analysis = self.analyze_overunder_market(overunder_data, line)
            result['overunder_analysis'] = ou_analysis
            
            # 步骤2: 分析胜平负市场 
            x2_analysis = self.analyze_1x2_market(odds_1x2_data)
            result['odds_1x2_analysis'] = x2_analysis
            result['1x2_analysis'] = x2_analysis  # 兼容性别名
            
            # 如果大小球分析失败但有胜平负数据，尝试基于默认总球数估算
            if not ou_analysis['success'] and x2_analysis['success']:
                result['warnings'].append(f"大小球数据获取失败(盘口{line})，使用默认总球数估算")
                # 使用默认总球数
                lambda_total = line if line > 0 else 2.5
                ou_analysis = {
                    'success': False,
                    'fallback_used': True,
                    'lambda_total_estimated': lambda_total
                }
                result['overunder_analysis'] = ou_analysis
            elif not ou_analysis['success']:
                result['warnings'].append(f"大小球和胜平负数据均不可用(盘口{line})，使用默认参数")
                # 完全失败时，设置基本默认值并返回
                result['model_validation']['data_quality']['overunder_available'] = False
                result['model_validation']['data_quality']['1x2_available'] = x2_analysis['success']
                return result
            
            # 步骤3: 智能选择庄家利润剔除方法
            margin_methods = self._select_optimal_margin_methods(ou_analysis, x2_analysis)
            result['selected_methods'] = margin_methods
            result['margin_analysis'] = {
                'overunder_bias': margin_methods['ou_bias_analysis'],
                '1x2_bias': margin_methods['x2_bias_analysis']
            }
            
            # 步骤4: 使用选定方法重新计算公平概率
            ou_fair_probs = None
            x2_fair_probs = None
            
            # 重新计算大小球公平概率
            if ou_analysis['success']:
                ou_original_odds = ou_analysis['original_odds']
                ou_implied = self.calculate_implied_probabilities([ou_original_odds['over'], ou_original_odds['under']])
                ou_fair_probs = self.remove_bookmaker_margin(ou_implied, method=margin_methods['ou_method'])
                ou_analysis['fair_probabilities'] = ou_fair_probs
            
            # 重新计算胜平负公平概率
            if x2_analysis['success']:
                x2_original_odds = x2_analysis['original_odds']
                x2_implied = self.calculate_implied_probabilities([x2_original_odds['home'], x2_original_odds['draw'], x2_original_odds['away']])
                x2_fair_probs = self.remove_bookmaker_margin(x2_implied, method=margin_methods['x2_method'])
                x2_analysis['fair_probabilities'] = x2_fair_probs
            
            # 步骤5: 估算λ值
            if ou_fair_probs and len(ou_fair_probs) >= 2:
                under_prob = ou_fair_probs[1]
                lambda_total = self.estimate_total_lambda_from_overunder(under_prob, line)
            elif ou_analysis.get('fallback_used'):
                lambda_total = ou_analysis['lambda_total_estimated']
            else:
                lambda_total = line if line > 0 else 2.5
                result['warnings'].append(f"无法从赔率估算总球数，使用盘口值 {lambda_total}")
            
            # 步骤6: 分配主客队λ
            if x2_fair_probs and len(x2_fair_probs) >= 3:
                lambda_home, lambda_away = self.allocate_team_lambdas(lambda_total, x2_fair_probs)
            else:
                # 没有1x2数据时的默认分配
                lambda_home = lambda_total * 0.55
                lambda_away = lambda_total * 0.45
                result['warnings'].append("缺少胜平负数据，使用默认λ分配")
            
            # 步骤7: 数据质量评估和最终结果构建
            data_quality = {
                'overunder_available': ou_analysis['success'],
                '1x2_available': x2_analysis['success'],
                'overround_ou': ou_analysis.get('overround', 0),
                'overround_1x2': x2_analysis.get('overround', 0),
                'margin_method_ou': margin_methods['ou_method'],
                'margin_method_1x2': margin_methods['x2_method'],
                'bias_detected_ou': margin_methods['ou_bias_analysis']['bias_detected'],
                'bias_detected_1x2': margin_methods['x2_bias_analysis']['bias_detected']
            }
            
            # 更新最终结果
            result.update({
                'success': True,
                'lambda_estimates': {
                    'home': lambda_home,
                    'away': lambda_away,
                    'total': lambda_total
                },
                'model_validation': {
                    'data_quality': data_quality,
                    'fit_quality': self._calculate_fit_quality(ou_analysis, x2_analysis)
                }
            })
            
        except Exception as e:
            result.update({
                'success': False,
                'error': f"市场分析异常: {str(e)}",
                'warnings': [f"市场分析异常: {str(e)}"]
            })
            
        return result
    
    def _select_optimal_margin_methods(self, ou_analysis, x2_analysis):
        """
        智能选择最优的庄家利润剔除方法
        
        根据市场偏差分析结果，为大小球和胜平负市场分别选择最适合的方法
        
        Args:
            ou_analysis: 大小球市场分析结果
            x2_analysis: 胜平负市场分析结果
            
        Returns:
            dict: 包含选定方法和偏差分析的结果
        """
        methods = {
            'ou_method': 'proportional',
            'x2_method': 'proportional', 
            'ou_bias_analysis': {'bias_detected': False, 'recommendation': 'proportional'},
            'x2_bias_analysis': {'bias_detected': False, 'recommendation': 'proportional'}
        }
        
        # 分析大小球市场偏差
        if ou_analysis['success'] and 'implied_probabilities' in ou_analysis:
            # 将字典格式转换为数组格式
            ou_implied_dict = ou_analysis['implied_probabilities']
            ou_implied_array = [ou_implied_dict['over'], ou_implied_dict['under']]
            ou_bias = self.detect_margin_bias(ou_implied_array)
            methods['ou_bias_analysis'] = ou_bias
            methods['ou_method'] = ou_bias['recommendation']
        
        # 分析胜平负市场偏差
        if x2_analysis['success'] and 'implied_probabilities' in x2_analysis:
            # 将字典格式转换为数组格式
            x2_implied_dict = x2_analysis['implied_probabilities']
            x2_implied_array = [x2_implied_dict['home'], x2_implied_dict['draw'], x2_implied_dict['away']]
            x2_bias = self.detect_margin_bias(x2_implied_array)
            methods['x2_bias_analysis'] = x2_bias
            methods['x2_method'] = x2_bias['recommendation']
        
        return methods
    
    def _calculate_fit_quality(self, ou_analysis, x2_analysis):
        """
        计算拟合质量分数
        
        Args:
            ou_analysis: 大小球分析结果
            x2_analysis: 胜平负分析结果
            
        Returns:
            float: 拟合质量分数 (0-1)
        """
        quality_score = 0.5  # 基础分数
        
        # 大小球质量评估
        if ou_analysis['success']:
            overround_ou = abs(ou_analysis.get('overround', 0))
            # 利润率越低，质量越高
            ou_quality = max(0, 1 - overround_ou * 5)  # 10%利润率对应0.5质量
            quality_score += ou_quality * 0.3
        
        # 胜平负质量评估
        if x2_analysis['success']:
            overround_1x2 = abs(x2_analysis.get('overround', 0))
            x2_quality = max(0, 1 - overround_1x2 * 5)
            quality_score += x2_quality * 0.2
        
        return min(1.0, quality_score)

if __name__ == "__main__":
    # 测试市场分析器
    analyzer = MarketAnalyzer()
    
    # 模拟赔率数据进行测试
    test_overunder_data = [
        {
            'company_name': '澳门',
            'over_odds': 1.95,
            'under_odds': 1.95,
            'line_numeric': 2.5
        }
    ]
    
    test_1x2_data = [
        {
            'company_name': '澳门',
            'home_win_odds': 2.20,
            'draw_odds': 3.30,
            'away_win_odds': 3.50
        }
    ]
    
    print("=== 市场分析器测试 ===")
    
    # 测试大小球分析
    ou_result = analyzer.analyze_overunder_market(test_overunder_data)
    print(f"大小球分析: {ou_result}")
    
    # 测试胜平负分析
    x12_result = analyzer.analyze_1x2_market(test_1x2_data)
    print(f"胜平负分析: {x12_result}")
    
    # 测试综合分析
    comprehensive_result = analyzer.comprehensive_market_analysis(test_overunder_data, test_1x2_data)
    print(f"综合分析结果: {comprehensive_result['success']}")
    if comprehensive_result['success']:
        print(f"λ参数: 主队={comprehensive_result['lambda_estimates']['home']:.3f}, 客队={comprehensive_result['lambda_estimates']['away']:.3f}")
        print(f"拟合质量: {comprehensive_result['model_validation']['fit_quality']:.3f}") 
    else:
        print(f"分析失败: {comprehensive_result.get('error', '未知错误')}") 