#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查记录ID表
"""

import sqlite3

def check_record_id_table():
    """检查记录ID表"""
    
    db_path = "data/matches_and_odds.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='odds_record_ids';")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ odds_record_ids 表存在")
            
            # 获取表结构
            cursor.execute("PRAGMA table_info(odds_record_ids);")
            columns = cursor.fetchall()
            
            print("\n=== 表结构 ===")
            for col_info in columns:
                cid, name, data_type, notnull, default_value, pk = col_info
                pk_str = " (主键)" if pk else ""
                notnull_str = " NOT NULL" if notnull else ""
                default_str = f" DEFAULT {default_value}" if default_value else ""
                print(f"  {name}: {data_type}{notnull_str}{default_str}{pk_str}")
            
            # 获取记录数
            cursor.execute("SELECT COUNT(*) FROM odds_record_ids;")
            count = cursor.fetchone()[0]
            print(f"\n记录数: {count}")
            
            # 显示所有数据
            if count > 0:
                cursor.execute("SELECT * FROM odds_record_ids ORDER BY scrape_time DESC;")
                records = cursor.fetchall()
                
                print("\n=== 所有记录 ===")
                for i, record in enumerate(records, 1):
                    print(f"记录 {i}: {record}")
                    
                # 按比赛分组显示
                cursor.execute("""
                    SELECT match_id, COUNT(*) as company_count 
                    FROM odds_record_ids 
                    GROUP BY match_id 
                    ORDER BY match_id
                """)
                match_stats = cursor.fetchall()
                
                print(f"\n=== 按比赛分组统计 ===")
                for match_id, company_count in match_stats:
                    print(f"比赛 {match_id}: {company_count} 家公司")
            
        else:
            print("❌ odds_record_ids 表不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_record_id_table() 