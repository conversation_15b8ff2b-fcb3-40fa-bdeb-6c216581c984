@echo off
echo 正在启动足球分析系统 - 现代版...

:: 检查Python是否安装
where python >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
  echo 错误: 未找到Python。请安装Python 3.8或更高版本。
  pause
  exit /b 1
)

:: 检查PIL是否安装
python -c "import PIL" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
  echo 正在安装Pillow图像处理库...
  python -m pip install pillow
)

:: 激活虚拟环境（如果存在）
if exist "venv\Scripts\activate.bat" (
  call venv\Scripts\activate.bat
)

:: 切换到项目目录
cd /d "%~dp0"

:: 启动现代UI应用
python football_analysis_system\run_modern_app.py

:: 如果应用程序返回错误，则显示错误消息
if %ERRORLEVEL% NEQ 0 (
  echo 应用程序异常退出，错误代码: %ERRORLEVEL%
  echo 请检查日志文件获取更多信息。
  pause
  exit /b %ERRORLEVEL%
)

:: 退出批处理
exit /b 0 