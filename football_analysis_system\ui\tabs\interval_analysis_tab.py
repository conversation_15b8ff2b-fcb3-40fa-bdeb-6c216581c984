import tkinter as tk
import logging
from .data_manager import DataManager
from .ui_components import UIBuilder
from .event_handlers import EventHandlers
from .business_logic import OddsAnalysisLogic
from .utils import format_interval_type
import threading


class IntervalAnalysisTab(tk.Frame, OddsAnalysisLogic):
    """区间分析标签页"""

    def __init__(self, parent, odds_analyzer, interval_analyzer, fundamental_analyzer=None, db_path=None):
        """
        初始化区间分析标签页

        Args:
            parent: 父容器
            odds_analyzer: 赔率分析器
            interval_analyzer: 区间分析器
            fundamental_analyzer: 基本面分析器（不再使用）
            db_path: 数据库路径
        """
        # 初始化父类 - 修复从parent.master获取COLOR_BG属性的问题
        tk.Frame.__init__(self, parent, bg="#F5F5F5")  # 使用默认背景色
        OddsAnalysisLogic.__init__(self)

        # 保存分析器引用
        self.odds_analyzer = odds_analyzer
        self.interval_analyzer = interval_analyzer
        # 不再使用基本面分析器
        self.fundamental_analyzer = None

        # 获取父应用的数据库路径，确保与app.py中使用的相同
        self.db_path = db_path

        # 记录数据库路径用于调试
        logging.info(f"区间分析标签页使用数据库路径: {self.db_path}")

        # 状态变量
        self.match_data = None  # 存储当前比赛数据
        self.should_stop = False  # 停止分析标志
        self.draw_interval_tree = None  # 添加平赔区间树属性并初始化为None

        # 初始化数据管理器
        self.data_manager = DataManager(self.db_path)

        # 初始化UI - 使用当前Frame的背景色和默认值
        self.ui_builder = UIBuilder(self, "#F5F5F5", "#3366CC", ("Arial", 12, "bold"))

        # 创建简化版UI控件 - 只包含区间分析相关控件
        self.interval_tree, self.save_button, self.status_label = self.create_simplified_widgets()

        # 创建区间分析框架
        interval_frame = tk.Frame(self, bg="#F5F5F5")
        interval_frame.pack(fill=tk.BOTH, expand=True)

        # 创建滚动区域用于区间分析树视图
        interval_scroll = tk.Scrollbar(interval_frame)
        interval_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 将interval_tree配置为使用滚动条
        self.interval_tree.configure(yscrollcommand=interval_scroll.set)
        interval_scroll.configure(command=self.interval_tree.yview)
        self.interval_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 初始化事件处理器
        self.event_handlers = EventHandlers(self.interval_tree, self.draw_interval_tree)

        # 应用特殊样式 - 使用更细的分隔线
        self.interval_tree.tag_configure('separator', background='#f0f0f0')  # 更浅的分隔线颜色

        # 为规则值不同的范围配置不同的颜色标签
        self.interval_tree.tag_configure('good', background='#90EE90')  # 浅绿色，规则值 ≤ 3
        self.interval_tree.tag_configure('normal', background='#FFFF99') # 浅黄色，规则值 3-4.5
        self.interval_tree.tag_configure('bad', background='#FFA07A')   # 浅橙色，规则值 4.5-6
        self.interval_tree.tag_configure('very_bad', background='#FF6B6B') # 浅红色，规则值 > 6

        # 为主队和客队单独创建标签
        self.interval_tree.tag_configure('home_good', background='#90EE90')
        self.interval_tree.tag_configure('home_normal', background='#FFFF99')
        self.interval_tree.tag_configure('home_bad', background='#FFA07A')
        self.interval_tree.tag_configure('home_very_bad', background='#FF6B6B')

        self.interval_tree.tag_configure('away_good', background='#90EE90')
        self.interval_tree.tag_configure('away_normal', background='#FFFF99')
        self.interval_tree.tag_configure('away_bad', background='#FFA07A')
        self.interval_tree.tag_configure('away_very_bad', background='#FF6B6B')

        # 添加超高水/超低水提示标签 - 保持背景色不变，仅加粗文本
        self.interval_tree.tag_configure('extreme_water', font=('Arial', 10, 'bold'))

        # 添加分组标题样式
        self.interval_tree.tag_configure('group_title', background='#E0E0E0', font=('Arial', 10, 'bold'))

        # 添加平赔区间样式
        self.interval_tree.tag_configure('draw_low', background='#C8E6C9')    # 浅绿色 - 低开平、平实低
        self.interval_tree.tag_configure('draw_normal', background='#FFF9C4')  # 浅黄色 - 平中庸
        self.interval_tree.tag_configure('draw_high', background='#FFCCBC')    # 浅橙色 - 高开平、平实高
        self.interval_tree.tag_configure('draw_extreme', background='#FFCDD2') # 浅红色 - 韬光平、超韬平、超深平、超散平、超实平

    def create_simplified_widgets(self):
        """创建简化版UI控件（仅包含区间分析相关控件）"""
        # 使用UIBuilder的新方法创建只含区间分析的UI控件
        interval_tree, save_button, status_label = self.ui_builder.create_interval_widgets(
            self.show_odds_interval_help,
            self.save_selected_predictions,
            self.on_interval_tree_click
        )

        return interval_tree, save_button, status_label

    def show_odds_interval_help(self):
        """显示赔率区间分析帮助信息"""
        self.ui_builder.show_odds_interval_help()

    def save_selected_predictions(self):
        """保存选中的预测结果"""
        self.data_manager.save_predictions(self.match_data, self.interval_tree, self.draw_interval_tree)

    def on_interval_tree_click(self, event):
        """处理区间分析表格点击事件"""
        self.event_handlers.on_interval_tree_click(event)

    def update_interval_data(self, match_data, gap_difference):
        """更新区间分析数据"""
        try:
            # 保存当前比赛数据
            self.match_data = match_data

            # 清空区间分析表格
            for item in self.interval_tree.get_children():
                self.interval_tree.delete(item)

            # 清空已选项目
            self.event_handlers.clear_selections()

            if not match_data or 'odds' not in match_data:
                logging.warning("没有有效数据可分析")
                return

            # 获取联赛名称
            league_name = match_data.get('league_name', '未知联赛')
            logging.info(f"当前联赛: {league_name}")

            # 标准化联赛名称以确保正确匹配
            try:
                from scrapers.league_mapping import normalize_league_name
                normalized_league_name = normalize_league_name(league_name)
                if normalized_league_name != league_name:
                    logging.info(f"联赛名称标准化: '{league_name}' -> '{normalized_league_name}'")
                    league_name = normalized_league_name
            except ImportError:
                logging.warning("无法导入联赛名称标准化函数，使用原始联赛名称")

            # 判断是否为南美联赛
            south_american_leagues = ["巴西甲", "巴西乙", "阿根廷甲", "阿根廷乙", "智利甲", "智利乙"]
            is_south_american = any(league in league_name for league in south_american_leagues)

            # 判断是否为东欧和亚洲地区联赛
            east_european_asian_leagues = ["俄超", "俄甲", "乌克兰", "波兰", "奥地利", "韩K", "韩K2"]
            is_east_european_asian = any(league in league_name for league in east_european_asian_leagues)

            # 判断是否为韩国K2联赛(以澳门为基准赔率)
            is_korean_k2 = "韩K2" in league_name

            # 获取主客队对象和多公司档距差数据
            home_team_data = match_data.get('home_team_obj')
            away_team_data = match_data.get('away_team_obj')

            # 获取多公司档距差数据
            company_gap_diffs = {}

            if home_team_data and away_team_data:
                home_ratings = getattr(home_team_data, 'multi_company_ratings', {})
                away_ratings = getattr(away_team_data, 'multi_company_ratings', {})

                # 计算每家公司的档距差
                all_company_ids = set(list(home_ratings.keys()) + list(away_ratings.keys()))

                for company_id in all_company_ids:
                    home_data = home_ratings.get(company_id, {})
                    away_data = away_ratings.get(company_id, {})

                    home_rating = home_data.get('rating')
                    away_rating = away_data.get('rating')

                    if home_rating is not None and away_rating is not None:
                        try:
                            h_rating = float(home_rating)
                            a_rating = float(away_rating)
                            company_gap = round(h_rating - a_rating, 2)
                            company_gap_diffs[company_id] = {
                                'gap_diff': company_gap,
                                'company_name': home_data.get('company_name') or away_data.get('company_name')
                            }
                        except (ValueError, TypeError):
                            pass

            # 如果没有多公司评分数据，记录日志但不添加假数据
            if not company_gap_diffs and home_team_data and away_team_data:
                logging.warning(f"未找到多公司评分数据 - 联赛: {league_name}")
                logging.info("区间分析将只显示有真实广义实力数据的公司")

            # 调试信息：输出当前状态
            home_team_name = match_data.get('home_team', '未知主队')
            away_team_name = match_data.get('away_team', '未知客队')
            logging.info(f"区间分析数据状态 - 联赛: {league_name}, 主队: {home_team_name}, 客队: {away_team_name}")
            logging.info(f"找到 {len(company_gap_diffs)} 个公司的档距差数据: {list(company_gap_diffs.keys())}")

            # 检查主客队的multi_company_ratings属性
            if home_team_data:
                home_ratings = getattr(home_team_data, 'multi_company_ratings', {})
                logging.info(f"主队 {home_team_name} 的多公司评分数据: {list(home_ratings.keys())}")
            if away_team_data:
                away_ratings = getattr(away_team_data, 'multi_company_ratings', {})
                logging.info(f"客队 {away_team_name} 的多公司评分数据: {list(away_ratings.keys())}")

            # 准备所有博彩公司数据
            odds_dict = match_data.get('odds', {})
            logging.info(f"区间分析 - 找到 {len(odds_dict)} 个博彩公司的赔率数据")
            logging.info(f"赔率数据中的博彩公司: {list(odds_dict.keys())}")

            # 特别检查18*和Bwi*公司
            for bookmaker in odds_dict.keys():
                if "18" in bookmaker or "bwi" in bookmaker.lower():
                    logging.info(f"区间分析 - 发现目标公司: {bookmaker}")

            # 如果没有赔率数据，显示友好提示并返回
            if not odds_dict:
                logging.warning(f"比赛 {match_data.get('match_id', '未知')} ({home_team_name} vs {away_team_name}) 没有赔率数据")

                # 在区间分析表格中显示提示信息
                self.interval_tree.insert('', 'end', values=[
                    "❌ 提示",
                    "该比赛暂无赔率数据",
                    "请选择标有📊或⚠️图标的比赛",
                    "📊=含香港马会 ⚠️=无香港马会", "", "", "", "", "", ""
                ])
                return

            # 预处理获取每个博彩公司的赔率类型
            bookmaker_types = {}
            for bookmaker in odds_dict.keys():
                bookmaker_types[bookmaker] = self.get_bookmaker_type(bookmaker, league_name)

            # 生成公司列表，添加基准赔率公司初赔作为单独条目
            all_bookmakers = []

            # 获取可用的广义实力公司ID
            available_company_ids = set(company_gap_diffs.keys())
            company_id_mapping = {
                "香港马会": "432",  # 马会
                "马会": "432",    # 马会
                "HKJC": "432",    # 马会
                "BetISn": "937", # BetISn
                "betisn": "937", # BetISn
                "iddaa": "657",   # iddaa
                "Macau": "80",  # 澳门
                "澳门": "80",    # 澳门
                "伟德": "81",    # 伟德
                "韦德": "81",    # 伟德（变体）
                "18BET": "976",   # 18BET
                "18bet": "976",   # 18BET（变体）
                "18*": "976",     # 18BET（变体）
                "18": "976",      # 18BET（变体）
                "威廉": "115",    # 威廉希尔
                "威廉希尔": "115",  # 威廉希尔
                "William Hill": "115", # 威廉希尔英文名
                "必赢": "255",    # bwin/必赢
                "bwin": "255",    # bwin/必赢
                "BWIN": "255",     # bwin/必赢
                "bwi": "255",     # bwin/必赢（变体）
                "Bwi": "255",     # bwin/必赢（变体）
                "易胜博": "90",    # 易胜博
                "easybets": "90"   # 易胜博英文名
            }

            for bookmaker, odds_data in odds_dict.items():
                if not bookmaker:
                    continue

                bookmaker_lower = bookmaker.lower()

                # 检查是否为BetISn
                is_betisn = "BetISn" in bookmaker or "betisn" in bookmaker_lower

                # 检查是否为马会
                is_hkjc = "马会" in bookmaker or "HKJC" in bookmaker or "香港" in bookmaker

                # 检查是否为iddaa
                is_iddaa = "iddaa" in bookmaker or "iddaa" in bookmaker.lower()

                # 检查是否为澳门
                is_macau = "澳门" in bookmaker or "Macau" in bookmaker

                # 检查是否为伟德
                is_weide = "伟德" in bookmaker or "韦德" in bookmaker or "weide" in bookmaker.lower()

                # 检查是否为18BET
                is_18bet = "18BET" in bookmaker or "18bet" in bookmaker.lower() or "18*" in bookmaker or bookmaker.startswith("18")

                # 检查是否为威廉希尔
                is_william = "威廉" in bookmaker or "William Hill" in bookmaker or "william hill" in bookmaker_lower

                # 检查是否为bwin/必赢
                is_bwin = "必赢" in bookmaker or "bwin" in bookmaker_lower or "BWIN" in bookmaker or "bwi" in bookmaker_lower

                # 检查是否为易胜博
                is_yishengbo = "易胜博" in bookmaker or "easybets" in bookmaker_lower

                # 判断是否为基准赔率公司（移除澳门和金宝博）
                is_base_odds_company = (is_betisn or is_hkjc or is_iddaa) and not is_macau

                # 根据联赛类型特殊处理
                is_base_due_to_league = False
                if is_south_american and is_weide:
                    is_base_odds_company = True
                    is_base_due_to_league = True
                elif is_east_european_asian and is_18bet:
                    is_base_odds_company = True
                    is_base_due_to_league = True

                # 以明确的顺序添加基准赔率公司初赔
                if is_base_odds_company:
                    # 添加初始赔率
                    if is_betisn:
                        # BetISn优先级最高
                        company_order = 1
                    elif is_hkjc:
                        # 马会第二
                        company_order = 2
                    elif is_iddaa:
                        # iddaa第三
                        company_order = 3
                    elif is_base_due_to_league:
                        # 联赛特定的基准公司最后
                        company_order = 4
                    else:
                        company_order = 5

                    # 检查公司ID
                    company_id = None
                    for key, id_val in company_id_mapping.items():
                        if key in bookmaker:
                            company_id = id_val
                            break

                    # 检查是否有广义实力数据
                    has_guangyi_data = False
                    if company_id and company_id in available_company_ids:
                        has_guangyi_data = True
                        logging.info(f"找到公司 {bookmaker} 的广义实力数据，ID: {company_id}")

                    # 只有在有广义实力数据时才添加
                    if has_guangyi_data:
                        # 检查是否有初始赔率数据
                        initial_home_win = odds_data.get('Initial_W') or odds_data.get('initial_home_win')
                        initial_draw = odds_data.get('Initial_D') or odds_data.get('initial_draw')
                        initial_away_win = odds_data.get('Initial_L') or odds_data.get('initial_away_win')

                        if initial_home_win is not None or initial_draw is not None or initial_away_win is not None:
                            all_bookmakers.append({
                                'name': bookmaker,
                                'type': "基准赔率",
                                'odds_mode': 'initial',
                                'display_suffix': ' (初赔)',
                                'order': company_order  # 添加排序索引
                            })
                            logging.info(f"添加基准赔率公司: {bookmaker}, 排序顺序: {company_order}")

                # 处理即时赔率
                # 检查公司ID和广义实力数据（添加到所有赔率类型前的通用检查）
                company_id = None
                for key, id_val in company_id_mapping.items():
                    if key in bookmaker:
                        company_id = id_val
                        break

                # 检查是否有广义实力数据
                has_guangyi_data = False
                if company_id and company_id in available_company_ids:
                    has_guangyi_data = True
                    logging.info(f"找到公司 {bookmaker} 的广义实力数据，ID: {company_id}")

                # 只有在有广义实力数据时才添加任何类型的赔率
                if has_guangyi_data:
                    # 基准赔率公司的即时赔添加到保守赔率类别 (动态规则)
                    if is_base_odds_company or is_hkjc or is_betisn or is_william:
                        all_bookmakers.append({
                            'name': bookmaker,
                            'type': "保守赔率",
                            'odds_mode': 'instant',
                            'display_suffix': ' (即赔)',
                            'order': 200  # 高数值确保顺序
                        })
                        logging.info(f"添加保守赔率公司(即时): {bookmaker}")

                    # 对于伟德，如果它是作为基准赔率使用，还需要添加其即时赔率到针对赔率类别
                    if is_weide and is_south_american:
                        all_bookmakers.append({
                            'name': bookmaker,
                            'type': "针对赔率",
                            'odds_mode': 'instant',
                            'display_suffix': ' (即赔)',
                            'order': 100  # 高数值确保在基准赔率之后
                        })
                        logging.info(f"添加针对赔率公司(即时): {bookmaker}")

                    # 易胜博添加到针对赔率类别
                    if is_yishengbo:
                        all_bookmakers.append({
                            'name': bookmaker,
                            'type': "针对赔率",
                            'odds_mode': 'instant',
                            'display_suffix': ' (即赔)',
                            'order': 90  # 优先级高于其他针对赔率公司
                        })
                        logging.info(f"添加针对赔率公司(即时): {bookmaker}")

                    # bwin添加到针对赔率类别
                    if is_bwin:
                        all_bookmakers.append({
                            'name': bookmaker,
                            'type': "针对赔率",
                            'odds_mode': 'instant',
                            'display_suffix': ' (即赔)',
                            'order': 100  # 高数值确保在基准赔率之后
                        })
                        logging.info(f"添加针对赔率公司(即时): {bookmaker}")

                    # 非基准公司的即时赔率(且不是已添加到其他类别的公司)
                    if not is_base_odds_company and not is_william and not is_bwin and not (is_weide and is_south_american) and not is_yishengbo:
                        # 非基准公司保持原有处理
                        all_bookmakers.append({
                            'name': bookmaker,
                            'type': bookmaker_types.get(bookmaker, "其他"),
                            'odds_mode': 'instant',
                            'display_suffix': ' (即赔)',
                            'order': 100  # 高数值确保在基准赔率之后
                        })
                elif not has_guangyi_data and (is_hkjc or is_betisn or (is_weide and is_south_american) or not is_base_odds_company):
                    # 记录没有广义实力数据的公司被过滤掉的情况
                    logging.warning(f"公司 {bookmaker} 没有广义实力数据，不添加到即时赔率展示列表")

            # 按类型分组
            type_groups = {
                "基准赔率": [],
                "针对赔率": [],
                "保守赔率": [],
                "其他": []
            }

            # 分组
            for bookmaker_info in all_bookmakers:
                if bookmaker_info['type'] in type_groups:
                    type_groups[bookmaker_info['type']].append(bookmaker_info)
                else:
                    type_groups["其他"].append(bookmaker_info)

            # 同类型内部排序
            for type_name, bookmakers in type_groups.items():
                # 使用order字段排序
                sorted_bookmakers = sorted(bookmakers, key=lambda x: x.get('order', 100))
                type_groups[type_name] = sorted_bookmakers

            # 按顺序处理各类型
            type_order = ["基准赔率", "针对赔率", "保守赔率", "其他"]
            for type_name in type_order:
                # 添加类型标题行
                if type_groups[type_name]:
                    self.interval_tree.insert("", tk.END, values=(
                        "", type_name, "", "", "", "", "", "", ""
                    ), tags=('separator', 'group_title'))

                    # 处理此类型下的所有公司
                    for bookmaker_info in type_groups[type_name]:
                        bookmaker = bookmaker_info['name']
                        odds_mode = bookmaker_info['odds_mode']

                        odds_data = odds_dict[bookmaker]

                        # 根据模式选择赔率数据
                        if odds_mode == 'initial':
                            # 使用初始赔率 - 严格只使用初始赔率字段
                            home_win = odds_data.get('Initial_W') or odds_data.get('initial_home_win', 'N/A')
                            draw = odds_data.get('Initial_D') or odds_data.get('initial_draw', 'N/A')
                            away_win = odds_data.get('Initial_L') or odds_data.get('initial_away_win', 'N/A')
                            # 使用初始返还率
                            payout_rate = odds_data.get('Initial_Payout_Rate') or odds_data.get('initial_payout_rate', 'N/A')
                            display_suffix = bookmaker_info.get('display_suffix', '')
                        else:
                            # 使用即时赔率 - 尝试所有可能的字段名
                            home_win = odds_data.get('Instant_W') or odds_data.get('instant_home_win', 'N/A')
                            draw = odds_data.get('Instant_D') or odds_data.get('instant_draw', 'N/A')
                            away_win = odds_data.get('Instant_L') or odds_data.get('instant_away_win', 'N/A')
                            # 使用即时返还率
                            payout_rate = odds_data.get('Payout_Rate') or odds_data.get('payout_rate', 'N/A')
                            display_suffix = bookmaker_info.get('display_suffix', '')

                        # 添加调试日志
                        logging.info(f"区间分析 - {bookmaker} ({odds_mode}): 主胜={home_win}, 平局={draw}, 客胜={away_win}, 返还率={payout_rate}")

                        # 添加显示后缀
                        display_bookmaker = f"{bookmaker}{display_suffix}" if display_suffix else bookmaker

                        # 安全转换为浮点数
                        try:
                            if home_win != 'N/A' and home_win is not None:
                                home_win = float(home_win)
                            else:
                                home_win = None

                            if draw != 'N/A' and draw is not None:
                                draw = float(draw)
                            else:
                                draw = None

                            if away_win != 'N/A' and away_win is not None:
                                away_win = float(away_win)
                            else:
                                away_win = None

                            if payout_rate != 'N/A' and payout_rate is not None:
                                payout_rate = float(payout_rate)
                            else:
                                payout_rate = None
                        except (ValueError, TypeError) as e:
                            logging.error(f"转换区间分析赔率数据为浮点数时出错: {e}, 数据: 主胜={home_win}, 平局={draw}, 客胜={away_win}")
                            continue  # 跳过此公司的处理

                        # 计算满水赔率
                        home_win_odds = self.odds_analyzer.calculate_true_odds(home_win, payout_rate) if home_win is not None else None
                        draw_true = self.odds_analyzer.calculate_true_odds(draw, payout_rate) if draw is not None else None
                        away_win_odds = self.odds_analyzer.calculate_true_odds(away_win, payout_rate) if away_win is not None else None

                        # 查找对应的档距
                        home_gap = None
                        away_gap = None
                        if home_win_odds:
                            home_gap = self.odds_analyzer.find_gap_for_odds(home_win_odds, "win")
                        if away_win_odds:
                            away_gap = self.odds_analyzer.find_gap_for_odds(away_win_odds, "loss")

                        # 确定使用哪个档距差
                        current_gap_diff = gap_difference  # 默认值

                        # 尝试匹配博彩公司名称与多公司档距差数据
                        matched_company_id = None
                        for company_id, company_data in company_gap_diffs.items():
                            if company_data['company_name'] == bookmaker:
                                matched_company_id = company_id
                                break

                        if matched_company_id:
                            current_gap_diff = company_gap_diffs[matched_company_id]['gap_diff']

                        # 计算主队区间
                        home_interval = self.interval_analyzer.find_interval_for_gap(home_gap, current_gap_diff, True)
                        home_type = home_interval.get('interval_type', '未知')
                        home_rule_value = home_interval.get('rule_value', '未知')

                        # 计算客队区间
                        away_interval = self.interval_analyzer.find_interval_for_gap(away_gap, current_gap_diff, False)
                        away_type = away_interval.get('interval_type', '未知')
                        away_rule_value = away_interval.get('rule_value', '未知')

                        # 计算规差值
                        rule_diff = None
                        if home_rule_value != '未知' and away_rule_value != '未知':
                            try:
                                rule_diff = float(home_rule_value) - float(away_rule_value)
                            except (ValueError, TypeError):
                                pass

                        # 计算平赔区间信息
                        draw_interval_type = "未知区间"
                        if home_win_odds and away_win_odds and draw_true:
                            # 计算标准平赔
                            standard_draw = self.odds_analyzer.find_standard_draw_odds(home_win_odds, away_win_odds)

                            # 计算平赔区间
                            draw_intervals = self.odds_analyzer.calculate_draw_odds_intervals(standard_draw)

                            # 确定实际平赔所在区间
                            draw_interval_type = self.odds_analyzer.determine_draw_interval(draw_true, draw_intervals)

                        # 获取首选推荐
                        first_choice = self.interval_analyzer.get_first_choice(rule_diff, current_gap_diff)

                        # 确定标签
                        home_tag = None
                        away_tag = None
                        draw_tag = None

                        try:
                            # 处理主队规则号
                            if home_rule_value != "未知":
                                try:
                                    home_rule_float = float(home_rule_value)
                                    # 规则号 0.5, 1, 1.5, 2, 2.5 → 绿色（安全区）
                                    if home_rule_float in [0.5, 1, 1.5, 2, 2.5]:
                                        home_tag = 'home_good'  # 绿色
                                    # 规则号 3, 3.5, 4 → 黄色（警示区）
                                    elif home_rule_float in [3, 3.5, 4]:
                                        home_tag = 'home_normal'  # 黄色
                                    # 规则号 4.5 → 橙色（高风险区）
                                    elif home_rule_float == 4.5:
                                        home_tag = 'home_bad'  # 橙色
                                    # 规则号 5, 5.5 → 红色（危险区）
                                    elif home_rule_float in [5, 5.5]:
                                        home_tag = 'home_very_bad'  # 红色
                                    # 其他值
                                    else:
                                        home_tag = None  # 不设置颜色
                                except (ValueError, TypeError):
                                    pass

                            # 处理客队规则号
                            if away_rule_value != "未知":
                                try:
                                    away_rule_float = float(away_rule_value)
                                    # 规则号 0.5, 1, 1.5, 2, 2.5 → 绿色（安全区）
                                    if away_rule_float in [0.5, 1, 1.5, 2, 2.5]:
                                        away_tag = 'away_good'  # 绿色
                                    # 规则号 3, 3.5, 4 → 黄色（警示区）
                                    elif away_rule_float in [3, 3.5, 4]:
                                        away_tag = 'away_normal'  # 黄色
                                    # 规则号 4.5 → 橙色（高风险区）
                                    elif away_rule_float == 4.5:
                                        away_tag = 'away_bad'  # 橙色
                                    # 规则号 5, 5.5 → 红色（危险区）
                                    elif away_rule_float in [5, 5.5]:
                                        away_tag = 'away_very_bad'  # 红色
                                    # 其他值
                                    else:
                                        away_tag = None  # 不设置颜色
                                except (ValueError, TypeError):
                                    pass

                            # 处理平赔区间颜色
                            if draw_interval_type != "未知区间":
                                if draw_interval_type in ["低开平", "平实低"]:
                                    draw_tag = 'draw_low'  # 浅绿色
                                elif draw_interval_type in ["平中庸下", "平中庸上"]:
                                    draw_tag = 'draw_normal'  # 浅黄色
                                elif draw_interval_type in ["高开平", "平实高"]:
                                    draw_tag = 'draw_high'  # 浅橙色
                                elif draw_interval_type in ["韬光平", "超韬平", "超深平", "超散平", "超实平"]:
                                    draw_tag = 'draw_extreme'  # 浅红色
                                # 记录日志
                                logging.info(f"平赔区间: {draw_interval_type}, 标签: {draw_tag}")

                        except (ValueError, TypeError) as e:
                            logging.warning(f"计算规则值颜色标记时出错: {e}")

                        # 根据区间类型检查是否包含"超高水"或"超低水"
                        has_extreme_water = home_type and ("超高水" in home_type or "超低水" in home_type)

                        # 准备标签列表
                        tags = []
                        if home_tag:
                            tags.append(home_tag)
                        if has_extreme_water:
                            tags.append('extreme_water')
                        if draw_tag:
                            tags.append(draw_tag)

                        # 创建主队行 - 添加选择列，将rule_diff替换为draw_interval
                        self.interval_tree.insert("", tk.END, values=(
                            "",  # 选择状态，默认为空
                            "",  # 不重复显示赔率类型
                            display_bookmaker,
                            "主队",
                            current_gap_diff,
                            format_interval_type(home_type),  # 使用独立工具函数格式化区间类型
                            home_rule_value,
                            draw_interval_type,  # 显示平赔区间而不是规差值
                            first_choice
                        ), tags=tuple(tags) if tags else ())

                        # 检查客队区间类型
                        has_extreme_water_away = away_type and ("超高水" in away_type or "超低水" in away_type)

                        # 准备客队标签列表
                        away_tags = []
                        if away_tag:
                            away_tags.append(away_tag)
                        if has_extreme_water_away:
                            away_tags.append('extreme_water')

                        # 创建客队行 - 添加选择列
                        self.interval_tree.insert("", tk.END, values=(
                            "",  # 选择状态，默认为空
                            "",  # 留空，与主队合并显示
                            "",  # 留空，与主队合并显示
                            "客队",
                            "",  # 留空，与主队合并显示
                            format_interval_type(away_type),  # 使用独立工具函数格式化区间类型
                            away_rule_value,
                            "",  # 留空，与主队合并显示
                            ""   # 留空，与主队合并显示
                        ), tags=tuple(away_tags) if away_tags else ())

                        # 添加分隔行
                        self.interval_tree.insert("", tk.END, values=("", "", "", "", "", "", "", "", ""), tags=('separator',))

        except Exception as e:
            logging.error(f"更新区间分析数据时出错: {e}")

    def clear(self):
        """清空所有UI元素"""
        # 清空区间分析表格
        for item in self.interval_tree.get_children():
            self.interval_tree.delete(item)

        # 清空已选项目
        self.event_handlers.clear_selections()

        # 清空状态标签
        self.status_label.config(text="")