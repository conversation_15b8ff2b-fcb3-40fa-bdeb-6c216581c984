#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的爬虫
"""

import time
import requests
import re
import sqlite3
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 直接定义配置
ODDS_URL_TEMPLATE = "https://1x2d.titan007.com/{match_id}.js?r={timestamp}"
ODDS_HEADERS_TEMPLATE = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
    'Referer': 'https://op1.titan007.com/oddslist/{match_id}.htm',
}
TARGET_COMPANIES = ['威廉希尔', '易胜博', '香港马会', '澳门', 'BWIN']

def test_concurrent_database_access():
    """测试并发数据库访问"""
    
    print("=" * 60)
    print("🧪 测试并发数据库访问修复")
    print("=" * 60)
    
    # 导入修复后的数据库管理器
    from fix_database_issue import create_robust_database_manager
    
    RobustDatabaseManager = create_robust_database_manager()
    db_manager = RobustDatabaseManager("data/matches_and_odds.db")
    
    # 模拟多个并发保存操作
    import threading
    import random
    
    def simulate_save_operation(match_id, thread_id):
        """模拟保存操作"""
        try:
            # 模拟记录ID数据
            record_ids_data = {
                f'公司{thread_id}': {
                    'company_id': f'{100 + thread_id}',
                    'company_name': f'Company_{thread_id}',
                    'record_id': f'{********* + random.randint(1000, 9999)}',
                    'update_timestamp': f'2025,08-1,03,{random.randint(10, 59)},00'
                }
            }
            
            success = db_manager.save_record_ids(match_id, record_ids_data)
            if success:
                print(f"✅ 线程 {thread_id}: 保存成功")
            else:
                print(f"❌ 线程 {thread_id}: 保存失败")
                
        except Exception as e:
            print(f"❌ 线程 {thread_id}: 异常 - {e}")
    
    # 创建多个线程同时访问数据库
    threads = []
    test_match_id = "concurrent_test"
    
    print("启动10个并发线程...")
    for i in range(10):
        thread = threading.Thread(
            target=simulate_save_operation, 
            args=(test_match_id, i)
        )
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 检查结果
    try:
        conn = sqlite3.connect("data/matches_and_odds.db")
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM odds_record_ids WHERE match_id=?", (test_match_id,))
        count = cursor.fetchone()[0]
        conn.close()
        
        print(f"✅ 并发测试完成，数据库中保存了 {count} 条记录")
        
        # 清理测试数据
        conn = sqlite3.connect("data/matches_and_odds.db")
        cursor = conn.cursor()
        cursor.execute("DELETE FROM odds_record_ids WHERE match_id=?", (test_match_id,))
        conn.commit()
        conn.close()
        print("✅ 测试数据清理完成")
        
    except Exception as e:
        print(f"❌ 检查结果失败: {e}")

def test_real_scraping():
    """测试真实的爬取场景"""
    
    print("\n" + "=" * 60)
    print("🌐 测试真实爬取场景")
    print("=" * 60)
    
    # 创建简化的爬虫类
    class TestScraper:
        def __init__(self):
            # 使用健壮的数据库管理器
            from fix_database_issue import create_robust_database_manager
            RobustDatabaseManager = create_robust_database_manager()
            self.db_manager = RobustDatabaseManager("data/matches_and_odds.db")
        
        def get_timestamp_param(self):
            timestamp_part = str(int(time.time() * 1000))
            if len(timestamp_part) > 13:
                timestamp_part = timestamp_part[-13:]
            else:
                timestamp_part = timestamp_part.ljust(13, '0')
            return "007" + timestamp_part
        
        def scrape_and_save(self, match_id):
            """爬取并保存数据"""
            try:
                # 1. 获取数据
                timestamp = self.get_timestamp_param()
                url = ODDS_URL_TEMPLATE.format(match_id=match_id, timestamp=timestamp)
                headers = ODDS_HEADERS_TEMPLATE.copy()
                headers['Referer'] = headers['Referer'].format(match_id=match_id)
                
                response = requests.get(url, headers=headers, timeout=10)
                if response.status_code != 200:
                    print(f"❌ 请求失败: {response.status_code}")
                    return False
                
                # 2. 解析数据
                game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', response.text, re.DOTALL)
                if not game_match:
                    print(f"❌ 未找到game数组")
                    return False
                
                game_data = game_match.group(1)
                company_records = re.findall(r'"([^"]*)"', game_data)
                
                record_ids_data = {}
                for record in company_records[:3]:  # 只处理前3条
                    fields = record.split('|')
                    if len(fields) >= 21:
                        company_id = fields[0]
                        record_id = fields[1]
                        company_name = fields[2].strip()
                        update_timestamp = fields[20].strip()
                        
                        record_ids_data[f'company_{company_id}'] = {
                            'company_id': company_id,
                            'company_name': company_name,
                            'record_id': record_id,
                            'update_timestamp': update_timestamp
                        }
                
                # 3. 保存数据
                if record_ids_data:
                    success = self.db_manager.save_record_ids(match_id, record_ids_data)
                    if success:
                        print(f"✅ 比赛 {match_id}: 成功保存 {len(record_ids_data)} 条记录")
                        return True
                    else:
                        print(f"❌ 比赛 {match_id}: 保存失败")
                        return False
                else:
                    print(f"❌ 比赛 {match_id}: 未解析到数据")
                    return False
                    
            except Exception as e:
                print(f"❌ 比赛 {match_id}: 异常 - {e}")
                return False
    
    # 测试多个比赛ID
    scraper = TestScraper()
    test_matches = ["2696009", "2791500", "2793168"]
    
    success_count = 0
    for match_id in test_matches:
        print(f"\n处理比赛 {match_id}...")
        if scraper.scrape_and_save(match_id):
            success_count += 1
        time.sleep(1)  # 避免请求过快
    
    print(f"\n✅ 真实爬取测试完成: {success_count}/{len(test_matches)} 成功")

def main():
    """主函数"""
    
    print("🔧 数据库并发问题修复验证")
    
    # 1. 测试并发访问
    test_concurrent_database_access()
    
    # 2. 测试真实爬取
    test_real_scraping()
    
    print(f"\n🎉 所有测试完成！数据库问题已修复。")
    print(f"现在您的爬虫应该可以正常处理并发数据库访问了。")

if __name__ == "__main__":
    main() 