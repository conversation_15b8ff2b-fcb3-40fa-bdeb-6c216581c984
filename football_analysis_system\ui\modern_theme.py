"""
现代化主题管理系统
提供深色和浅色主题支持，现代化的UI样式配置
"""

import tkinter as tk
from tkinter import ttk, font
from typing import Dict, Any
import json
import os
from PIL import Image, ImageTk, ImageDraw

class ModernTheme:
    """现代化主题管理类"""
    
    # 现代化主题颜色
    PRIMARY = "#4361ee"       # 主色调
    PRIMARY_LIGHT = "#597aff" # 亮主色调  
    PRIMARY_DARK = "#2c41da"  # 深主色调
    SECONDARY = "#3a0ca3"     # 次要色调
    ACCENT = "#48cae4"        # 强调色
    SUCCESS = "#10b981"       # 成功色
    WARNING = "#f59e0b"       # 警告色
    ERROR = "#ef4444"         # 错误色
    INFO = "#60a5fa"          # 信息色
    
    # 中性色调
    BACKGROUND = "#f8fafc"    # 主背景色
    SURFACE = "#ffffff"       # 表面色
    CARD_BG = "#ffffff"       # 卡片背景色
    BORDER = "#e2e8f0"        # 边框色
    
    # 文本颜色
    TEXT_PRIMARY = "#1e293b"  # 主文本色
    TEXT_SECONDARY = "#64748b" # 次要文本色
    TEXT_DISABLED = "#94a3b8" # 禁用文本色
    TEXT_ON_PRIMARY = "#ffffff" # 主色上文本
    
    # 数据颜色
    HOME = "#3f8ef7"          # 主队
    AWAY = "#f97316"          # 客队
    DRAW = "#9333ea"          # 平局
    
    # 字体设置
    FONT_FAMILY = "Segoe UI"
    FONT_SIZES = {
        "xs": 8,
        "sm": 10,
        "md": 12,
        "lg": 14,
        "xl": 16,
        "2xl": 18,
        "3xl": 24,
        "4xl": 32,
    }
    
    # 圆角和阴影
    CORNER_RADIUS = 8
    BORDER_WIDTH = 1
    
    def __init__(self):
        self.current_theme = "light"
        self.themes = self._load_themes()
        
    def _load_themes(self) -> Dict[str, Dict[str, Any]]:
        """加载主题配置"""
        return {
            "light": {
                # 主色调
                "primary": "#2563eb",           # 现代蓝色
                "primary_dark": "#1d4ed8",      # 深蓝色
                "primary_light": "#60a5fa",     # 浅蓝色
                
                # 次要色
                "secondary": "#64748b",         # 现代灰色
                "secondary_dark": "#475569",    # 深灰色
                "secondary_light": "#94a3b8",   # 浅灰色
                
                # 背景色
                "bg_primary": "#ffffff",        # 主背景
                "bg_secondary": "#f8fafc",      # 次要背景
                "bg_tertiary": "#f1f5f9",       # 第三背景
                "bg_elevated": "#ffffff",       # 提升背景（卡片等）
                
                # 文本色
                "text_primary": "#0f172a",      # 主文本
                "text_secondary": "#475569",    # 次要文本
                "text_muted": "#64748b",        # 弱化文本
                "text_inverse": "#ffffff",      # 反色文本
                
                # 边框色
                "border": "#e2e8f0",            # 边框
                "border_light": "#f1f5f9",      # 浅边框
                "border_dark": "#cbd5e1",       # 深边框
                
                # 状态色
                "success": "#10b981",           # 成功色
                "warning": "#f59e0b",           # 警告色
                "error": "#ef4444",             # 错误色
                "info": "#3b82f6",              # 信息色
                
                # 足球特定色
                "home_team": "#2563eb",         # 主队色
                "away_team": "#dc2626",         # 客队色
                "draw": "#f59e0b",              # 平局色
                
                # 阴影
                "shadow_sm": "#0000001a",       # 小阴影
                "shadow_md": "#00000026",       # 中阴影
                "shadow_lg": "#00000033",       # 大阴影
            },
            
            "dark": {
                # 主色调
                "primary": "#3b82f6",           # 亮蓝色
                "primary_dark": "#1d4ed8",      # 深蓝色
                "primary_light": "#60a5fa",     # 浅蓝色
                
                # 次要色
                "secondary": "#64748b",         # 现代灰色
                "secondary_dark": "#374151",    # 深灰色
                "secondary_light": "#9ca3af",   # 浅灰色
                
                # 背景色
                "bg_primary": "#0f172a",        # 主背景
                "bg_secondary": "#1e293b",      # 次要背景
                "bg_tertiary": "#334155",       # 第三背景
                "bg_elevated": "#1e293b",       # 提升背景（卡片等）
                
                # 文本色
                "text_primary": "#f8fafc",      # 主文本
                "text_secondary": "#cbd5e1",    # 次要文本
                "text_muted": "#94a3b8",        # 弱化文本
                "text_inverse": "#0f172a",      # 反色文本
                
                # 边框色
                "border": "#334155",            # 边框
                "border_light": "#475569",      # 浅边框
                "border_dark": "#1e293b",       # 深边框
                
                # 状态色
                "success": "#22c55e",           # 成功色
                "warning": "#fbbf24",           # 警告色
                "error": "#f87171",             # 错误色
                "info": "#60a5fa",              # 信息色
                
                # 足球特定色
                "home_team": "#3b82f6",         # 主队色
                "away_team": "#ef4444",         # 客队色
                "draw": "#fbbf24",              # 平局色
                
                # 阴影
                "shadow_sm": "#00000040",       # 小阴影
                "shadow_md": "#00000059",       # 中阴影
                "shadow_lg": "#00000073",       # 大阴影
            }
        }
    
    def get_color(self, color_name: str) -> str:
        """获取当前主题的颜色"""
        return self.themes[self.current_theme].get(color_name, "#000000")
    
    def set_theme(self, theme_name: str):
        """设置主题"""
        if theme_name in self.themes:
            self.current_theme = theme_name
    
    def get_font_config(self) -> Dict[str, tuple]:
        """获取字体配置"""
        return {
            "small": ("Segoe UI", 9),
            "normal": ("Segoe UI", 10),
            "medium": ("Segoe UI", 11),
            "large": ("Segoe UI", 12, "bold"),
            "header": ("Segoe UI", 16, "bold"),
            "title": ("Segoe UI", 20, "bold"),
            "display": ("Segoe UI", 24, "bold"),
        }

    @classmethod
    def setup(cls):
        """设置现代主题"""
        style = ttk.Style()
        
        # 检测最合适的主题作为基础
        available_themes = style.theme_names()
        if 'clam' in available_themes:
            style.theme_use('clam')
        elif 'vista' in available_themes:
            style.theme_use('vista')
        
        # 确保系统字体可用
        available_fonts = font.families()
        if cls.FONT_FAMILY not in available_fonts:
            cls.FONT_FAMILY = "Arial" if "Arial" in available_fonts else tk.font.nametofont("TkDefaultFont").actual()['family']
        
        # 基本字体设置
        font_normal = (cls.FONT_FAMILY, cls.FONT_SIZES["md"])
        font_bold = (cls.FONT_FAMILY, cls.FONT_SIZES["md"], "bold")
        font_large = (cls.FONT_FAMILY, cls.FONT_SIZES["lg"])
        font_large_bold = (cls.FONT_FAMILY, cls.FONT_SIZES["lg"], "bold")
        font_header = (cls.FONT_FAMILY, cls.FONT_SIZES["2xl"], "bold")
        font_subheader = (cls.FONT_FAMILY, cls.FONT_SIZES["xl"], "bold")
        
        # 框架样式
        style.configure('TFrame', background=cls.BACKGROUND)
        style.configure('Card.TFrame', background=cls.CARD_BG, borderwidth=cls.BORDER_WIDTH, relief='solid')
        
        # 标签样式
        style.configure('TLabel', 
                       background=cls.BACKGROUND, 
                       foreground=cls.TEXT_PRIMARY,
                       font=font_normal)
        
        style.configure('Header.TLabel',
                       background=cls.BACKGROUND,
                       foreground=cls.PRIMARY,
                       font=font_header)
                       
        style.configure('Subheader.TLabel',
                       background=cls.BACKGROUND,
                       foreground=cls.PRIMARY_DARK,
                       font=font_subheader)
        
        style.configure('Card.TLabel',
                       background=cls.CARD_BG,
                       foreground=cls.TEXT_PRIMARY,
                       font=font_normal)
        
        style.configure('Small.TLabel',
                       font=(cls.FONT_FAMILY, cls.FONT_SIZES["sm"]),
                       foreground=cls.TEXT_SECONDARY)
        
        # 按钮样式
        style.configure('TButton', 
                       font=font_normal,
                       background=cls.PRIMARY,
                       foreground=cls.TEXT_ON_PRIMARY,
                       borderwidth=0,
                       focusthickness=0,
                       padding=[16, 8])
        
        style.map('TButton',
                 background=[('active', cls.PRIMARY_LIGHT), ('disabled', cls.BORDER)],
                 foreground=[('disabled', cls.TEXT_DISABLED)])
        
        # 主要按钮
        style.configure('Primary.TButton',
                       background=cls.PRIMARY,
                       foreground=cls.TEXT_ON_PRIMARY)
        
        style.map('Primary.TButton',
                 background=[('active', cls.PRIMARY_LIGHT), ('disabled', cls.BORDER)],
                 foreground=[('disabled', cls.TEXT_DISABLED)])
        
        # 次要按钮
        style.configure('Secondary.TButton',
                       background=cls.SECONDARY,
                       foreground=cls.TEXT_ON_PRIMARY)
        
        style.map('Secondary.TButton',
                 background=[('active', '#4d21cc'), ('disabled', cls.BORDER)])
        
        # 成功按钮
        style.configure('Success.TButton',
                       background=cls.SUCCESS,
                       foreground=cls.TEXT_ON_PRIMARY)
        
        style.map('Success.TButton',
                 background=[('active', '#15d997'), ('disabled', cls.BORDER)])
        
        # 警告按钮
        style.configure('Warning.TButton',
                       background=cls.WARNING,
                       foreground=cls.TEXT_ON_PRIMARY)
        
        style.map('Warning.TButton',
                 background=[('active', '#ffb83d'), ('disabled', cls.BORDER)])
        
        # 错误按钮
        style.configure('Error.TButton',
                       background=cls.ERROR,
                       foreground=cls.TEXT_ON_PRIMARY)
        
        style.map('Error.TButton',
                 background=[('active', '#f87171'), ('disabled', cls.BORDER)])
        
        # 树视图样式
        style.configure('Treeview',
                       background=cls.SURFACE,
                       fieldbackground=cls.SURFACE,
                       foreground=cls.TEXT_PRIMARY,
                       font=font_normal,
                       rowheight=36,
                       borderwidth=0)
        
        style.configure('Treeview.Heading',
                       background=cls.PRIMARY,
                       foreground=cls.TEXT_ON_PRIMARY,
                       font=font_bold,
                       padding=[5, 5],
                       relief='flat')
        
        style.map('Treeview',
                 background=[('selected', cls.PRIMARY_LIGHT)],
                 foreground=[('selected', cls.TEXT_ON_PRIMARY)])
        
        # 笔记本样式
        style.configure('TNotebook',
                       background=cls.BACKGROUND,
                       relief='flat',
                       tabposition='n')
        
        style.configure('TNotebook.Tab',
                       background=cls.BACKGROUND,
                       foreground=cls.TEXT_SECONDARY,
                       padding=[15, 8],
                       font=font_normal)
        
        style.map('TNotebook.Tab',
                 background=[('selected', cls.PRIMARY)],
                 foreground=[('selected', cls.TEXT_ON_PRIMARY)],
                 expand=[('selected', [0, 0, 0, 2])])
        
        # 组合框和输入框
        style.configure('TCombobox',
                       background=cls.SURFACE,
                       fieldbackground=cls.SURFACE,
                       foreground=cls.TEXT_PRIMARY,
                       arrowcolor=cls.PRIMARY,
                       font=font_normal,
                       padding=[8, 5])
        
        style.map('TCombobox',
                 fieldbackground=[('readonly', cls.SURFACE)],
                 selectbackground=[('readonly', cls.PRIMARY_LIGHT)],
                 selectforeground=[('readonly', cls.TEXT_ON_PRIMARY)])
        
        # 设置输入框样式
        style.configure('TEntry',
                       font=cls.FONT_SIZES["normal"],
                       fieldbackground=cls.CARD_BG,
                       background=cls.CARD_BG,
                       foreground=cls.TEXT_PRIMARY,
                       relief='flat',
                       bordercolor=cls.BORDER,
                       insertcolor=cls.PRIMARY,
                       padding=[12, 8])
        
        # 标签框架
        style.configure('TLabelframe',
                       background=cls.BACKGROUND,
                       relief='solid',
                       padding=4)
        
        style.configure('TLabelframe.Label',
                       font=font_large_bold,
                       background=cls.BACKGROUND,
                       foreground=cls.PRIMARY,
                       padding=[8, 4])
        
        # 滚动条样式
        style.configure('TScrollbar',
                       background=cls.BACKGROUND,
                       arrowcolor=cls.PRIMARY,
                       bordercolor=cls.BACKGROUND,
                       troughcolor=cls.BACKGROUND,
                       width=8,
                       borderwidth=0)
        
        style.map('TScrollbar',
                 background=[('active', cls.PRIMARY_LIGHT), ('pressed', cls.PRIMARY)],
                 arrowcolor=[('active', cls.TEXT_ON_PRIMARY)])
        
        # 进度条样式
        style.configure('TProgressbar',
                       background=cls.PRIMARY,
                       troughcolor="#e0e0e0",
                       bordercolor=cls.BACKGROUND,
                       thickness=8)
        
        # 定制数据展示标签
        style.configure('Home.TLabel',
                       foreground=cls.HOME,
                       background=cls.BACKGROUND,
                       font=font_large_bold)
        
        style.configure('Away.TLabel',
                       foreground=cls.AWAY,
                       background=cls.BACKGROUND,
                       font=font_large_bold)
        
        style.configure('Draw.TLabel',
                       foreground=cls.DRAW,
                       background=cls.BACKGROUND,
                       font=font_large_bold)
        
        # 卡片样式
        style.configure('Card.TFrame', 
                       background=cls.CARD_BG, 
                       relief='solid', 
                       borderwidth=1)
        
        style.configure('CardHeader.TFrame',
                       background="#f1f5f9",
                       relief='flat')
        
        style.configure('CardHeader.TLabel',
                       background="#f1f5f9",
                       foreground=cls.PRIMARY_DARK,
                       font=font_bold)
        
        style.configure('CardContent.TFrame',
                       background=cls.CARD_BG,
                       relief='flat')
        
        return style
        
    @staticmethod
    def create_football_icon(size=64):
        """
        创建简单的足球图标

        Args:
            size: 图标大小

        Returns:
            ImageTk.PhotoImage: 图标图像
        """
        try:
            # 创建一个白色背景的图像
            img = Image.new('RGBA', (size, size), (255, 255, 255, 0))
            draw = ImageDraw.Draw(img)
            
            # 球的颜色
            ball_color = "#333333"
            
            # 绘制足球轮廓
            draw.ellipse([(2, 2), (size-2, size-2)], outline=ball_color, fill="white", width=2)
            
            # 绘制五边形图案
            center_x, center_y = size // 2, size // 2
            radius = size // 2 - 6
            
            # 绘制五角星
            for i in range(5):
                x1 = center_x + int(radius * 0.8 * 0.5 * (1 if i % 2 else 2) * 
                                 0.9 * (1 if i < 3 else -1))
                y1 = center_y + int(radius * 0.8 * 0.5 * (1 if i % 2 else 0.5) * 
                                 (1 if i < 2 or i > 3 else -1))
                x2 = center_x + int(radius * 0.8 * 0.5 * (1 if (i+3) % 5 < 3 else -1))
                y2 = center_y + int(radius * 0.8 * 0.5 * (1 if (i+2) % 5 < 2 or (i+2) % 5 > 3 else -1))
                
                draw.polygon([(center_x, center_y), (x1, y1), (x2, y2)], 
                           fill=ball_color, outline=ball_color)
            
            return ImageTk.PhotoImage(img)
        except Exception as e:
            print(f"创建足球图标时出错: {e}")
            return None

class ModernStyleManager:
    """现代化样式管理器"""
    
    def __init__(self, theme: ModernTheme):
        self.theme = theme
        self.fonts = theme.get_font_config()
        
    def setup_styles(self, root: tk.Tk):
        """设置现代化样式"""
        style = ttk.Style()
        
        # 使用clam主题作为基础
        style.theme_use('clam')
        
        # 设置全局样式
        self._setup_global_styles(style)
        self._setup_button_styles(style)
        self._setup_entry_styles(style)
        self._setup_treeview_styles(style)
        self._setup_notebook_styles(style)
        self._setup_frame_styles(style)
        self._setup_progressbar_styles(style)
        self._setup_scrollbar_styles(style)
        
        return style
    
    def _setup_global_styles(self, style: ttk.Style):
        """设置全局样式"""
        # 基础样式
        style.configure('.',
                       background=self.theme.get_color("bg_primary"),
                       foreground=self.theme.get_color("text_primary"),
                       borderwidth=0,
                       focuscolor='none')
        
        # 标签样式
        style.configure('TLabel',
                       background=self.theme.get_color("bg_primary"),
                       foreground=self.theme.get_color("text_primary"),
                       font=self.fonts["normal"])
        
        # 框架样式
        style.configure('TFrame',
                       background=self.theme.get_color("bg_primary"),
                       borderwidth=0)
    
    def _setup_button_styles(self, style: ttk.Style):
        """设置按钮样式"""
        # 主要按钮
        style.configure('Primary.TButton',
                       font=self.fonts["normal"],
                       background=self.theme.get_color("primary"),
                       foreground=self.theme.get_color("text_inverse"),
                       borderwidth=0,
                       focusthickness=0,
                       padding=[16, 10],
                       relief='flat')
        
        style.map('Primary.TButton',
                 background=[
                     ('active', self.theme.get_color("primary_dark")),
                     ('pressed', self.theme.get_color("primary_dark"))
                 ],
                 relief=[('pressed', 'flat'), ('!pressed', 'flat')])
        
        # 次要按钮
        style.configure('Secondary.TButton',
                       font=self.fonts["normal"],
                       background=self.theme.get_color("bg_elevated"),
                       foreground=self.theme.get_color("text_primary"),
                       borderwidth=1,
                       bordercolor=self.theme.get_color("border"),
                       focusthickness=0,
                       padding=[16, 10],
                       relief='flat')
        
        style.map('Secondary.TButton',
                 background=[
                     ('active', self.theme.get_color("bg_tertiary")),
                     ('pressed', self.theme.get_color("bg_tertiary"))
                 ],
                 bordercolor=[
                     ('active', self.theme.get_color("border_dark")),
                     ('pressed', self.theme.get_color("border_dark"))
                 ])
        
        # 成功按钮
        style.configure('Success.TButton',
                       font=self.fonts["normal"],
                       background=self.theme.get_color("success"),
                       foreground=self.theme.get_color("text_inverse"),
                       borderwidth=0,
                       focusthickness=0,
                       padding=[16, 10],
                       relief='flat')
        
        # 警告按钮
        style.configure('Warning.TButton',
                       font=self.fonts["normal"],
                       background=self.theme.get_color("warning"),
                       foreground=self.theme.get_color("text_inverse"),
                       borderwidth=0,
                       focusthickness=0,
                       padding=[16, 10],
                       relief='flat')
        
        # 错误按钮
        style.configure('Error.TButton',
                       font=self.fonts["normal"],
                       background=self.theme.get_color("error"),
                       foreground=self.theme.get_color("text_inverse"),
                       borderwidth=0,
                       focusthickness=0,
                       padding=[16, 10],
                       relief='flat')
    
    def _setup_entry_styles(self, style: ttk.Style):
        """设置输入框样式"""
        style.configure('TEntry',
                       font=self.fonts["normal"],
                       fieldbackground=self.theme.get_color("bg_elevated"),
                       background=self.theme.get_color("bg_elevated"),
                       foreground=self.theme.get_color("text_primary"),
                       relief='flat',
                       bordercolor=self.theme.get_color("border"),
                       insertcolor=self.theme.get_color("primary"),
                       padding=[12, 8])
        
        style.map('TEntry',
                 bordercolor=[
                     ('focus', self.theme.get_color("primary")),
                     ('!focus', self.theme.get_color("border"))
                 ])
        
        # 下拉框样式
        style.configure('TCombobox',
                       font=self.fonts["normal"],
                       fieldbackground=self.theme.get_color("bg_elevated"),
                       background=self.theme.get_color("bg_elevated"),
                       foreground=self.theme.get_color("text_primary"),
                       relief='flat',
                       bordercolor=self.theme.get_color("border"),
                       arrowcolor=self.theme.get_color("text_secondary"),
                       padding=[12, 8])
        
        style.map('TCombobox',
                 fieldbackground=[
                     ('readonly', self.theme.get_color("bg_elevated")),
                     ('focus', self.theme.get_color("bg_elevated"))
                 ],
                 bordercolor=[
                     ('focus', self.theme.get_color("primary")),
                     ('!focus', self.theme.get_color("border"))
                 ])
    
    def _setup_treeview_styles(self, style: ttk.Style):
        """设置树视图样式"""
        style.configure('Treeview',
                       background=self.theme.get_color("bg_elevated"),
                       foreground=self.theme.get_color("text_primary"),
                       fieldbackground=self.theme.get_color("bg_elevated"),
                       relief='solid',
                       bordercolor=self.theme.get_color("border"),
                       lightcolor=self.theme.get_color("bg_elevated"),
                       darkcolor=self.theme.get_color("bg_elevated"),
                       rowheight=40,
                       font=self.fonts["normal"])
        
        style.configure('Treeview.Heading',
                       font=self.fonts["medium"],
                       background=self.theme.get_color("bg_tertiary"),
                       foreground=self.theme.get_color("text_primary"),
                       relief='flat',
                       padding=[12, 10])
        
        style.map('Treeview',
                 background=[
                     ('selected', self.theme.get_color("primary")),
                     ('focus', self.theme.get_color("primary"))
                 ],
                 foreground=[
                     ('selected', self.theme.get_color("text_inverse")),
                     ('focus', self.theme.get_color("text_inverse"))
                 ])
        
        style.map('Treeview.Heading',
                 background=[
                     ('active', self.theme.get_color("bg_secondary"))
                 ])
    
    def _setup_notebook_styles(self, style: ttk.Style):
        """设置标签页样式"""
        style.configure('TNotebook',
                       background=self.theme.get_color("bg_primary"),
                       relief='flat',
                       tabposition='n')
        
        style.configure('TNotebook.Tab',
                       font=self.fonts["normal"],
                       background=self.theme.get_color("bg_secondary"),
                       foreground=self.theme.get_color("text_secondary"),
                       padding=[20, 12],
                       relief='flat',
                       focuscolor='none')
        
        style.map('TNotebook.Tab',
                 background=[
                     ('selected', self.theme.get_color("bg_elevated")),
                     ('active', self.theme.get_color("bg_tertiary"))
                 ],
                 foreground=[
                     ('selected', self.theme.get_color("text_primary")),
                     ('active', self.theme.get_color("text_primary"))
                 ])
    
    def _setup_frame_styles(self, style: ttk.Style):
        """设置框架样式"""
        # 标签框架
        style.configure('TLabelframe',
                       background=self.theme.get_color("bg_primary"),
                       relief='solid',
                       padding=4)
        
        style.configure('TLabelframe.Label',
                       font=self.fonts["medium"],
                       background=self.theme.get_color("bg_primary"),
                       foreground=self.theme.get_color("text_primary"),
                       padding=[8, 4])
        
        # 卡片样式框架
        style.configure('Card.TFrame',
                       background=self.theme.get_color("bg_elevated"),
                       relief='flat')
        
        # 卡片头部样式
        style.configure('CardHeader.TFrame',
                       background=self.theme.get_color("bg_elevated"),
                       relief='flat')
        
        style.configure('CardHeader.TLabel',
                       background=self.theme.get_color("bg_elevated"),
                       foreground=self.theme.get_color("text_primary"),
                       font=self.fonts["medium"])
        
        # 卡片内容样式
        style.configure('CardContent.TFrame',
                       background=self.theme.get_color("bg_elevated"),
                       relief='flat')
    
    def _setup_progressbar_styles(self, style: ttk.Style):
        """设置进度条样式"""
        style.configure('TProgressbar',
                       background=self.theme.get_color("primary"),
                       troughcolor=self.theme.get_color("bg_tertiary"),
                       borderwidth=0,
                       lightcolor=self.theme.get_color("primary"),
                       darkcolor=self.theme.get_color("primary"),
                       thickness=8)
    
    def _setup_scrollbar_styles(self, style: ttk.Style):
        """设置滚动条样式"""
        style.configure('TScrollbar',
                       background=self.theme.get_color("bg_secondary"),
                       troughcolor=self.theme.get_color("bg_tertiary"),
                       borderwidth=0,
                       arrowcolor=self.theme.get_color("text_secondary"),
                       relief='flat',
                       width=12)
        
        style.map('TScrollbar',
                 background=[
                     ('active', self.theme.get_color("bg_tertiary")),
                     ('pressed', self.theme.get_color("border_dark"))
                 ])

class ModernCard(tk.Frame):
    """现代化卡片组件"""
    
    def __init__(self, parent, theme: ModernTheme, title: str = None, **kwargs):
        super().__init__(parent, **kwargs)
        self.theme = theme
        
        # 卡片样式
        self.configure(
            bg=theme.get_color("bg_elevated"),
            relief='flat',
            bd=1,
            highlightbackground=theme.get_color("border"),
            highlightcolor=theme.get_color("border"),
            highlightthickness=1
        )
        
        # 添加内边距
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(1 if title else 0, weight=1)
        
        # 标题
        if title:
            title_frame = tk.Frame(self, bg=theme.get_color("bg_elevated"))
            title_frame.grid(row=0, column=0, sticky="ew", padx=16, pady=(16, 8))
            
            title_label = tk.Label(
                title_frame,
                text=title,
                font=theme.get_font_config()["large"],
                bg=theme.get_color("bg_elevated"),
                fg=theme.get_color("text_primary")
            )
            title_label.pack(anchor="w")
        
        # 内容区域
        self.content_frame = tk.Frame(self, bg=theme.get_color("bg_elevated"))
        self.content_frame.grid(
            row=1 if title else 0,
            column=0,
            sticky="nsew",
            padx=16,
            pady=(0, 16) if title else 16
        )

class ModernButton(tk.Button):
    """现代化按钮组件"""
    
    def __init__(self, parent, theme: ModernTheme, style: str = "primary", **kwargs):
        # 默认样式
        default_config = {
            "font": theme.get_font_config()["normal"],
            "relief": "flat",
            "bd": 0,
            "padx": 16,
            "pady": 10,
            "cursor": "hand2"
        }
        
        # 根据样式设置颜色
        if style == "primary":
            default_config.update({
                "bg": theme.get_color("primary"),
                "fg": theme.get_color("text_inverse"),
                "activebackground": theme.get_color("primary_dark"),
                "activeforeground": theme.get_color("text_inverse")
            })
        elif style == "secondary":
            default_config.update({
                "bg": theme.get_color("bg_elevated"),
                "fg": theme.get_color("text_primary"),
                "activebackground": theme.get_color("bg_tertiary"),
                "activeforeground": theme.get_color("text_primary"),
                "highlightbackground": theme.get_color("border"),
                "highlightcolor": theme.get_color("border"),
                "highlightthickness": 1
            })
        elif style == "success":
            default_config.update({
                "bg": theme.get_color("success"),
                "fg": theme.get_color("text_inverse"),
                "activebackground": "#059669",
                "activeforeground": theme.get_color("text_inverse")
            })
        elif style == "warning":
            default_config.update({
                "bg": theme.get_color("warning"),
                "fg": theme.get_color("text_inverse"),
                "activebackground": "#d97706",
                "activeforeground": theme.get_color("text_inverse")
            })
        elif style == "error":
            default_config.update({
                "bg": theme.get_color("error"),
                "fg": theme.get_color("text_inverse"),
                "activebackground": "#dc2626",
                "activeforeground": theme.get_color("text_inverse")
            })
        
        # 合并用户配置
        default_config.update(kwargs)
        
        super().__init__(parent, **default_config)

# 全局主题实例
modern_theme = ModernTheme()
modern_style_manager = ModernStyleManager(modern_theme) 