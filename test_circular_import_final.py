#!/usr/bin/env python3
"""
Final test to verify circular import is completely resolved
"""

import sys
import os

# Add the football_analysis_system to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'football_analysis_system'))

def test_no_circular_import():
    """Test that both modules can be imported in any order without circular import"""
    
    print("=== Testing Circular Import Resolution ===\n")
    
    # Test 1: Import ConfigManager first
    print("Test 1: Importing ConfigManager first...")
    try:
        from football_analysis_system.core.config_manager import ConfigManager
        print("✓ ConfigManager imported successfully")
        
        # Now import PathResolver
        from football_analysis_system.core.path_resolver import PathResolver
        print("✓ PathResolver imported successfully after ConfigManager")
        
        # Test functionality
        config = ConfigManager()
        path = PathResolver.get_database_path("test.db")
        print(f"✓ Both modules working together: {path}")
        
    except Exception as e:
        print(f"✗ Error in Test 1: {e}")
        return False
    
    # Clear imports for clean test
    modules_to_remove = [k for k in sys.modules.keys() if 'config_manager' in k or 'path_resolver' in k]
    for module in modules_to_remove:
        del sys.modules[module]
    
    # Test 2: Import PathResolver first (this would fail with circular import)
    print("\nTest 2: Importing PathResolver first...")
    try:
        from football_analysis_system.core.path_resolver import PathResolver
        print("✓ PathResolver imported successfully")
        
        # Now import ConfigManager
        from football_analysis_system.core.config_manager import ConfigManager
        print("✓ ConfigManager imported successfully after PathResolver")
        
        # Test functionality
        config = ConfigManager()
        path = PathResolver.get_database_path("test2.db")
        print(f"✓ Both modules working together: {path}")
        
    except Exception as e:
        print(f"✗ Error in Test 2: {e}")
        return False
    
    print("\n🎉 All circular import tests passed!")
    return True

if __name__ == "__main__":
    success = test_no_circular_import()
    sys.exit(0 if success else 1)