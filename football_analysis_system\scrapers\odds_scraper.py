import time
import logging
import re
import requests
import os
import sqlite3
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
from football_analysis_system.config import DATA_DIR
from football_analysis_system.core.path_resolver import PathResolver
from .crawler import CrawlerUtils, ErrorType

class OddsScraper:
    """增强的赔率数据爬虫类"""

    def __init__(self, url_template, headers_template, target_companies, db_path="data/matches_and_odds.db"):
        """
        初始化赔率爬虫

        Args:
            url_template: 请求URL模板
            headers_template: 请求头模板
            target_companies: 目标博彩公司列表
            db_path: 数据库路径
        """
        self.url_template = url_template
        self.headers_template = headers_template
        self.target_companies = target_companies
        
        # 使用PathResolver解析数据库路径，确保向后兼容性
        try:
            self.db_path = PathResolver.resolve_relative_path(db_path)
            logging.debug(f"数据库路径解析: {db_path} -> {self.db_path}")
            
            # 检查是否为外层路径，添加调试信息
            if PathResolver.is_external_data_path(db_path):
                logging.info(f"检测到外层数据库路径 '{db_path}'，已转换为内层路径: {self.db_path}")
            
        except Exception as e:
            # 路径解析失败时的错误处理，回退到原始路径
            logging.warning(f"路径解析失败，使用原始路径: {e}")
            self.db_path = db_path

        # 创建增强的HTTP会话
        self.session = CrawlerUtils.create_session(retry_times=3)

        # 添加线程锁，保证并发安全
        self._lock = threading.Lock()
        
        # 初始化健壮的数据库管理器
        self._init_robust_database_manager()
        
        # 创建历史赔率表
        self._create_history_odds_table()

    def _init_robust_database_manager(self):
        """初始化健壮的数据库管理器"""
        try:
            # 创建健壮的数据库管理器类
            class RobustDatabaseManager:
                def __init__(self, db_path, max_retries=3, timeout=30.0):
                    self.db_path = os.path.abspath(db_path)
                    self.max_retries = max_retries
                    self.timeout = timeout
                    self._lock = threading.Lock()
                    
                    # 确保目录存在
                    os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
                    
                    # 初始化数据库
                    self._initialize_database()
                
                def _initialize_database(self):
                    """初始化数据库和表"""
                    try:
                        with self._lock:
                            conn = sqlite3.connect(self.db_path, timeout=self.timeout)
                            cursor = conn.cursor()
                            
                            # 启用WAL模式以支持并发访问
                            cursor.execute("PRAGMA journal_mode=WAL;")
                            cursor.execute("PRAGMA synchronous=NORMAL;")
                            cursor.execute("PRAGMA cache_size=10000;")
                            cursor.execute("PRAGMA temp_store=MEMORY;")
                            
                            # 创建记录ID表
                            cursor.execute('''
                                CREATE TABLE IF NOT EXISTS odds_record_ids (
                                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                                    match_id TEXT NOT NULL,
                                    company_id TEXT NOT NULL,
                                    company_name TEXT NOT NULL,
                                    record_id TEXT NOT NULL,
                                    update_timestamp TEXT NOT NULL,
                                    scrape_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                    UNIQUE(match_id, company_id, record_id)
                                )
                            ''')
                            
                            # 创建索引
                            cursor.execute('''
                                CREATE INDEX IF NOT EXISTS idx_odds_record_ids_match_company 
                                ON odds_record_ids(match_id, company_id)
                            ''')
                            
                            conn.commit()
                            conn.close()
                            
                            logging.debug("数据库初始化成功")
                            
                    except Exception as e:
                        logging.error(f"数据库初始化失败: {e}")
                        raise
                
                def execute_with_retry(self, operation_func, *args, **kwargs):
                    """带重试机制的数据库操作"""
                    for attempt in range(self.max_retries):
                        try:
                            with self._lock:
                                conn = sqlite3.connect(self.db_path, timeout=self.timeout)
                                try:
                                    result = operation_func(conn, *args, **kwargs)
                                    conn.commit()
                                    return result
                                finally:
                                    conn.close()
                                    
                        except sqlite3.OperationalError as e:
                            if "database is locked" in str(e) or "unable to open" in str(e):
                                if attempt < self.max_retries - 1:
                                    wait_time = (attempt + 1) * 0.5
                                    logging.warning(f"数据库锁定，等待 {wait_time} 秒后重试... (尝试 {attempt + 1}/{self.max_retries})")
                                    time.sleep(wait_time)
                                    continue
                            raise
                        except Exception as e:
                            if attempt < self.max_retries - 1:
                                wait_time = (attempt + 1) * 0.5
                                logging.warning(f"数据库操作失败，等待 {wait_time} 秒后重试: {e}")
                                time.sleep(wait_time)
                                continue
                            raise
                    
                    raise Exception(f"数据库操作在 {self.max_retries} 次尝试后仍然失败")
                
                def save_record_ids(self, match_id, record_ids_data):
                    """保存记录ID到数据库"""
                    if not record_ids_data:
                        return True
                    
                    def _save_operation(conn, match_id, record_ids_data):
                        cursor = conn.cursor()
                        saved_count = 0
                        
                        for company_name, data in record_ids_data.items():
                            cursor.execute('''
                                INSERT OR REPLACE INTO odds_record_ids 
                                (match_id, company_id, company_name, record_id, update_timestamp, scrape_time)
                                VALUES (?, ?, ?, ?, ?, ?)
                            ''', (
                                str(match_id),
                                data['company_id'],
                                data['company_name'],
                                data['record_id'],
                                data['update_timestamp'],
                                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            ))
                            saved_count += 1
                        
                        return saved_count
                    
                    try:
                        saved_count = self.execute_with_retry(_save_operation, match_id, record_ids_data)
                        if saved_count > 0:
                            logging.debug(f"比赛 {match_id} 的记录ID保存完成: {saved_count} 条记录")
                        return True
                        
                    except Exception as e:
                        logging.error(f"保存记录ID失败: {e}")
                        return False
            
            # 创建数据库管理器实例
            self.db_manager = RobustDatabaseManager(self.db_path)
            logging.debug("健壮数据库管理器初始化成功")
            
        except Exception as e:
            logging.error(f"初始化健壮数据库管理器失败: {e}")
            # 回退到原有方法
            self._create_record_id_table()

    def _create_record_id_table(self):
        """创建记录ID存储表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建记录ID表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS odds_record_ids (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    match_id TEXT NOT NULL,
                    company_id TEXT NOT NULL,
                    company_name TEXT NOT NULL,
                    record_id TEXT NOT NULL,
                    update_timestamp TEXT NOT NULL,
                    scrape_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(match_id, company_id, record_id)
                )
            ''')
            
            # 创建索引以提高查询性能
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_odds_record_ids_match_company 
                ON odds_record_ids(match_id, company_id)
            ''')
            
            conn.commit()
            conn.close()
            
            logging.debug("记录ID表初始化成功")
            
        except Exception as e:
            logging.error(f"创建记录ID表失败: {e}")
            if 'conn' in locals():
                conn.close()

    def _create_history_odds_table(self):
        """创建历史赔率表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建历史赔率表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS history_odds (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    match_id TEXT NOT NULL,
                    target_name TEXT NOT NULL,
                    company_id TEXT NOT NULL,
                    company_name TEXT NOT NULL,
                    record_id TEXT NOT NULL,
                    home_odds REAL,
                    draw_odds REAL,
                    away_odds REAL,
                    payout_rate REAL,
                    update_time TEXT,
                    kelly_home REAL,
                    kelly_draw REAL,
                    kelly_away REAL,
                    year TEXT,
                    scrape_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 如果表已存在但缺少payout_rate字段，则添加该字段
            try:
                cursor.execute('ALTER TABLE history_odds ADD COLUMN payout_rate REAL;')
                logging.debug("已为history_odds表添加payout_rate字段")
            except sqlite3.OperationalError:
                # 字段已存在或其他错误，忽略
                pass
            
            # 创建索引以提高查询性能
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_history_odds_match_target 
                ON history_odds(match_id, target_name, update_time)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_history_odds_company 
                ON history_odds(company_id, match_id)
            ''')
            
            conn.commit()
            conn.close()
            
            logging.debug("历史赔率表初始化成功")
            
        except Exception as e:
            logging.error(f"创建历史赔率表失败: {e}")
            if 'conn' in locals():
                conn.close()

    def _save_record_ids_to_db(self, match_id, record_ids_data):
        """
        将记录ID数据保存到数据库
        
        Args:
            match_id: 比赛ID
            record_ids_data: 记录ID数据字典
        """
        if not record_ids_data:
            return
        
        # 使用健壮的数据库管理器
        if hasattr(self, 'db_manager'):
            try:
                success = self.db_manager.save_record_ids(match_id, record_ids_data)
                if success:
                    logging.info(f"比赛 {match_id} 的记录ID保存完成: {len(record_ids_data)} 条记录")
                return
            except Exception as e:
                logging.error(f"健壮数据库管理器保存失败，回退到原方法: {e}")
        
        # 回退到原有方法
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            saved_count = 0
            
            for company_name, data in record_ids_data.items():
                try:
                    # 使用 INSERT OR REPLACE 来处理重复数据
                    cursor.execute('''
                        INSERT OR REPLACE INTO odds_record_ids 
                        (match_id, company_id, company_name, record_id, update_timestamp, scrape_time)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        str(match_id),
                        data['company_id'],
                        data['company_name'],
                        data['record_id'],
                        data['update_timestamp'],
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ))
                    
                    if cursor.rowcount > 0:
                        saved_count += 1
                        logging.debug(f"保存记录ID: {company_name} -> {data['record_id']}")
                    
                except Exception as e:
                    logging.error(f"保存公司 {company_name} 的记录ID失败: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            if saved_count > 0:
                logging.info(f"比赛 {match_id} 的记录ID保存完成: {saved_count} 条记录")
            
        except Exception as e:
            logging.error(f"保存记录ID到数据库失败: {e}")
            if 'conn' in locals():
                conn.close()

    def _get_target_company_mapping(self, odds_js_content):
        """从game数组获取目标公司的ID到名称的映射"""
        company_mapping = {}
        
        # 查找game数组
        game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', odds_js_content, re.DOTALL)
        if not game_match:
            logging.warning("未找到game数组")
            return {}
        
        game_data = game_match.group(1)
        company_records = re.findall(r'"([^"]*)"', game_data)
        
        for record in company_records:
            fields = record.split('|')
            if len(fields) >= 3:
                company_id = fields[0]
                record_id = fields[1]
                company_name = fields[2].strip()
                
                # 匹配目标公司
                matched_target_name = None
                for target_name in self.target_companies:
                    normalized_target_name = target_name.lower().replace(' ','')
                    normalized_company_name = company_name.lower().replace(' ','')
                    
                    # 精确匹配
                    if normalized_target_name == normalized_company_name:
                        matched_target_name = target_name
                        break
                    
                    # 特殊匹配规则
                    if target_name == '威廉希尔' and (company_id == '115' or company_name.startswith('威') or 'william hill' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == '易胜博' and (company_id == '90' or company_name.startswith('易') or 'easybets' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == '香港马会' and (company_id == '432' or company_name.startswith('香港马') or 'hkjc' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == '澳门' and (company_id == '80' or company_name.startswith('澳') or 'macao' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == 'BWIN' and (company_id == '255' or company_name.startswith('Bwi')):
                        matched_target_name = target_name
                        break
                    if target_name == '伟德' and (company_id == '81' or company_name.startswith('伟')):
                        matched_target_name = target_name
                        break
                    if target_name == 'Nordicbet' and (company_id == '4' or company_name.startswith('Nordic')):
                        matched_target_name = target_name
                        break
                    if target_name == '利记' and (company_id == '474' or company_name.startswith('利')):
                        matched_target_name = target_name
                        break
                    if target_name == 'BetISn' and (company_id == '937' or 'betisn' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == 'iddaa' and (company_id == '657' or 'iddaa' in normalized_company_name):
                        matched_target_name = target_name
                        break
                
                if matched_target_name:
                    company_mapping[record_id] = {
                        'company_id': company_id,
                        'company_name': company_name,
                        'target_name': matched_target_name
                    }
        
        logging.debug(f"匹配到 {len(company_mapping)} 家目标公司")
        return company_mapping

    def _parse_target_companies_history(self, odds_js_content, match_id):
        """解析目标公司的历史赔率数据"""
        
        # 获取目标公司映射
        company_mapping = self._get_target_company_mapping(odds_js_content)
        if not company_mapping:
            logging.debug("未找到任何目标公司")
            return {}
        
        # 提取gameDetail数据
        gamedetail_match = re.search(r'var\s+gameDetail\s*=\s*Array\((.*?)\);', odds_js_content, re.DOTALL)
        if not gamedetail_match:
            logging.debug("未找到gameDetail数组")
            return {}
        
        gamedetail_data = gamedetail_match.group(1)
        company_records = re.findall(r'"([^"]*)"', gamedetail_data)
        
        target_companies_history = {}
        
        for record in company_records:
            try:
                # gameDetail格式: record_id^历史数据
                parts = record.split('^')
                if len(parts) < 2:
                    continue
                
                record_id = parts[0]
                history_data = parts[1]
                
                # 检查是否为目标公司
                if record_id not in company_mapping:
                    continue
                
                company_info = company_mapping[record_id]
                target_name = company_info['target_name']
                
                # 解析历史数据，用分号分隔不同时间点的记录
                history_entries = history_data.split(';')
                
                odds_history = []
                for entry in history_entries:
                    if entry.strip():
                        # 格式: 主胜|平局|客胜|时间|kelly1|kelly2|kelly3|年份|返还率(可能)
                        parts_entry = entry.split('|')
                        
                        if len(parts_entry) >= 8:
                            try:
                                # 计算返还率：1 / (1/主胜 + 1/平局 + 1/客胜) * 100
                                payout_rate = None
                                try:
                                    home_odds = float(parts_entry[0]) if parts_entry[0] else 0
                                    draw_odds = float(parts_entry[1]) if parts_entry[1] else 0
                                    away_odds = float(parts_entry[2]) if parts_entry[2] else 0
                                    
                                    if home_odds > 0 and draw_odds > 0 and away_odds > 0:
                                        payout_rate = 1 / (1/home_odds + 1/draw_odds + 1/away_odds) * 100
                                except (ValueError, ZeroDivisionError):
                                    payout_rate = None
                                
                                history_record = {
                                    'home_odds': float(parts_entry[0]) if parts_entry[0] else None,
                                    'draw_odds': float(parts_entry[1]) if parts_entry[1] else None,
                                    'away_odds': float(parts_entry[2]) if parts_entry[2] else None,
                                    'payout_rate': payout_rate,
                                    'update_time': parts_entry[3],  # 格式: MM-DD HH:MM
                                    'kelly_home': float(parts_entry[4]) if parts_entry[4] else None,
                                    'kelly_draw': float(parts_entry[5]) if parts_entry[5] else None,
                                    'kelly_away': float(parts_entry[6]) if parts_entry[6] else None,
                                    'year': parts_entry[7]
                                }
                                odds_history.append(history_record)
                            except ValueError:
                                continue
                
                if odds_history:
                    target_companies_history[target_name] = {
                        'company_id': company_info['company_id'],
                        'company_name': company_info['company_name'],
                        'record_id': record_id,
                        'target_name': target_name,
                        'history_count': len(odds_history),
                        'odds_history': odds_history
                    }
                    
            except Exception as e:
                logging.debug(f"解析历史记录失败: {e}")
                continue
        
        logging.debug(f"解析出 {len(target_companies_history)} 家目标公司的历史赔率")
        return target_companies_history

    def _save_history_odds_to_db(self, match_id, target_companies_history):
        """将历史赔率数据保存到数据库"""
        
        if not target_companies_history:
            return
        
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            saved_count = 0
            
            for target_name, company_data in target_companies_history.items():
                company_id = company_data['company_id']
                company_name = company_data['company_name']
                record_id = company_data['record_id']
                
                for history_entry in company_data['odds_history']:
                    try:
                        cursor.execute('''
                            INSERT OR REPLACE INTO history_odds 
                            (match_id, target_name, company_id, company_name, record_id, 
                             home_odds, draw_odds, away_odds, payout_rate, update_time, 
                             kelly_home, kelly_draw, kelly_away, year, scrape_time)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            str(match_id),
                            target_name,
                            company_id,
                            company_name,
                            record_id,
                            history_entry['home_odds'],
                            history_entry['draw_odds'],
                            history_entry['away_odds'],
                            history_entry['payout_rate'],
                            history_entry['update_time'],
                            history_entry['kelly_home'],
                            history_entry['kelly_draw'],
                            history_entry['kelly_away'],
                            history_entry['year'],
                            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        ))
                        saved_count += 1
                    except Exception as e:
                        logging.debug(f"保存历史赔率记录失败: {e}")
                        continue
            
            conn.commit()
            conn.close()
            
            if saved_count > 0:
                logging.info(f"比赛 {match_id} 的历史赔率保存完成: {saved_count} 条记录")
            
        except Exception as e:
            logging.error(f"保存历史赔率到数据库失败: {e}")
            if 'conn' in locals():
                conn.close()

    def get_timestamp_param(self):
        """
        生成增强的时间戳参数

        Returns:
            str: 格式化的时间戳参数
        """
        timestamp_part = str(int(time.time() * 1000))
        if len(timestamp_part) > 13:
            timestamp_part = timestamp_part[-13:]
        else:
            timestamp_part = timestamp_part.ljust(13, '0')
        return "007" + timestamp_part

    def fetch_data_with_retry(self, url, headers, max_retries=3, backoff_factor=1.2):
        """
        增强的带重试机制的数据获取函数

        Args:
            url: 请求的URL
            headers: 请求头信息
            max_retries: 最大重试次数
            backoff_factor: 退避因子，控制重试等待时间

        Returns:
            str: 响应内容，失败则返回None
        """
        # 使用增强的CrawlerUtils的fetch_data方法，减少延迟
        return CrawlerUtils.fetch_data(
            self.session,
            url,
            min_delay=0.1,  # 从0.3减少到0.1
            max_delay=0.4,  # 从0.8减少到0.4
            timeout=20,     # 从25减少到20
            retry_times=max_retries
        )

    def fetch_odds_js(self, match_id):
        """
        获取特定比赛的赔率JavaScript文件

        Args:
            match_id: 比赛ID

        Returns:
            str: JavaScript内容
        """
        if not match_id:
            logging.error("获取赔率需要比赛ID")
            return None

        timestamp = self.get_timestamp_param()
        url = self.url_template.format(match_id=match_id, timestamp=timestamp)

        headers = self.headers_template.copy()
        headers['Referer'] = headers['Referer'].format(match_id=match_id)

        # 增强请求头
        headers.update({
            'Accept': 'text/javascript, application/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache'
        })

        # 使用增强的重试机制获取数据
        response_text = self.fetch_data_with_retry(url, headers, max_retries=4)

        if response_text:
            # 保存原始JS以便调试（仅DEBUG模式）
            if logging.getLogger().level <= logging.DEBUG:
                try:
                    debug_file = os.path.join(DATA_DIR, f"odds_js_{match_id}.txt")
                    with open(debug_file, "w", encoding="utf-8") as f:
                        f.write(response_text)
                except Exception as e:
                    logging.debug(f"保存调试文件失败: {e}")

            logging.info(f"成功获取比赛ID {match_id} 的赔率数据")

        return response_text

    def parse_odds_js(self, odds_js_content, match_id=None, save_record_ids=False):
        """
        解析赔率JavaScript内容，提取目标公司的赔率数据

        Args:
            odds_js_content: JavaScript内容
            match_id: 比赛ID（用于保存记录ID）
            save_record_ids: 是否保存记录ID到数据库

        Returns:
            dict: 公司名称到赔率数据的映射
        """
        if not odds_js_content:
            logging.warning("没有赔率JS内容可解析")
            return {}

        odds_data = {}
        record_ids_data = {}  # 新增：存储记录ID数据

        # 查找game数组
        game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', odds_js_content, re.DOTALL)
        if not game_match:
            logging.warning("在JS内容中未找到game数组")
            return {}

        game_data = game_match.group(1)

        # 提取每个引号包围的字符串
        company_records = re.findall(r'"([^"]*)"', game_data)

        found_count = 0
        # 解析每个公司记录
        for record in company_records:
            try:
                fields = record.split('|')

                if len(fields) < 16:
                    continue  # 跳过字段不足的记录

                company_id = fields[0]
                record_id = fields[1]  # 新增：记录ID
                company_name = fields[2].strip()

                # 初始赔率 - 索引3,4,5
                initial_w = fields[3].strip()  # 主胜
                initial_d = fields[4].strip()  # 平
                initial_l = fields[5].strip()  # 客胜

                # 即时赔率 - 索引10,11,12
                instant_w = fields[10].strip()  # 主胜
                instant_d = fields[11].strip()  # 平
                instant_l = fields[12].strip()  # 客胜

                # 返还率 - 索引9为初始返还率，索引16为即时返还率
                initial_payout_rate = fields[9].strip() if len(fields) > 9 else ""
                payout_rate = fields[16].strip() if len(fields) > 16 else ""
                
                # 新增：更新时间戳 - 索引20
                update_timestamp = fields[20].strip() if len(fields) > 20 else ""

                # 添加调试日志以验证返还率提取是否正确
                if company_id in ['80', '432', '1132', '657']:  # 澳门、香港马会、Coolbet和iddaa
                    company_display_name = ""
                    if company_id == '80':
                        company_display_name = "澳门"
                    elif company_id == '432':
                        company_display_name = "香港马会"
                    elif company_id == '1132':
                        company_display_name = "Coolbet"
                    elif company_id == '657':
                        company_display_name = "iddaa"
                    logging.info(f"{company_display_name} - 初始返还率(索引9): {initial_payout_rate}, 即时返还率(索引16): {payout_rate}")

                # 匹配目标公司
                matched_target_name = None
                for target_name in self.target_companies:
                    normalized_target_name = target_name.lower().replace(' ','')
                    normalized_company_name = company_name.lower().replace(' ','')

                    # 精确匹配
                    if normalized_target_name == normalized_company_name:
                        matched_target_name = target_name
                        break

                    # 特殊匹配规则
                    # 使用ID和名称前缀进行精确匹配
                    if target_name == '威廉希尔' and (company_id == '115' or company_name.startswith('威') or 'william hill' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == '易胜博' and (company_id == '90' or company_name.startswith('易') or 'easybets' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    # 添加香港马会的匹配规则
                    if target_name == '香港马会' and (company_id == '432' or company_name.startswith('香港马') or 'hkjc' in normalized_company_name):
                        matched_target_name = target_name
                        logging.info(f"匹配到香港马会数据: ID={company_id}, 名称={company_name}")
                        break
                    # 添加澳门的匹配规则
                    if target_name == '澳门' and (company_id == '80' or company_name.startswith('澳') or 'macao' in normalized_company_name):
                        matched_target_name = target_name
                        logging.info(f"匹配到澳门赔率数据: ID={company_id}, 名称={company_name}")
                        break
                    # 添加BWIN的匹配规则
                    if target_name == 'BWIN' and (company_id == '255' or company_name.startswith('Bwi')):
                        matched_target_name = target_name
                        break
                    # 添加伟德的匹配规则
                    if target_name == '伟德' and (company_id == '81' or company_name.startswith('伟')):
                        matched_target_name = target_name
                        break
                    # 添加Nordicbet的匹配规则
                    if target_name == 'Nordicbet' and (company_id == '4' or company_name.startswith('Nordic')):
                        matched_target_name = target_name
                        break
                    # 添加利记的匹配规则
                    if target_name == '利记' and (company_id == '474' or company_name.startswith('利')):
                        matched_target_name = target_name
                        break
                    # 添加BetISn的匹配规则
                    if target_name == 'BetISn' and (company_id == '937' or 'betisn' in normalized_company_name):
                        matched_target_name = target_name
                        logging.info(f"匹配到BetISn公司数据: ID={company_id}, 名称={company_name}")
                        break
                    # 添加iddaa的匹配规则
                    if target_name == 'iddaa' and (company_id == '657' or 'iddaa' in normalized_company_name):
                        matched_target_name = target_name
                        logging.info(f"匹配到iddaa公司数据: ID={company_id}, 名称={company_name}")
                        break

                if matched_target_name:
                    odds_data[matched_target_name] = {
                        'Initial_W': initial_w,
                        'Initial_D': initial_d,
                        'Initial_L': initial_l,
                        'Instant_W': instant_w,
                        'Instant_D': instant_d,
                        'Instant_L': instant_l,
                        'Payout_Rate': payout_rate,
                        'Initial_Payout_Rate': initial_payout_rate,  # 确保键名一致
                    }

                    # 新增：存储记录ID数据
                    record_ids_data[matched_target_name] = {
                        'company_id': company_id,
                        'company_name': company_name,
                        'record_id': record_id,
                        'update_timestamp': update_timestamp
                    }

                    # 为主要公司添加额外的调试信息
                    if company_id in ['80', '432', '1132', '657']:  # 澳门、香港马会、Coolbet和iddaa
                        logging.info(f"存储赔率数据 - {matched_target_name} 初始返还率: {initial_payout_rate}, 即时返还率: {payout_rate}")
                        logging.debug(f"记录ID: {record_id}, 更新时间: {update_timestamp}")

                    found_count += 1

            except IndexError:
                continue
            except Exception as e:
                logging.error(f"处理记录时出错: {e}")
                continue

        if not odds_data:
            # 尝试备用方法解析
            odds_data = self.parse_odds_js_gamedetail(odds_js_content)

        # 新增：保存记录ID到数据库
        if save_record_ids and match_id and record_ids_data:
            self._save_record_ids_to_db(match_id, record_ids_data)

        if not odds_data:
            logging.warning("未能为任何目标公司提取赔率数据")

        return odds_data

    def parse_odds_js_gamedetail(self, odds_js_content):
        """
        尝试从gameDetail数组中解析赔率数据（备选方法）

        Args:
            odds_js_content: JavaScript内容

        Returns:
            dict: 公司名称到赔率数据的映射
        """
        if not odds_js_content:
            logging.warning("没有赔率JS内容可解析")
            return {}

        odds_data = {}

        # 查找gameDetail数组
        gamedetail_match = re.search(r'var\s+gameDetail\s*=\s*Array\((.*?)\);', odds_js_content, re.DOTALL)
        if not gamedetail_match:
            logging.warning("在JS内容中未找到gameDetail数组")
            return {}

        gamedetail_data = gamedetail_match.group(1)

        # 提取每个引号包围的字符串
        company_records = re.findall(r'"([^"]*)"', gamedetail_data)

        # 公司ID到公司名称的映射
        company_id_name_map = {}

        # 先从主game数组中提取公司ID到名称的映射
        game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', odds_js_content, re.DOTALL)
        if game_match:
            game_data = game_match.group(1)
            game_records = re.findall(r'"([^"]*)"', game_data)

            for record in game_records:
                fields = record.split('|')
                if len(fields) > 2:
                    company_id = fields[0]
                    company_name = fields[2]
                    company_id_name_map[company_id] = company_name

        found_count = 0
        # 解析每条公司记录
        for record in company_records:
            try:
                fields = record.split('^')

                if len(fields) < 2:
                    continue  # 跳过字段不足的记录

                company_id = fields[0]

                # 获取公司名称
                company_name = company_id_name_map.get(company_id, f"Unknown_{company_id}")

                # gameDetail第一部分通常包含最新赔率
                odds_parts = fields[1].split('|')
                if len(odds_parts) >= 3:
                    instant_w = odds_parts[0].strip()
                    instant_d = odds_parts[1].strip()
                    instant_l = odds_parts[2].strip()

                    # 匹配目标公司
                    matched_target_name = None
                    for target_name in self.target_companies:
                        normalized_target_name = target_name.lower().replace(' ','')
                        normalized_company_name = company_name.lower().replace(' ','')

                        # 精确匹配
                        if normalized_target_name == normalized_company_name:
                            matched_target_name = target_name
                            break

                        # 特殊匹配规则（与主方法保持一致）
                        if target_name == '威廉希尔' and (company_id == '115' or company_name.startswith('威') or 'william hill' in normalized_company_name):
                            matched_target_name = target_name
                            break
                        if target_name == '易胜博' and (company_id == '90' or company_name.startswith('易') or 'easybets' in normalized_company_name):
                            matched_target_name = target_name
                            break
                        # 其他特殊匹配规则...
                        if target_name == '香港马会' and (company_id == '432' or company_name.startswith('香港马') or 'hkjc' in normalized_company_name):
                            matched_target_name = target_name
                            break
                        if target_name == '澳门' and (company_id == '80' or company_name.startswith('澳') or 'macao' in normalized_company_name):
                            matched_target_name = target_name
                            break

                    if matched_target_name:
                        odds_data[matched_target_name] = {
                            'Initial_W': instant_w,  # gameDetail方法中用即时赔率代替初始赔率
                            'Initial_D': instant_d,
                            'Initial_L': instant_l,
                            'Instant_W': instant_w,
                            'Instant_D': instant_d,
                            'Instant_L': instant_l,
                            'Payout_Rate': '',  # gameDetail方法中没有返还率信息
                            'Initial_Payout_Rate': '',
                        }
                        found_count += 1

            except Exception as e:
                logging.error(f"处理gameDetail记录时出错: {e}")
                continue

        logging.info(f"gameDetail备选方法找到 {found_count} 家公司的赔率数据")
        return odds_data

    def debug_game_content(self, match_id, save_to_file=True):
        """
        调试方法：详细分析var game参数的内容结构
        
        Args:
            match_id: 比赛ID
            save_to_file: 是否保存详细分析结果到文件
            
        Returns:
            dict: 包含原始数据和解析结果的详细信息
        """
        print(f"\n=== 开始分析比赛ID {match_id} 的var game内容 ===")
        
        # 获取原始JS内容
        odds_js_content = self.fetch_odds_js(match_id)
        if not odds_js_content:
            print("❌ 无法获取JS内容")
            return None
            
        # 查找game数组
        game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', odds_js_content, re.DOTALL)
        if not game_match:
            print("❌ 在JS内容中未找到game数组")
            return None
            
        game_data = game_match.group(1)
        print(f"✅ 找到game数组，原始长度: {len(game_data)} 字符")
        
        # 提取每个引号包围的字符串
        company_records = re.findall(r'"([^"]*)"', game_data)
        print(f"✅ 解析出 {len(company_records)} 条公司记录")
        
        debug_info = {
            'match_id': match_id,
            'total_records': len(company_records),
            'raw_game_data': game_data[:500] + "..." if len(game_data) > 500 else game_data,
            'parsed_records': []
        }
        
        print(f"\n--- 前10条记录的详细分析 ---")
        for i, record in enumerate(company_records[:10]):  # 只显示前10条
            fields = record.split('|')
            
            record_info = {
                'record_index': i,
                'total_fields': len(fields),
                'raw_record': record,
                'parsed_fields': {}
            }
            
            print(f"\n记录 {i+1}:")
            print(f"  总字段数: {len(fields)}")
            print(f"  原始记录: {record[:100]}{'...' if len(record) > 100 else ''}")
            
            # 解析已知的关键字段
            if len(fields) > 0:
                record_info['parsed_fields']['company_id'] = fields[0]
                print(f"  [字段0] 公司ID: {fields[0]}")
                
            if len(fields) > 2:
                record_info['parsed_fields']['company_name'] = fields[2]
                print(f"  [字段2] 公司名称: {fields[2]}")
                
            if len(fields) > 3:
                record_info['parsed_fields']['initial_w'] = fields[3]
                print(f"  [字段3] 初始主胜: {fields[3]}")
                
            if len(fields) > 4:
                record_info['parsed_fields']['initial_d'] = fields[4]
                print(f"  [字段4] 初始平局: {fields[4]}")
                
            if len(fields) > 5:
                record_info['parsed_fields']['initial_l'] = fields[5]
                print(f"  [字段5] 初始客胜: {fields[5]}")
                
            if len(fields) > 9:
                record_info['parsed_fields']['initial_payout_rate'] = fields[9]
                print(f"  [字段9] 初始返还率: {fields[9]}")
                
            if len(fields) > 10:
                record_info['parsed_fields']['instant_w'] = fields[10]
                print(f"  [字段10] 即时主胜: {fields[10]}")
                
            if len(fields) > 11:
                record_info['parsed_fields']['instant_d'] = fields[11]
                print(f"  [字段11] 即时平局: {fields[11]}")
                
            if len(fields) > 12:
                record_info['parsed_fields']['instant_l'] = fields[12]
                print(f"  [字段12] 即时客胜: {fields[12]}")
                
            if len(fields) > 16:
                record_info['parsed_fields']['payout_rate'] = fields[16]
                print(f"  [字段16] 即时返还率: {fields[16]}")
                
            # 显示所有字段的索引和值
            print(f"  完整字段列表:")
            for idx, field in enumerate(fields):
                if idx < 20:  # 只显示前20个字段
                    print(f"    [{idx:2d}] {field}")
                elif idx == 20:
                    print(f"    ... (还有 {len(fields)-20} 个字段)")
                    break
                    
            debug_info['parsed_records'].append(record_info)
            
        # 保存到文件
        if save_to_file:
            try:
                debug_file = os.path.join(DATA_DIR, f"game_debug_{match_id}.txt")
                with open(debug_file, "w", encoding="utf-8") as f:
                    f.write(f"比赛ID: {match_id}\n")
                    f.write(f"总记录数: {len(company_records)}\n\n")
                    f.write("=== 原始game数据 (前1000字符) ===\n")
                    f.write(game_data[:1000] + ("..." if len(game_data) > 1000 else ""))
                    f.write("\n\n=== 所有解析记录 ===\n")
                    
                    for i, record in enumerate(company_records):
                        f.write(f"\n--- 记录 {i+1} ---\n")
                        fields = record.split('|')
                        f.write(f"字段数: {len(fields)}\n")
                        f.write(f"原始: {record}\n")
                        f.write("字段解析:\n")
                        for idx, field in enumerate(fields):
                            f.write(f"  [{idx:2d}] {field}\n")
                            
                print(f"✅ 详细调试信息已保存到: {debug_file}")
                
            except Exception as e:
                print(f"❌ 保存调试文件失败: {e}")
                
        print(f"\n=== 分析完成 ===")
        return debug_info

    def get_company_odds(self, match_id, max_retries=3, save_record_ids=True, save_history_odds=True):
        """
        获取指定比赛的指定公司赔率数据

        Args:
            match_id: 比赛ID
            max_retries: 最大重试次数
            save_record_ids: 是否保存记录ID到数据库
            save_history_odds: 是否保存历史赔率到数据库

        Returns:
            dict: 公司名称到赔率数据的映射
        """
        # 使用重试机制
        for retry in range(max_retries):
            try:
                odds_js_content = self.fetch_odds_js(match_id)

                if not odds_js_content:
                    logging.warning(f"获取比赛ID {match_id} 的赔率数据失败 (尝试 {retry+1}/{max_retries})")
                    if retry < max_retries - 1:
                        wait_time = 1.5 ** retry
                        logging.info(f"等待 {wait_time:.2f} 秒后重试...")
                        time.sleep(wait_time)
                    continue

                # 解析当前赔率数据
                odds_data = self.parse_odds_js(odds_js_content, match_id=match_id, save_record_ids=save_record_ids)
                
                # 解析并保存历史赔率数据
                if save_history_odds:
                    try:
                        target_companies_history = self._parse_target_companies_history(odds_js_content, match_id)
                        if target_companies_history:
                            self._save_history_odds_to_db(match_id, target_companies_history)
                            logging.debug(f"比赛 {match_id} 历史赔率解析完成，找到 {len(target_companies_history)} 家目标公司")
                    except Exception as e:
                        logging.error(f"解析或保存历史赔率失败: {e}")
                
                if odds_data:
                    logging.info(f"成功解析比赛ID {match_id} 的赔率数据，找到 {len(odds_data)} 家公司")
                    return odds_data
                else:
                    logging.warning(f"解析比赛ID {match_id} 的赔率数据失败 (尝试 {retry+1}/{max_retries})")
                    if retry < max_retries - 1:
                        wait_time = 1.5 ** retry
                        logging.info(f"等待 {wait_time:.2f} 秒后重试...")
                        time.sleep(wait_time)
            except Exception as e:
                logging.error(f"处理比赛ID {match_id} 的赔率数据时出错: {e}")
                if retry < max_retries - 1:
                    wait_time = 1.5 ** retry
                    logging.info(f"等待 {wait_time:.2f} 秒后重试...")
                    time.sleep(wait_time)

        logging.error(f"在 {max_retries} 次尝试后仍然无法获取比赛ID {match_id} 的赔率数据")
        return {}

    def get_odds_for_matches(self, match_ids, max_workers=8, batch_size=20, delay_between_batches=1.0, max_retries=3, progress_callback=None):
        """
        增强的并行获取多场比赛的赔率数据

        Args:
            match_ids: 比赛ID列表
            max_workers: 最大线程数
            batch_size: 每批处理的比赛数量
            delay_between_batches: 批次间延迟(秒)
            max_retries: 每个请求的最大重试次数
            progress_callback: 进度回调函数，接收 (processed, total, success_count) 参数

        Returns:
            dict: 比赛ID到赔率数据的映射
        """
        all_odds_data = {}
        total_matches = len(match_ids)
        success_count = 0

        logging.info(f"开始获取 {total_matches} 场比赛的赔率数据，分 {(total_matches-1)//batch_size + 1} 批处理")

        # 创建总进度条
        with tqdm(total=total_matches, desc="获取赔率数据", unit="场") as pbar:
            # 分批处理
            for i in range(0, total_matches, batch_size):
                batch_ids = match_ids[i:i+batch_size]
                batch_num = i//batch_size + 1
                total_batches = (total_matches-1)//batch_size + 1

                logging.info(f"处理批次 {batch_num}/{total_batches}, 包含 {len(batch_ids)} 场比赛")

                # 为每批创建线程池，确保资源被及时释放
                with ThreadPoolExecutor(max_workers=min(max_workers, len(batch_ids))) as executor:
                    # 创建Future到match_id的映射
                    future_to_match_id = {
                        executor.submit(self.get_company_odds, match_id, max_retries): match_id
                        for match_id in batch_ids
                    }

                    # 处理完成的Future
                    batch_success = 0
                    for future in as_completed(future_to_match_id):
                        match_id = future_to_match_id[future]

                        try:
                            odds_data = future.result()
                            if odds_data:
                                all_odds_data[match_id] = odds_data
                                batch_success += 1
                                success_count += 1
                                logging.debug(f"成功获取比赛ID {match_id} 的赔率数据: {len(odds_data)} 家公司")
                            else:
                                logging.warning(f"未获取到比赛ID {match_id} 的有效赔率数据")
                        except Exception as e:
                            logging.error(f"处理比赛ID {match_id} 的赔率数据时出错: {e}")

                        # 更新进度条
                        pbar.update(1)

                        # 调用进度回调
                        if progress_callback:
                            processed = min(i + len([f for f in future_to_match_id if f.done()]), total_matches)
                            progress_callback(processed, total_matches, success_count)

                # 批次处理完成，打印进度
                processed = min(i + batch_size, total_matches)
                logging.info(f"批次 {batch_num}/{total_batches} 完成: 成功 {batch_success}/{len(batch_ids)} 场，累计 {success_count}/{processed} 场")

                # 批次间延迟，避免请求过于密集
                if i + batch_size < total_matches:
                    logging.info(f"等待 {delay_between_batches} 秒后继续下一批次...")
                    time.sleep(delay_between_batches)

        # 最终统计
        success_rate = success_count / total_matches if total_matches > 0 else 0
        logging.info(f"所有比赛处理完成: 总计 {total_matches} 场, 成功 {success_count} 场, 成功率 {success_rate:.1%}")

        # 显示爬虫统计信息
        stats = self.get_stats()
        logging.info(f"爬虫统计: 成功率 {stats.get('success_rate', 0):.1%}")

        return all_odds_data


    def get_stats(self):
        """获取爬虫统计信息"""
        crawler_stats = CrawlerUtils.get_stats()

        # 合并统计信息
        combined_stats = {
            **crawler_stats,
            'circuit_breaker': CrawlerUtils.get_circuit_breaker_status()
        }

        return combined_stats

    def clear_database(self):
        """
        清空1X2赔率数据库

        Returns:
            bool: 是否清空成功
        """
        try:
            # 获取数据库路径
            from config import DB_MATCHES

            # 连接数据库
            conn = sqlite3.connect(DB_MATCHES)
            cursor = conn.cursor()

            # 清空1X2赔率表
            cursor.execute("DELETE FROM odds")

            # 获取删除的行数
            deleted_rows = cursor.rowcount

            # 提交更改
            conn.commit()
            conn.close()

            logging.info(f"✅ 成功清空1X2赔率数据库，删除了 {deleted_rows} 条记录")
            return True

        except Exception as e:
            logging.error(f"❌ 清空1X2赔率数据库失败: {e}")
            if 'conn' in locals():
                conn.close()
            return False

    def clear_database_for_match(self, match_id):
        """
        清空指定比赛的1X2赔率数据

        Args:
            match_id: 比赛ID

        Returns:
            bool: 是否清空成功
        """
        try:
            # 获取数据库路径
            from config import DB_MATCHES

            # 连接数据库
            conn = sqlite3.connect(DB_MATCHES)
            cursor = conn.cursor()

            # 清空指定比赛的1X2赔率
            cursor.execute("DELETE FROM odds WHERE match_id = ?", (str(match_id),))

            # 获取删除的行数
            deleted_rows = cursor.rowcount

            # 提交更改
            conn.commit()
            conn.close()

            logging.info(f"✅ 成功清空比赛 {match_id} 的1X2赔率数据，删除了 {deleted_rows} 条记录")
            return True

        except Exception as e:
            logging.error(f"❌ 清空比赛 {match_id} 的1X2赔率数据失败: {e}")
            if 'conn' in locals():
                conn.close()
            return False

def test_game_content_analysis(match_id, url_template, headers_template, target_companies):
    """
    测试函数：分析指定比赛的var game内容
    
    Args:
        match_id: 比赛ID
        url_template: URL模板
        headers_template: 请求头模板  
        target_companies: 目标公司列表
    """
    print(f"正在初始化赔率爬虫...")
    scraper = OddsScraper(url_template, headers_template, target_companies)
    
    print(f"开始分析比赛ID {match_id} 的var game内容...")
    result = scraper.debug_game_content(match_id, save_to_file=True)
    
    if result:
        print(f"\n=== 分析摘要 ===")
        print(f"比赛ID: {result['match_id']}")
        print(f"总记录数: {result['total_records']}")
        print(f"成功解析记录数: {len(result['parsed_records'])}")
    
    return result

if __name__ == "__main__":
    # 示例调用 - 您需要提供实际的参数
    print("这是一个调试脚本，请在其他地方调用 test_game_content_analysis 函数")
    print("示例调用:")
    print("result = test_game_content_analysis('您的比赛ID', '您的URL模板', 您的headers字典, ['目标公司列表'])")