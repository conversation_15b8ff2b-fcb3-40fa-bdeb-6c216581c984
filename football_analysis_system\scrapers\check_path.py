import os
import sys
import time

print("=== 路径检查开始 ===", file=sys.stderr)
print("当前工作目录:", os.getcwd(), file=sys.stderr)
print("脚本目录:", os.path.dirname(os.path.abspath(__file__)), file=sys.stderr)
print("上级目录:", os.path.dirname(os.path.dirname(os.path.abspath(__file__))), file=sys.stderr)

# 定义项目根目录 - 脚本所在目录的上级目录
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print("项目根目录:", project_root, file=sys.stderr)

# 数据目录
data_dir = os.path.join(project_root, 'data')
print("数据目录:", data_dir, file=sys.stderr)
print("数据目录存在:", os.path.exists(data_dir), file=sys.stderr)

# 数据库文件
db_file = os.path.join(data_dir, 'football.db')
print("数据库文件:", db_file, file=sys.stderr)
print("数据库文件存在:", os.path.exists(db_file), file=sys.stderr)

# 检查顶层目录的数据库
top_data_dir = os.path.join(os.path.dirname(project_root), 'data')
print("\n顶层数据目录:", top_data_dir, file=sys.stderr)
print("顶层数据目录存在:", os.path.exists(top_data_dir), file=sys.stderr)

top_db_file = os.path.join(top_data_dir, 'football.db')
print("顶层数据库文件:", top_db_file, file=sys.stderr)
print("顶层数据库文件存在:", os.path.exists(top_db_file), file=sys.stderr)
print("=== 路径检查结束 ===", file=sys.stderr)

# 这里还是用标准输出打印
print("检查已完成，结果已输出到stderr")
time.sleep(5) 