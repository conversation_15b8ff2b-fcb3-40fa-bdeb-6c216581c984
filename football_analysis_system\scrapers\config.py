"""
配置模块 - 包含爬虫所需的常量、配置和默认值
"""
import os
import json
import logging
import sys

# 配置路径
try:
    # 尝试从football_analysis_system.config导入
    from football_analysis_system.config import DATA_DIR, DB_MATCHES
except ImportError:
    try:
        # 尝试从外部config导入
        from config import DATA_DIR, DB_MATCHES
    except ImportError:
        # 如果导入失败，使用默认值
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        DATA_DIR = os.path.join(project_root, 'data')
        DB_MATCHES = os.path.join(DATA_DIR, 'matches_and_odds.db')

# 确保data目录存在
os.makedirs(DATA_DIR, exist_ok=True)

# 数据库文件路径
DB_FILE = os.path.join(DATA_DIR, 'football.db')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("football_crawler.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("FootballCrawler")

# 爬虫默认配置
DEFAULT_CONFIG = {
    "max_workers": 8,  # 增加并发线程数
    "request_timeout": 30,
    "retry_times": 3,
    "min_delay": 0.5,  # 减少最小延迟
    "max_delay": 1.5,  # 减少最大延迟
    "batch_size": 50,
    "db_batch_size": 100
}

# 用户代理列表
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0"
]

# 请求头设置，模拟浏览器行为
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Referer': 'https://zq.titan007.com/cn/League/36.html',
    'Accept': '*/*',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive'
}

# 联赛URL配置
LEAGUES = json.loads(r'''
{
    "ENG1": {
        "name": "英超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s36.js"
    },
    "ENG2": {
        "name": "英冠",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s37_87.js"
    },
    "ENG3": {
        "name": "英甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s39_135.js"
    },
    "ITA1": {
        "name": "意甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s34_2948.js"
    },
    "ITA2": {
        "name": "意乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s40_261.js"
    },
    "ESP1": {
        "name": "西甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s31.js"
    },
    "ESP2": {
        "name": "西乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s33_546.js"
    },
    "GER1": {
        "name": "德甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s8.js"
    },
    "GER2": {
        "name": "德乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s9_132.js"
    },
    "FRA1": {
        "name": "法甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s11.js"
    },
    "FRA2": {
        "name": "法乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s12_1778.js"
    },
    "POR1": {
        "name": "葡超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s23_1123.js"
    },
    "POR2": {
        "name": "葡甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s157_1787.js"
    },
    "SCO1": {
        "name": "苏超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s29_2498.js"
    },
    "SCO2": {
        "name": "苏冠",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s150_1115.js"
    },
    "NED1": {
        "name": "荷甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s16_98.js"
    },
    "NED2": {
        "name": "荷乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s17_94.js"
    },
    "SWE1": {
        "name": "瑞典超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s26_431.js"
    },
    "SWE2": {
        "name": "瑞典甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s122_415.js"
    },
    "SWE3": {
        "name": "瑞典乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s218_1448.js"
    },
    "SWE4": {
        "name": "瑞典丙",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1436_1560.js"
    },
    "NOR1": {
        "name": "挪超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s22_3219.js"
    },
    "NOR2": {
        "name": "挪甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s123_437.js"
    },
    "DEN1": {
        "name": "丹麦超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025-2026/s7_1722.js"
    },
    "DEN2": {
        "name": "丹麦甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s127_2901.js"
    },
    "DEN3": {
        "name": "丹麦乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s1427_1581.js"
    },
    "DEN4": {
        "name": "丹麦丙",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s271_2907.js"
    },
    "SUI1": {
        "name": "瑞士超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s27_2507.js"
    },
    "SUI2": {
        "name": "瑞士甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s121_297.js"
    },
    "IRL1": {
        "name": "爱尔兰超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1_418.js"
    },
    "IRL2": {
        "name": "爱尔兰甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s139_419.js"
    },
    "RUS1": {
        "name": "俄超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s10_591.js"
    },
    "RUS2": {
        "name": "俄甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s235_674.js"
    },
    "BRA1": {
        "name": "巴西甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s4.js"
    },
    "BRA2": {
        "name": "巴西乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s358.js"
    },
    "ARG1": {
        "name": "阿根廷甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s2_1232.js"
    },
    "ARG2": {
        "name": "阿根廷乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s423_377.js"
    },
    "JPN1": {
        "name": "日职联",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s25_943.js"
    },
    "JPN2": {
        "name": "日职乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s284_808.js"
    },
    "JPN3": {
        "name": "日职丙",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1346_3213.js"
    },
    "JPNW1": {
        "name": "日职女甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s459_811.js"
    },
    "JPNW2": {
        "name": "日职女乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s455_684.js"
    },
    "KOR1": {
        "name": "韩K",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s15_313.js"
    },
    "KOR2": {
        "name": "韩K2",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1292_1212.js"
    },
    "CHN1": {
        "name": "中超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s60_2187.js"
    },
    "IND1": {
        "name": "印度超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s1367_1230.js"
    },
    "IND2": {
        "name": "印度甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s763_2399.js"
    },
    "AUS1": {
        "name": "澳超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s273_462.js"
    },
    "AUS_QLD": {
        "name": "澳昆超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1068_351.js"
    },
    "AUS_WA": {
        "name": "澳西超", 
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s481_558.js"
    },
    "AUS_SA": {
        "name": "澳南超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1029_483.js"
    },
    "AUS_BRI": {
        "name": "澳布超",
        "url": "https://zq.titan007.com/jsData/matchResult/2021/s774_331.js"
    },
    "AUS_NSW": {
        "name": "澳威超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s616_375.js"
    },
    "AUS_VIC2": {
        "name": "澳维甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1073_1667.js"
    },
    "AUS_VIC1": {
        "name": "澳维超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s436_386.js"
    },
    "CHI1": {
        "name": "智利甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s415_395.js"
    },
    "CHI2": {
        "name": "智利乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s611_896.js"
    },
    "TUR1": {
        "name": "土耳其超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s30_690.js"
    },
    "UZB1": {
        "name": "乌兹超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s772_1958.js"
    },
    "UZB2": {
        "name": "乌兹甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1160_3121.js"
    },
    "BLR1": {
        "name": "白俄超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s230_1007.js"
    },
    "BLR2": {
        "name": "白俄甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1046_616.js"
    },
    "UAE1": {
        "name": "阿联酋超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s301_1082.js"
    },
    "UAE2": {
        "name": "阿联酋甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s740_799.js"
    },
    "MEX1": {
        "name": "墨西哥甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s21_1951.js"
    },
    "USA1": {
        "name": "美职业",
        "english_name": "USA Major League Soccer",
        "short_name": "MLS",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s21_165.js",
        "description": "美国职业大联盟联赛，分为东西两个区，共30支球队参与竞争"
    },
    "USA2": {
        "name": "美甲",
        "english_name": "USL Championship", 
        "short_name": "USLC",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s2632_3241.js"
    },
    "SAU1": {
        "name": "沙特联",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s292_1079.js"
    },
    "RSA1": {
        "name": "南非超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s308_607.js"
    },
    "RSA2": {
        "name": "南非甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s907_563.js"
    },
    "MAR1": {
        "name": "摩洛超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s321.js"
    },
    "THA1": {
        "name": "泰超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s700_442.js"
    },
    "THA2": {
        "name": "泰甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s1048_2431.js"
    },
    "VIE1": {
        "name": "越南联",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s766_2096.js"
    },
    "VIE2": {
        "name": "越南甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s1248_1657.js"
    },
    "BHR1": {
        "name": "巴林超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s318_271.js"
    },
    "MYS1": {
        "name": "马来超", 
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s316_739.js"
    },
    "MYS2": {
        "name": "马来甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2022/s317_744.js"
    },
    "BHR2": {
        "name": "巴林甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s896_2216.js"
    },
    "BRA1": {
        "name": "巴西甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s4.js"
    },
    "BRA2": {
        "name": "巴西乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s358.js"
    },
    "PAR1": {
        "name": "巴拉甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s354_56.js"
    },
    "PAR2": {
        "name": "巴拉乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1076_568.js"
    },
    "PER1": {
        "name": "秘鲁甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s242_2045.js"
    },
    "ISL1": {
        "name": "冰岛超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s166_1192.js"
    },
    "ISL2": {
        "name": "冰岛甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s381_2993.js"
    },
    "LAT1": {
        "name": "拉脱超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s214_2866.js"
    },
    "LAT2": {
        "name": "拉脱甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1070_587.js"
    },
    "BLR1": {
        "name": "白俄超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s230_1007.js"
    },
    "BLR2": {
        "name": "白俄甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1046_616.js"
    },
    "LTU1": {
        "name": "立陶甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s217_1684.js"
    },
    "LTU2": {
        "name": "立陶乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s897_1012.js"
    },
    "TUR2": {
        "name": "土甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s130_264.js"
    },
    "TUR1": {
        "name": "土耳其超",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s30_690.js"
    },
    "HUN1": {
        "name": "匈甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s136.js"
    },
    "CRO1": {
        "name": "克亚甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s133_1131.js"
    },
    "SVN1": {
        "name": "斯亚甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s247_307.js"
    },
    "KAZ1": {
        "name": "哈萨克超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s466_400.js"
    },
    "KAZ2": {
        "name": "哈萨克甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s1077_1661.js"
    },
    "BIH1": {
        "name": "波斯甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s352_1730.js"
    },
    "EST1": {
        "name": "爱沙甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s353_2107.js"
    },
    "GEO1": {
        "name": "格鲁甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s563_536.js"
    },
    "AUS_VIC1": {
        "name": "澳维超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s436_386.js"
    },
    "SWE2": {
        "name": "瑞甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s103_1097.js"
    },
    "SCO3": {
        "name": "苏甲",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s89_1156.js"
    },
    "SCO4": {
        "name": "苏乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s225_1169.js"
    },
    "ENG4": {
        "name": "英乙",
        "url": "https://zq.titan007.com/jsData/matchResult/2024-2025/s8_1079.js"
    },
    "AUT_WSL": {
        "name": "奥威超",
        "url": "https://zq.titan007.com/jsData/matchResult/2025/s616_375.js"
    }
}
''')

# 目标博彩公司 - 要获取赔率的公司列表
TARGET_COMPANIES = {
    "115": {"en": "William Hill", "cn": "威廉希尔"},
    "90": {"en": "Easybets", "cn": "易胜博"},
    "432": {"en": "HKJC", "cn": "香港马会"},
    "80": {"en": "Macau", "cn": "澳门"},
    "255": {"en": "BWIN", "cn": "BWIN"},
    "81": {"en": "Weide", "cn": "伟德"},
    "474": {"en": "SB", "cn": "利记"},
    "4": {"en": "Nordicbet", "cn": "Nordicbet"},
    "976": {"en": "18BET", "cn": "18BET"},
    "937": {"en": "BetISn", "cn": "BetISn"},
    "657": {"en": "iddaa", "cn": "iddaa"}
}