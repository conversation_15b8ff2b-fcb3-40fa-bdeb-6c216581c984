"""
数据库模块 - 包含数据库创建、查询和数据操作相关功能
"""
import sqlite3
import logging
import os
import traceback
from datetime import datetime

from scrapers.config import DB_FILE, TARGET_COMPANIES, logger

class DatabaseManager:
    """数据库管理类，处理数据库操作"""
    
    @staticmethod
    def create_database():
        """创建SQLite数据库和表结构 - 支持多联赛数据和增量更新"""
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        try:
            # 禁用外键约束，以避免插入错误
            cursor.execute("PRAGMA foreign_keys = OFF;")
            
            # 检查数据库中的所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            logger.info(f"数据库中的表: {tables}")
            
            # 检查tables表是否存在matches表
            if "matches" not in tables:
                logger.info("创建matches表...")
                # 创建比赛表
                cursor.execute('''
                CREATE TABLE matches (
                    match_id INTEGER,                    /* 比赛ID */
                    league_id INTEGER NOT NULL,          /* 联赛ID */
                    league_name TEXT NOT NULL,           /* 联赛中文名称 */
                    round_number INTEGER NOT NULL,       /* 轮次 */
                    match_status INTEGER NOT NULL,       /* 比赛状态：-1=已完成比赛，0=未开始，1=上半场，2=中场，3=下半场，4=完场，5=取消，6=延期，7=腰斩 */
                    match_time TEXT NOT NULL,            /* 比赛时间 */
                    home_team_id INTEGER NOT NULL,       /* 主队ID */
                    home_team_name TEXT NOT NULL,        /* 主队名称 */
                    away_team_id INTEGER NOT NULL,       /* 客队ID */
                    away_team_name TEXT NOT NULL,        /* 客队名称 */
                    home_score INTEGER,                  /* 主队得分 */
                    away_score INTEGER,                  /* 客队得分 */
                    home_half_score INTEGER,             /* 主队半场得分 */
                    away_half_score INTEGER,             /* 客队半场得分 */
                    home_rank TEXT,                      /* 主队排名 */
                    away_rank TEXT,                      /* 客队排名 */
                    handicap REAL,                       /* 让球盘口 */
                    over_under TEXT,                     /* 大小球盘口 */
                    is_included_in_stat INTEGER DEFAULT 1, /* 是否计入统计：1=是，0=否 */
                    matchid TEXT,                        /* 比赛ID(字符串格式) */
                    odds_updated INTEGER DEFAULT 0,      /* 是否已更新赔率：0=未更新，1=已更新 */
                    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, /* 更新时间 */
                    PRIMARY KEY (league_id, match_id)    /* 联赛ID和比赛ID共同组成主键 */
                )
                ''')
                
                # 创建matches表索引
                cursor.execute('CREATE INDEX idx_matches_date ON matches(match_time)')
                cursor.execute('CREATE INDEX idx_matches_teams ON matches(home_team_id, away_team_id)')
                cursor.execute('CREATE INDEX idx_matches_matchid ON matches(matchid)')
                cursor.execute('CREATE INDEX idx_matches_league ON matches(league_id)')
                cursor.execute('CREATE INDEX idx_matches_league_name ON matches(league_name)')
                cursor.execute('CREATE INDEX idx_matches_odds_updated ON matches(odds_updated)')
                cursor.execute('CREATE INDEX idx_matches_match_status ON matches(match_status)')
            else:
                # 如果matches表已存在，检查必要的字段
                cursor.execute("PRAGMA table_info(matches)")
                columns = cursor.fetchall()
                column_names = [column[1] for column in columns]
                
                # 检查是否有odds_updated字段
                if 'odds_updated' not in column_names:
                    logger.info("向matches表添加odds_updated字段...")
                    cursor.execute("ALTER TABLE matches ADD COLUMN odds_updated INTEGER DEFAULT 0")
                    cursor.execute('CREATE INDEX IF NOT EXISTS idx_matches_odds_updated ON matches(odds_updated)')
                
                # 检查是否有update_time字段  
                if 'update_time' not in column_names:
                    logger.info("向matches表添加update_time字段...")
                    # 使用静态时间戳，因为SQLite不允许在ALTER TABLE中使用CURRENT_TIMESTAMP作为默认值
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    # 使用参数化查询防止SQL注入
                    cursor.execute("ALTER TABLE matches ADD COLUMN update_time TEXT DEFAULT ?", (current_time,))
                    # 更新所有记录的update_time
                    cursor.execute("UPDATE matches SET update_time = datetime('now') WHERE update_time = ?", (current_time,))
            
            # 检查是否存在standings表
            if "standings" not in tables:
                logger.info("创建standings表...")
                # 创建积分榜表
                cursor.execute('''
                CREATE TABLE standings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,  /* 自增ID */
                    league_id INTEGER NOT NULL,            /* 联赛ID */
                    league_name TEXT NOT NULL,             /* 联赛中文名称 */
                    team_id INTEGER NOT NULL,              /* 球队ID */
                    team_name TEXT NOT NULL,               /* 球队名称 */
                    standing_type TEXT NOT NULL,           /* 积分类型：total=总积分, home=主场积分, away=客场积分 */
                    rank_no INTEGER NOT NULL,              /* 排名 */
                    matches_played INTEGER NOT NULL,       /* 已赛场次 */
                    wins INTEGER NOT NULL,                 /* 胜场数 */
                    draws INTEGER NOT NULL,                /* 平场数 */
                    losses INTEGER NOT NULL,               /* 负场数 */
                    goals_for INTEGER NOT NULL,            /* 进球数 */
                    goals_against INTEGER NOT NULL,        /* 失球数 */
                    goal_difference INTEGER NOT NULL,      /* 净胜球 */
                    win_rate REAL,                        /* 胜率（百分比） */
                    draw_rate REAL,                       /* 平率（百分比） */
                    loss_rate REAL,                       /* 负率（百分比） */
                    goals_for_avg REAL,                   /* 场均进球 */
                    goals_against_avg REAL,               /* 场均失球 */
                    points INTEGER NOT NULL,               /* 积分 */
                    update_time TEXT NOT NULL,            /* 更新时间 */
                    UNIQUE (league_id, team_id, standing_type)  /* 确保每支球队在每个联赛的每个积分类型中只有一条记录 */
                )
                ''')
                
                # 创建standings表索引
                cursor.execute('CREATE INDEX idx_standings_team ON standings(team_id)')
                cursor.execute('CREATE INDEX idx_standings_type ON standings(standing_type)')
                cursor.execute('CREATE INDEX idx_standings_league ON standings(league_id)')
                cursor.execute('CREATE INDEX idx_standings_league_name ON standings(league_name)')
            
            # 创建赔率公司表（如果不存在）
            if "odds_companies" not in tables:
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS odds_companies (
                    company_id INTEGER PRIMARY KEY,
                    company_name TEXT,
                    company_name_cn TEXT
                )
                ''')
            
            # 创建欧赔表（如果不存在）
            if "euro_odds" not in tables:
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS euro_odds (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    match_id INTEGER,
                    matchid TEXT,
                    company_id INTEGER,
                    company_odds_id TEXT,
                    init_home_win REAL,
                    init_draw REAL,
                    init_away_win REAL,
                    init_home_win_rate REAL,
                    init_draw_rate REAL,
                    init_away_win_rate REAL,
                    init_return_rate REAL,
                    real_home_win REAL,
                    real_draw REAL,
                    real_away_win REAL,
                    real_home_win_rate REAL,
                    real_draw_rate REAL,
                    real_away_win_rate REAL,
                    real_return_rate REAL,
                    k_home REAL,
                    k_draw REAL,
                    k_away REAL,
                    update_time TEXT,
                    crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (match_id) REFERENCES matches(match_id)
                )
                ''')
                
                # 创建euro_odds表索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_euro_odds_match_id ON euro_odds(match_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_euro_odds_company_id ON euro_odds(company_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_euro_odds_matchid ON euro_odds(matchid)')
            
            # 创建爬取状态表（如果不存在）
            if "crawl_status" not in tables:
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    matchid TEXT UNIQUE,
                    status TEXT,
                    retry_count INTEGER DEFAULT 0,
                    error_message TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                ''')
            
            # 插入博彩公司信息
            for company_id, company_info in TARGET_COMPANIES.items():
                cursor.execute('''
                INSERT OR IGNORE INTO odds_companies (company_id, company_name, company_name_cn)
                VALUES (?, ?, ?)
                ''', (company_id, company_info["en"], company_info["cn"]))
                
            conn.commit()
            logger.info("数据库结构检查/创建完成")
            return conn
        
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            logger.error(f"创建数据库时出错: {e}")
            raise
    
    @staticmethod
    def insert_match_data(conn, parsed_data):
        """将解析后的比赛数据插入数据库 - 支持增量更新"""
        cursor = conn.cursor()
        
        # 先检查matches表是否有update_time列
        cursor.execute("PRAGMA table_info(matches)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        has_update_time = 'update_time' in column_names
        
        try:
            # 开始事务，确保外键约束关闭
            conn.execute("BEGIN TRANSACTION;")
            conn.execute("PRAGMA foreign_keys = OFF;")
            
            # 提取联赛ID信息
            league_id = parsed_data['league'][0]
            
            # 获取联赛名称
            league_name = parsed_data.get('league_name', '')  # 从解析数据中获取
            if not league_name and 'league_code' in parsed_data:
                # 如果解析数据中没有，从LEAGUES配置中获取
                from scrapers.config import LEAGUES
                league_code = parsed_data['league_code']
                league_name = LEAGUES.get(league_code, {}).get('name', f'联赛{league_id}')
            
            logger.info(f"准备插入联赛数据: ID={league_id}, 名称={league_name}")
            
            # 创建球队ID到名称的映射
            team_id_to_name = {}
            for team in parsed_data['teams']:
                team_id_to_name[team[0]] = team[1]  # 使用中文名称
            
            # 获取当前时间
            current_date = datetime.now()
            logger.info(f"当前时间: {current_date}, 仅处理此时间之前的比赛")
            
            # 获取数据库中该联赛已有的比赛IDs
            cursor.execute("SELECT match_id, match_status FROM matches WHERE league_id = ?", (league_id,))
            existing_matches = {row[0]: row[1] for row in cursor.fetchall()}
            
            # 插入/更新比赛数据
            match_count = 0
            update_count = 0
            insert_count = 0
            filtered_count = 0
            
            # 检查odds_updated列是否存在
            has_odds_updated = 'odds_updated' in column_names
            
            for round_num, matches in parsed_data['matches'].items():
                for match in matches:
                    try:
                        # 获取比赛ID
                        match_id = match[0]
                        
                        # 检查比赛日期是否已过
                        match_date_str = match[3]
                        try:
                            match_date = datetime.strptime(match_date_str, '%Y-%m-%d %H:%M')
                            
                            # 如果比赛日期还未到，则跳过
                            if match_date > current_date:
                                filtered_count += 1
                                continue
                        except ValueError:
                            logger.warning(f"警告: 无法解析比赛日期: {match_date_str}, 仍保留该比赛")
                        
                        # 获取主队和客队名称
                        home_team_id = match[4]
                        away_team_id = match[5]
                        home_team_name = team_id_to_name.get(home_team_id, f"球队{home_team_id}")
                        away_team_name = team_id_to_name.get(away_team_id, f"球队{away_team_id}")
                        
                        # 解析比分
                        home_score, away_score = None, None
                        if match[6] and match[6] != '' and match[6] != 'null':
                            scores = match[6].split('-')
                            if len(scores) == 2:
                                home_score, away_score = int(scores[0]), int(scores[1])
                        
                        # 解析半场比分
                        home_half_score, away_half_score = None, None
                        if match[7] and match[7] != '' and match[7] != 'null':
                            half_scores = match[7].split('-')
                            if len(half_scores) == 2:
                                home_half_score, away_half_score = int(half_scores[0]), int(half_scores[1])
                        
                        # 处理让球盘口，可能为空
                        handicap = 0.0
                        if match[10] is not None and match[10] != '' and match[10] != 'null':
                            handicap = float(match[10])
                        
                        # 获取是否包含统计
                        is_included_in_stat = 1
                        if len(match) > 14 and match[14] is not None:
                            is_included_in_stat = int(match[14])
                        
                        # 设置比赛状态 - 对于已结束且有比分的比赛设置为-1
                        match_status = match[2]
                        if match_status == 4 and home_score is not None and away_score is not None:
                            match_status = -1  # 表示已完成的比赛
                        
                        # 原始匹配ID (match_id) 作为字符串保存到 matchid 字段
                        matchid = str(match[0])
                        
                        # 检查该比赛是否需要更新
                        need_update = True
                        
                        # 如果比赛已存在且状态为-1（已完成），只有在新状态不同时才更新
                        if match_id in existing_matches:
                            existing_status = existing_matches[match_id]
                            # 如果两者都是已完成状态，且数据库中已有，则不更新
                            if existing_status == -1 and match_status == -1:
                                # 但是，如果有赔率相关的字段变化，仍需更新
                                cursor.execute("""
                                SELECT home_score, away_score, handicap, over_under FROM matches
                                WHERE match_id = ? AND league_id = ?
                                """, (match_id, league_id))
                                existing_data = cursor.fetchone()
                                
                                if existing_data:
                                    existing_home_score, existing_away_score, existing_handicap, existing_over_under = existing_data
                                    
                                    # 只有在这些关键字段有变化时才更新
                                    if (existing_home_score == home_score and 
                                        existing_away_score == away_score and
                                        abs(existing_handicap - handicap) < 0.01 and
                                        existing_over_under == (str(match[12]) if match[12] else '')):
                                        need_update = False
                        
                        if need_update:
                            # 获取当前时间
                            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            
                            # 准备基本的插入数据
                            base_data = [
                                match_id, league_id, league_name, round_num, match_status, match[3],
                                home_team_id, home_team_name, away_team_id, away_team_name,
                                home_score, away_score, home_half_score, away_half_score,
                                str(match[8]), str(match[9]), handicap, 
                                str(match[12]) if match[12] else '', is_included_in_stat, matchid
                            ]
                            
                            # 构建SQL语句，根据表结构动态调整
                            sql_columns = """
                            match_id, league_id, league_name, round_number, match_status, match_time,
                            home_team_id, home_team_name, away_team_id, away_team_name,
                            home_score, away_score, home_half_score, away_half_score,
                            home_rank, away_rank, handicap, over_under, is_included_in_stat, matchid
                            """
                            
                            # 增加额外的列
                            extra_columns = []
                            extra_data = []
                            
                            if has_odds_updated:
                                extra_columns.append("odds_updated")
                                extra_data.append(0)  # 默认为未更新状态
                            
                            if has_update_time:
                                extra_columns.append("update_time")
                                extra_data.append(current_time)
                            
                            # 构建最终的SQL语句
                            final_columns = sql_columns
                            if extra_columns:
                                final_columns += ", " + ", ".join(extra_columns)
                            
                            placeholders = ", ".join(["?"] * (len(base_data) + len(extra_data)))
                            
                            sql = f"INSERT OR REPLACE INTO matches ({final_columns}) VALUES ({placeholders})"
                            
                            # 执行SQL
                            cursor.execute(sql, base_data + extra_data)
                            
                            # 统计插入和更新数量
                            if match_id in existing_matches:
                                update_count += 1
                            else:
                                insert_count += 1
                            
                            match_count += 1
                    
                    except Exception as e:
                        logger.error(f"插入比赛数据时出错: {e}, 轮次: {round_num}, 比赛ID: {match[0]}")
                        logger.debug(f"比赛数据: {match}")
            
            logger.info(f"比赛数据处理成功: 新增{insert_count}场，更新{update_count}场，过滤{filtered_count}场未来比赛")
            
            # 插入积分榜数据 - 将三种类型的积分榜数据全部导入
            standing_types_mapping = {
                'totalScore': 'total',    # 总积分
                'homeScore': 'home',      # 主场积分
                'guestScore': 'away'      # 客场积分
            }
            
            update_time = parsed_data.get('lastUpdateTime', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            standings_count = 0
            for score_type, type_name in standing_types_mapping.items():
                if score_type in parsed_data['standings']:
                    logger.debug(f"处理 {score_type} ({type_name}) 积分榜数据...")
                    
                    for team_standing in parsed_data['standings'][score_type]:
                        try:
                            # 不同类型的积分榜数据结构可能不同，需要进行适配
                            if score_type == 'totalScore':
                                # totalScore: [0, 1, 25, 2, 29, 21, 7, 1, 69, 27, 42, "72.4", "24.1", "3.4", 2.38, 0.93, 70, 0, "", 1, 0, 1, 0, 0, 0, ""]
                                team_id = team_standing[2]
                                rank_no = int(team_standing[1])
                                matches_played = int(team_standing[4])
                                wins = int(team_standing[5])
                                draws = int(team_standing[6])
                                losses = int(team_standing[7])
                                goals_for = int(team_standing[8])
                                goals_against = int(team_standing[9])
                                goal_difference = int(team_standing[10])
                                win_rate = float(team_standing[11].replace("'", "")) if team_standing[11] else 0.0
                                draw_rate = float(team_standing[12].replace("'", "")) if team_standing[12] else 0.0
                                loss_rate = float(team_standing[13].replace("'", "")) if team_standing[13] else 0.0
                                goals_for_avg = float(team_standing[14]) if team_standing[14] is not None else 0.0
                                goals_against_avg = float(team_standing[15]) if team_standing[15] is not None else 0.0
                                points = int(team_standing[16]) if team_standing[16] is not None else 0
                            
                            elif score_type == 'homeScore':
                                # homeScore: [1, 25, 14, 11, 2, 1, 31, 11, 20, "78.6", "14.3", "7.1", 2.21, 0.79, 35]
                                team_id = team_standing[1]
                                rank_no = int(team_standing[0])
                                matches_played = int(team_standing[2])
                                wins = int(team_standing[3])
                                draws = int(team_standing[4])
                                losses = int(team_standing[5])
                                goals_for = int(team_standing[6])
                                goals_against = int(team_standing[7])
                                goal_difference = int(team_standing[8])
                                win_rate = float(team_standing[9].replace("'", "")) if team_standing[9] else 0.0
                                draw_rate = float(team_standing[10].replace("'", "")) if team_standing[10] else 0.0
                                loss_rate = float(team_standing[11].replace("'", "")) if team_standing[11] else 0.0
                                goals_for_avg = float(team_standing[12]) if team_standing[12] is not None else 0.0
                                goals_against_avg = float(team_standing[13]) if team_standing[13] is not None else 0.0
                                points = int(team_standing[14]) if team_standing[14] is not None else 0
                            
                            elif score_type == 'guestScore':
                                # guestScore: [1, 25, 15, 10, 5, 0, 38, 16, 22, "66.7", "33.3", "0.0", 2.53, 1.07, 35]
                                team_id = team_standing[1]
                                rank_no = int(team_standing[0])
                                matches_played = int(team_standing[2])
                                wins = int(team_standing[3])
                                draws = int(team_standing[4])
                                losses = int(team_standing[5])
                                goals_for = int(team_standing[6])
                                goals_against = int(team_standing[7])
                                goal_difference = int(team_standing[8])
                                win_rate = float(team_standing[9].replace("'", "")) if team_standing[9] else 0.0
                                draw_rate = float(team_standing[10].replace("'", "")) if team_standing[10] else 0.0
                                loss_rate = float(team_standing[11].replace("'", "")) if team_standing[11] else 0.0
                                goals_for_avg = float(team_standing[12]) if team_standing[12] is not None else 0.0
                                goals_against_avg = float(team_standing[13]) if team_standing[13] is not None else 0.0
                                points = int(team_standing[14]) if team_standing[14] is not None else 0
                            
                            else:
                                # 其他类型暂不处理
                                continue
                            
                            # 获取球队名称
                            team_name = team_id_to_name.get(team_id, f"球队{team_id}")
                            
                            # 插入数据
                            cursor.execute('''
                            INSERT OR REPLACE INTO standings (
                                league_id, league_name, team_id, team_name, standing_type, rank_no, matches_played,
                                wins, draws, losses, goals_for, goals_against,
                                goal_difference, win_rate, draw_rate, loss_rate,
                                goals_for_avg, goals_against_avg, points, update_time
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                league_id, league_name, team_id, team_name, type_name, rank_no, matches_played,
                                wins, draws, losses, goals_for, goals_against,
                                goal_difference, win_rate, draw_rate, loss_rate,
                                goals_for_avg, goals_against_avg, points, update_time
                            ))
                            standings_count += 1
                        
                        except Exception as e:
                            logger.error(f"插入积分榜数据时出错: {e}, 类型: {score_type}")
                            logger.debug(f"积分榜数据: {team_standing}")
                            traceback.print_exc()
            
            logger.info(f"积分榜数据插入成功，共{standings_count}条记录")
            
            # 执行显式提交
            conn.commit()
            logger.info(f"提交事务成功: 联赛 {league_id}({league_name}) 数据全部成功导入数据库！")
            
        except Exception as e:
            conn.rollback()
            logger.error(f"数据插入失败: {e}")
            traceback.print_exc()
    
    @staticmethod
    def batch_insert_odds(db_conn, odds_data):
        """批量插入赔率数据
        
        Args:
            db_conn: 数据库连接
            odds_data: 赔率数据列表
            
        Returns:
            tuple: (新插入数量, 更新数量)
        """
        if not odds_data:
            return (0, 0)
        
        cursor = db_conn.cursor()
        
        try:
            inserted = 0
            updated = 0
            for odds in odds_data:
                try:
                    # 先检查是否已存在相同赔率数据
                    cursor.execute("""
                    SELECT id FROM euro_odds 
                    WHERE match_id = ? AND company_id = ? AND company_odds_id = ?
                    """, (odds['match_id'], odds['company_id'], odds['company_odds_id']))
                    existing = cursor.fetchone()
                    
                    if existing:
                        # 更新已存在的数据
                        cursor.execute('''
                        UPDATE euro_odds SET
                            init_home_win = ?, init_draw = ?, init_away_win = ?,
                            init_home_win_rate = ?, init_draw_rate = ?, init_away_win_rate = ?, init_return_rate = ?,
                            real_home_win = ?, real_draw = ?, real_away_win = ?,
                            real_home_win_rate = ?, real_draw_rate = ?, real_away_win_rate = ?, real_return_rate = ?,
                            k_home = ?, k_draw = ?, k_away = ?, update_time = ?,
                            crawl_time = CURRENT_TIMESTAMP
                        WHERE id = ?
                        ''', (
                            odds['init_home_win'], odds['init_draw'], odds['init_away_win'],
                            odds['init_home_win_rate'], odds['init_draw_rate'], 
                            odds['init_away_win_rate'], odds['init_return_rate'],
                            odds['real_home_win'], odds['real_draw'], odds['real_away_win'],
                            odds['real_home_win_rate'], odds['real_draw_rate'], 
                            odds['real_away_win_rate'], odds['real_return_rate'],
                            odds['k_home'], odds['k_draw'], odds['k_away'],
                            odds['update_time'],
                            existing[0]
                        ))
                        updated += 1
                    else:
                        # 插入新数据
                        cursor.execute('''
                        INSERT INTO euro_odds (
                            match_id, matchid, company_id, company_odds_id,
                            init_home_win, init_draw, init_away_win,
                            init_home_win_rate, init_draw_rate, init_away_win_rate, init_return_rate,
                            real_home_win, real_draw, real_away_win,
                            real_home_win_rate, real_draw_rate, real_away_win_rate, real_return_rate,
                            k_home, k_draw, k_away, update_time
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            odds['match_id'], odds['matchid'], odds['company_id'], odds['company_odds_id'],
                            odds['init_home_win'], odds['init_draw'], odds['init_away_win'],
                            odds['init_home_win_rate'], odds['init_draw_rate'], 
                            odds['init_away_win_rate'], odds['init_return_rate'],
                            odds['real_home_win'], odds['real_draw'], odds['real_away_win'],
                            odds['real_home_win_rate'], odds['real_draw_rate'], 
                            odds['real_away_win_rate'], odds['real_return_rate'],
                            odds['k_home'], odds['k_draw'], odds['k_away'],
                            odds['update_time']
                        ))
                        inserted += 1
                except Exception as e:
                    logger.error(f"插入赔率数据失败: {e}")
                    continue
            
            db_conn.commit()
            logger.debug(f"批量处理赔率数据: 插入了 {inserted} 条新数据，更新了 {updated} 条已有数据")
            return (inserted, updated)
        except Exception as e:
            db_conn.rollback()
            logger.error(f"批量插入赔率数据失败: {e}")
            return (0, 0)
    
    @staticmethod
    def get_matches_needing_odds(conn, force_all=False):
        """获取需要爬取赔率的比赛
        
        Args:
            conn: 数据库连接
            force_all: 是否强制爬取所有比赛的赔率，包括已爬取过的
            
        Returns:
            list: 需要爬取赔率的比赛列表
        """
        cursor = conn.cursor()
        
        try:
            # 基本查询：获取已完成的比赛
            base_query = """
            SELECT match_id, matchid 
            FROM matches 
            WHERE match_status = -1
              AND match_time < datetime('now')
              AND (home_score IS NOT NULL AND away_score IS NOT NULL)
            """
            
            # 如果不是强制爬取所有比赛，则只获取未爬取过赔率的比赛
            if not force_all:
                base_query += " AND (odds_updated = 0 OR odds_updated IS NULL)"
                
            base_query += " ORDER BY match_time DESC"
            
            cursor.execute(base_query)
            
            matches = cursor.fetchall()
            match_count = len(matches)
            
            logger.info(f"找到 {match_count} 场需要爬取赔率的比赛" + (" (强制爬取所有比赛)" if force_all else ""))
            return [{"match_id": row[0], "matchid": row[1]} for row in matches]
        except Exception as e:
            logger.error(f"获取需要爬取赔率的比赛时出错: {e}")
            return []
    
    @staticmethod
    def get_league_last_update(conn, league_id):
        """获取联赛最后更新时间"""
        cursor = conn.cursor()
        
        # 先检查matches表是否有update_time列
        cursor.execute("PRAGMA table_info(matches)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'update_time' in column_names:
            # 如果有update_time列，使用它来获取最后更新时间
            cursor.execute("""
            SELECT MAX(update_time) FROM matches 
            WHERE league_id = ?
            """, (league_id,))
        else:
            # 如果没有update_time列，使用最大match_time作为替代
            cursor.execute("""
            SELECT MAX(match_time) FROM matches 
            WHERE league_id = ?
            """, (league_id,))
        
        result = cursor.fetchone()
        return result[0] if result and result[0] else None