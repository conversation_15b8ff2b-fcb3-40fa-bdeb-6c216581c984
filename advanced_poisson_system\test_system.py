"""
高级泊松系统测试脚本
"""

import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from data_manager import DataManager
from market_analyzer import MarketAnalyzer

def test_data_manager():
    """测试数据管理器"""
    print("=== 测试数据管理器 ===")
    
    dm = DataManager()
    
    # 测试比赛ID 2702969
    match_id = 2702969
    
    # 测试比赛信息获取
    match_info = dm.get_match_info(match_id)
    print(f"比赛信息: {match_info}")
    
    if not match_info:
        print("❌ 无法获取比赛信息")
        return False
    
    # 测试大小球赔率获取
    overunder_data = dm.get_overunder_odds(match_id)
    print(f"大小球赔率数量: {len(overunder_data)}")
    if overunder_data:
        print(f"示例赔率: {overunder_data[0]}")
    
    # 测试胜平负赔率获取
    odds_1x2_data = dm.get_1x2_odds(match_id)
    print(f"胜平负赔率数量: {len(odds_1x2_data)}")
    if odds_1x2_data:
        print(f"示例赔率: {odds_1x2_data[0]}")
    
    # 测试数据可用性评估
    assessment = dm.assess_data_availability(
        match_id, 
        match_info.get('home_team_name', '主队'), 
        match_info.get('away_team_name', '客队')
    )
    print(f"数据可用性评估: {assessment}")
    
    # 测试博彩公司列表
    bookmakers = dm.get_available_bookmakers(match_id)
    print(f"可用博彩公司: {bookmakers}")
    
    return True

def test_market_analyzer():
    """测试市场分析器"""
    print("\n=== 测试市场分析器 ===")
    
    analyzer = MarketAnalyzer()
    
    # 先获取实际数据
    dm = DataManager()
    match_id = 2702969
    
    overunder_data = dm.get_overunder_odds(match_id)
    odds_1x2_data = dm.get_1x2_odds(match_id)
    
    if not overunder_data:
        print("❌ 无大小球赔率数据，使用模拟数据")
        overunder_data = [
            {
                'company_name': '澳门',
                'over_odds': 1.95,
                'under_odds': 1.95,
                'line_numeric': 2.5
            }
        ]
    
    if not odds_1x2_data:
        print("❌ 无胜平负赔率数据，使用模拟数据")
        odds_1x2_data = [
            {
                'company_name': '澳门',
                'home_win_odds': 2.20,
                'draw_odds': 3.30,
                'away_win_odds': 3.50
            }
        ]
    
    # 测试大小球分析
    ou_result = analyzer.analyze_overunder_market(overunder_data)
    print(f"大小球分析成功: {ou_result.get('success', False)}")
    if ou_result.get('success'):
        print(f"  - 总λ估算: {ou_result['lambda_total']:.3f}")
        print(f"  - 庄家: {ou_result['selected_bookmaker']}")
        print(f"  - 公平概率: {ou_result['fair_probabilities']}")
    
    # 测试胜平负分析
    x12_result = analyzer.analyze_1x2_market(odds_1x2_data)
    print(f"胜平负分析成功: {x12_result.get('success', False)}")
    if x12_result.get('success'):
        print(f"  - 庄家: {x12_result['selected_bookmaker']}")
        print(f"  - 公平概率: {x12_result['fair_probabilities']}")
    
    # 测试综合分析
    comprehensive_result = analyzer.comprehensive_market_analysis(overunder_data, odds_1x2_data)
    print(f"综合分析结果: {comprehensive_result['lambda_estimates']}")
    print(f"拟合质量: {comprehensive_result['model_validation']['fit_quality']:.3f}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 高级泊松系统测试开始")
    
    success = True
    
    try:
        success &= test_data_manager()
        success &= test_market_analyzer()
        
        if success:
            print("\n✅ 所有测试通过！")
        else:
            print("\n❌ 部分测试失败")
            
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 