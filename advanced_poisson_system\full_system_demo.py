"""
高级泊松系统完整演示脚本
展示系统的所有核心功能和分析能力
"""

import sys
import os
from datetime import datetime

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from data_manager import DataManager
from market_analyzer import MarketAnalyzer
from historical_analyzer import HistoricalAnalyzer
from hybrid_predictor import HybridPredictor
from advanced_poisson_engine import AdvancedPoissonEngine

def print_section_header(title):
    """打印章节标题"""
    print("\n" + "="*60)
    print(f"🔬 {title}")
    print("="*60)

def print_subsection(title):
    """打印小节标题"""
    print(f"\n--- {title} ---")

def demo_data_access():
    """演示数据访问功能"""
    print_section_header("数据访问层演示")
    
    dm = DataManager()
    match_id = 2702969
    
    # 数据可用性评估
    print_subsection("数据可用性评估")
    match_info = dm.get_match_info(match_id)
    if match_info:
        print(f"✅ 比赛信息: {match_info['home_team_name']} vs {match_info['away_team_name']}")
        
        assessment = dm.assess_data_availability(
            match_id, 
            match_info['home_team_name'], 
            match_info['away_team_name']
        )
        print(f"📊 数据质量评分: {assessment['data_quality_percentage']:.1f}%")
        print(f"📈 大小球赔率: {'✅' if assessment['overunder_odds'] else '❌'}")
        print(f"🎯 胜平负赔率: {'✅' if assessment['1x2_odds'] else '❌'}")
        print(f"📚 历史数据: {'✅' if assessment['historical_data'] else '❌'}")
        
        # 博彩公司统计
        bookmakers = dm.get_available_bookmakers(match_id)
        print(f"🏢 可用博彩公司: {bookmakers['total_unique_companies']} 家")
        print(f"   - 大小球: {len(bookmakers['overunder_companies'])} 家")
        print(f"   - 胜平负: {len(bookmakers['1x2_companies'])} 家")
        print(f"   - 共同: {len(bookmakers['common_companies'])} 家")

def demo_market_analysis():
    """演示市场分析功能"""
    print_section_header("市场分析层演示")
    
    dm = DataManager()
    ma = MarketAnalyzer()
    match_id = 2702969
    
    # 获取赔率数据
    overunder_data = dm.get_overunder_odds(match_id, 2.5)
    odds_1x2_data = dm.get_1x2_odds(match_id)
    
    print_subsection("赔率数据展示")
    print(f"📊 大小球2.5赔率数量: {len(overunder_data)}")
    if overunder_data:
        sample = overunder_data[0]
        print(f"   示例: {sample['company_name']} - 大球:{sample['over_odds']} 小球:{sample['under_odds']}")
    
    print(f"🎯 胜平负赔率数量: {len(odds_1x2_data)}")
    if odds_1x2_data:
        sample = odds_1x2_data[0]
        print(f"   示例: {sample['company_name']} - 主:{sample['home_win_odds']} 平:{sample['draw_odds']} 客:{sample['away_win_odds']}")
    
    # 市场综合分析
    print_subsection("市场分析结果")
    market_result = ma.comprehensive_market_analysis(overunder_data, odds_1x2_data, 2.5)
    
    lambda_estimates = market_result['lambda_estimates']
    print(f"🎯 市场λ估算:")
    print(f"   - 总进球期望: {lambda_estimates['total']:.3f}")
    print(f"   - 主队期望: {lambda_estimates['home']:.3f}")
    print(f"   - 客队期望: {lambda_estimates['away']:.3f}")
    
    model_validation = market_result['model_validation']
    print(f"📈 模型验证:")
    print(f"   - 拟合质量: {model_validation['fit_quality']:.3f}")
    print(f"   - 大小球可用: {'✅' if model_validation['data_quality']['overunder_available'] else '❌'}")
    print(f"   - 胜平负可用: {'✅' if model_validation['data_quality']['1x2_available'] else '❌'}")

def demo_historical_analysis():
    """演示历史分析功能"""
    print_section_header("历史分析层演示")
    
    dm = DataManager()
    ha = HistoricalAnalyzer(dm)
    
    home_team = "哥德堡盖斯"
    away_team = "天狼星"
    
    print_subsection("历史数据分析")
    historical_result = ha.calculate_historical_lambdas(home_team, away_team)
    
    if historical_result['success']:
        lambda_estimates = historical_result['lambda_estimates']
        print(f"🎯 历史λ估算:")
        print(f"   - 总进球期望: {lambda_estimates['total']:.3f}")
        print(f"   - 主队期望: {lambda_estimates['home']:.3f}")
        print(f"   - 客队期望: {lambda_estimates['away']:.3f}")
        
        print(f"📊 数据质量:")
        data_quality = historical_result['data_quality']
        print(f"   - 整体评分: {data_quality['overall_score']:.3f}")
        print(f"   - 置信度: {historical_result['confidence_level']}")
        print(f"   - 历史比赛数: {data_quality['total_matches']}")
        
        # 球队分析
        print_subsection("球队强度分析")
        home_analysis = historical_result['team_analysis']['home_team']
        away_analysis = historical_result['team_analysis']['away_team']
        
        print(f"🏠 {home_team}:")
        print(f"   - 攻击强度: {home_analysis['attack_strength']:.3f}")
        print(f"   - 防守强度: {home_analysis['defense_strength']:.3f}")
        print(f"   - 状态评估: {home_analysis['form_assessment']}")
        
        print(f"🚌 {away_team}:")
        print(f"   - 攻击强度: {away_analysis['attack_strength']:.3f}")
        print(f"   - 防守强度: {away_analysis['defense_strength']:.3f}")
        print(f"   - 状态评估: {away_analysis['form_assessment']}")

def demo_hybrid_prediction():
    """演示融合预测功能"""
    print_section_header("融合预测层演示")
    
    dm = DataManager()
    ma = MarketAnalyzer()
    ha = HistoricalAnalyzer(dm)
    hp = HybridPredictor(ha, ma)
    
    match_id = 2702969
    home_team = "哥德堡盖斯"
    away_team = "天狼星"
    
    print_subsection("多策略融合预测")
    
    strategies = ['conservative', 'balanced', 'aggressive', 'auto']
    
    for strategy in strategies:
        result = hp.predict_match_lambdas(match_id, home_team, away_team, strategy=strategy)
        if result['success']:
            lambdas = result['lambda_estimates']
            weights = result['fusion_weights']
            assessment = result['comprehensive_assessment']
            
            print(f"\n🎯 {strategy.upper()} 策略:")
            print(f"   λ总计: {lambdas['total']:.3f} (主:{lambdas['home']:.3f} 客:{lambdas['away']:.3f})")
            print(f"   权重: 历史{weights['historical']:.1%} + 市场{weights['market']:.1%}")
            print(f"   一致性: {lambdas['method_consistency']['assessment']} ({lambdas['method_consistency']['score']:.1%})")
            print(f"   置信度: {assessment['confidence_level']}")
            print(f"   建议: {assessment['recommendation']}")

def demo_comprehensive_analysis():
    """演示完整的比赛分析"""
    print_section_header("完整比赛分析演示")
    
    # 初始化完整系统
    dm = DataManager()
    ma = MarketAnalyzer()
    ha = HistoricalAnalyzer(dm)
    hp = HybridPredictor(ha, ma)
    engine = AdvancedPoissonEngine(hp)
    
    match_id = 2702969
    home_team = "哥德堡盖斯"
    away_team = "天狼星"
    
    print_subsection("全面分析报告")
    analysis = engine.comprehensive_match_analysis(match_id, home_team, away_team)
    
    if analysis['success']:
        # 生成并显示完整报告
        report = engine.generate_detailed_report(analysis)
        print(report)
        
        # 额外的技术细节
        print_subsection("技术细节")
        predictions = analysis['poisson_predictions']
        
        # 最可能比分前10
        print("🎯 完整比分概率排序 (前10):")
        for i, score in enumerate(predictions['most_likely_scores'][:10], 1):
            print(f"   {i:2d}. {score['score_string']:>3s}: {score['percentage']:5.1f}% (赔率 {score['odds']:6.1f})")
        
        # 进球分布统计
        stats = predictions['statistics_summary']
        print(f"\n📊 统计特征:")
        print(f"   主队进球方差: {stats['variance']['home']:.3f}")
        print(f"   客队进球方差: {stats['variance']['away']:.3f}")
        print(f"   主队进球标准差: {stats['standard_deviation']['home']:.3f}")
        print(f"   客队进球标准差: {stats['standard_deviation']['away']:.3f}")

def main():
    """主演示函数"""
    print("🚀 高级泊松足球分析系统 - 完整功能演示")
    print(f"📅 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔬 本演示将展示系统的所有核心功能和分析能力")
    
    try:
        # 1. 数据访问层演示
        demo_data_access()
        
        # 2. 市场分析层演示
        demo_market_analysis()
        
        # 3. 历史分析层演示
        demo_historical_analysis()
        
        # 4. 融合预测层演示
        demo_hybrid_prediction()
        
        # 5. 完整分析演示
        demo_comprehensive_analysis()
        
        print_section_header("演示完成")
        print("✅ 所有模块演示成功完成！")
        print("🎯 系统功能验证:")
        print("   ✅ 数据访问和质量评估")
        print("   ✅ 市场赔率分析和λ估算")
        print("   ✅ 历史数据分析和球队评估")
        print("   ✅ 多策略智能融合预测")
        print("   ✅ 完整的泊松分布建模")
        print("   ✅ 详细的概率分析和报告生成")
        print("\n🏆 高级泊松系统已准备就绪，可用于实际足球分析！")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 