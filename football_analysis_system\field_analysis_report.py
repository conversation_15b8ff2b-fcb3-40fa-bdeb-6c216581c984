#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
var game 参数字段详细分析报告
基于实际获取的数据进行字段含义推断和分析
"""

def analyze_game_fields():
    """分析var game参数的27个字段含义"""
    
    print("=" * 80)
    print("🎯 var game 参数字段详细分析报告")
    print("=" * 80)
    
    # 基于实际数据分析的字段含义
    field_analysis = {
        0: {
            "name": "公司ID",
            "description": "博彩公司的唯一标识符",
            "example": "281 (Bet365), 115 (威廉希尔), 90 (易胜博)",
            "data_type": "整数",
            "current_used": True,
            "note": "已在现有代码中使用，用于匹配目标公司"
        },
        1: {
            "name": "记录ID",
            "description": "该条赔率记录的唯一标识符",
            "example": "144419082, 144414641",
            "data_type": "长整数",
            "current_used": False,
            "note": "可用于追踪赔率记录的历史变化"
        },
        2: {
            "name": "公司名称",
            "description": "博彩公司的显示名称",
            "example": "Bet 365, <PERSON>, Easybets",
            "data_type": "字符串",
            "current_used": True,
            "note": "已在现有代码中使用，用于公司名称匹配"
        },
        3: {
            "name": "初始主胜赔率",
            "description": "比赛开盘时的主队获胜赔率",
            "example": "1.85, 1.65, 1.8",
            "data_type": "浮点数",
            "current_used": True,
            "note": "已在现有代码中使用 (Initial_W)"
        },
        4: {
            "name": "初始平局赔率",
            "description": "比赛开盘时的平局赔率",
            "example": "3.7, 4.33, 3.8",
            "data_type": "浮点数",
            "current_used": True,
            "note": "已在现有代码中使用 (Initial_D)"
        },
        5: {
            "name": "初始客胜赔率",
            "description": "比赛开盘时的客队获胜赔率",
            "example": "3.25, 3.9, 3.4",
            "data_type": "浮点数",
            "current_used": True,
            "note": "已在现有代码中使用 (Initial_L)"
        },
        6: {
            "name": "初始主胜概率",
            "description": "基于初始主胜赔率计算的获胜概率百分比",
            "example": "48.33%, 55.43%, 49.92%",
            "data_type": "浮点数",
            "current_used": False,
            "note": "新字段！可用于概率分析和投注价值计算"
        },
        7: {
            "name": "初始平局概率",
            "description": "基于初始平局赔率计算的概率百分比",
            "example": "24.16%, 21.12%, 23.65%",
            "data_type": "浮点数",
            "current_used": False,
            "note": "新字段！可用于概率分析"
        },
        8: {
            "name": "初始客胜概率",
            "description": "基于初始客胜赔率计算的获胜概率百分比",
            "example": "27.51%, 23.45%, 26.43%",
            "data_type": "浮点数",
            "current_used": False,
            "note": "新字段！可用于概率分析"
        },
        9: {
            "name": "初始返还率",
            "description": "初始赔率的返还率（越高对玩家越有利）",
            "example": "89.41%, 91.46%, 89.86%",
            "data_type": "浮点数",
            "current_used": True,
            "note": "已在现有代码中使用 (Initial_Payout_Rate)"
        },
        10: {
            "name": "即时主胜赔率",
            "description": "当前最新的主队获胜赔率",
            "example": "1.72, 1.75, 1.7",
            "data_type": "浮点数",
            "current_used": True,
            "note": "已在现有代码中使用 (Instant_W)"
        },
        11: {
            "name": "即时平局赔率",
            "description": "当前最新的平局赔率",
            "example": "4, 4.2, 4",
            "data_type": "浮点数",
            "current_used": True,
            "note": "已在现有代码中使用 (Instant_D)"
        },
        12: {
            "name": "即时客胜赔率",
            "description": "当前最新的客队获胜赔率",
            "example": "3.5, 3.4, 3.5",
            "data_type": "浮点数",
            "current_used": True,
            "note": "已在现有代码中使用 (Instant_L)"
        },
        13: {
            "name": "即时主胜概率",
            "description": "基于即时主胜赔率计算的获胜概率百分比",
            "example": "52.04%, 51.78%, 52.34%",
            "data_type": "浮点数",
            "current_used": False,
            "note": "新字段！可用于实时概率分析"
        },
        14: {
            "name": "即时平局概率",
            "description": "基于即时平局赔率计算的概率百分比",
            "example": "22.38%, 21.57%, 22.24%",
            "data_type": "浮点数",
            "current_used": False,
            "note": "新字段！可用于实时概率分析"
        },
        15: {
            "name": "即时客胜概率",
            "description": "基于即时客胜赔率计算的获胜概率百分比",
            "example": "25.58%, 26.65%, 25.42%",
            "data_type": "浮点数",
            "current_used": False,
            "note": "新字段！可用于实时概率分析"
        },
        16: {
            "name": "即时返还率",
            "description": "当前赔率的返还率",
            "example": "89.52%, 90.61%, 88.97%",
            "data_type": "浮点数",
            "current_used": True,
            "note": "已在现有代码中使用 (Payout_Rate)"
        },
        17: {
            "name": "主胜赔率变化指数",
            "description": "主胜赔率相对于初始值的变化指数",
            "example": "0.92, 0.93, 0.90",
            "data_type": "浮点数",
            "current_used": False,
            "note": "新字段！可用于赔率变化趋势分析"
        },
        18: {
            "name": "平局赔率变化指数",
            "description": "平局赔率相对于初始值的变化指数",
            "example": "0.86, 0.91, 0.86",
            "data_type": "浮点数",
            "current_used": False,
            "note": "新字段！可用于赔率变化趋势分析"
        },
        19: {
            "name": "客胜赔率变化指数",
            "description": "客胜赔率相对于初始值的变化指数",
            "example": "0.88, 0.86, 0.88",
            "data_type": "浮点数",
            "current_used": False,
            "note": "新字段！可用于赔率变化趋势分析"
        },
        20: {
            "name": "更新时间戳",
            "description": "该条赔率记录的最后更新时间",
            "example": "2025,08-1,03,05,56,00",
            "data_type": "时间字符串",
            "current_used": False,
            "note": "新字段！可用于时间序列分析和数据新鲜度判断"
        },
        21: {
            "name": "公司标识与地区",
            "description": "公司简称和注册地区信息",
            "example": "36*(英国), 威*(英国), 易*(安提瓜和巴布达)",
            "data_type": "字符串",
            "current_used": False,
            "note": "新字段！包含公司地理位置信息，可用于地区分析"
        },
        22: {
            "name": "状态标志1",
            "description": "赔率状态标志（可能表示是否活跃）",
            "example": "1, 0",
            "data_type": "整数",
            "current_used": False,
            "note": "新字段！可能表示赔率是否可用或公司是否活跃"
        },
        23: {
            "name": "状态标志2",
            "description": "次要状态标志",
            "example": "0, 1",
            "data_type": "整数",
            "current_used": False,
            "note": "新字段！可能表示特殊状态或推广标志"
        },
        24: {
            "name": "主胜指数/凯利指数",
            "description": "主胜的投注价值指数",
            "example": "0.98, 0.88, 0.96",
            "data_type": "浮点数",
            "current_used": False,
            "note": "新字段！可能是凯利指数，用于投注价值评估"
        },
        25: {
            "name": "平局指数/凯利指数",
            "description": "平局的投注价值指数",
            "example": "0.80, 0.93, 0.82",
            "data_type": "浮点数",
            "current_used": False,
            "note": "新字段！可能是凯利指数，用于投注价值评估"
        },
        26: {
            "name": "客胜指数/凯利指数",
            "description": "客胜的投注价值指数",
            "example": "0.82, 0.98, 0.86",
            "data_type": "浮点数",
            "current_used": False,
            "note": "新字段！可能是凯利指数，用于投注价值评估"
        }
    }
    
    # 统计分析
    total_fields = len(field_analysis)
    used_fields = sum(1 for field in field_analysis.values() if field["current_used"])
    new_fields = total_fields - used_fields
    
    print(f"📊 字段统计:")
    print(f"   总字段数: {total_fields}")
    print(f"   已使用字段: {used_fields}")
    print(f"   新发现字段: {new_fields}")
    print()
    
    # 显示已使用的字段
    print("✅ 当前已使用的字段:")
    for idx, field in field_analysis.items():
        if field["current_used"]:
            print(f"   [{idx:2d}] {field['name']}: {field['description']}")
    print()
    
    # 显示新发现的字段
    print("🆕 新发现的可用字段:")
    for idx, field in field_analysis.items():
        if not field["current_used"]:
            print(f"   [{idx:2d}] {field['name']}: {field['description']}")
            print(f"        示例: {field['example']}")
            print(f"        用途: {field['note']}")
            print()
    
    # 按类别分组分析
    print("📋 字段分类分析:")
    
    categories = {
        "基础信息": [0, 1, 2, 20, 21],
        "初始赔率": [3, 4, 5],
        "即时赔率": [10, 11, 12],
        "概率数据": [6, 7, 8, 13, 14, 15],
        "返还率": [9, 16],
        "变化指数": [17, 18, 19],
        "状态标志": [22, 23],
        "价值指数": [24, 25, 26]
    }
    
    for category, field_indices in categories.items():
        print(f"\n🔸 {category}:")
        for idx in field_indices:
            field = field_analysis[idx]
            status = "✅已用" if field["current_used"] else "🆕新增"
            print(f"   [{idx:2d}] {field['name']} {status}")
    
    print("\n" + "=" * 80)
    print("💡 建议新增功能:")
    print("   1. 概率分析: 使用字段6-8, 13-15进行概率变化分析")
    print("   2. 赔率趋势: 使用字段17-19分析赔率变化趋势")
    print("   3. 时间序列: 使用字段20进行时间序列分析")
    print("   4. 价值投注: 使用字段24-26进行投注价值评估")
    print("   5. 地区分析: 使用字段21进行博彩公司地区分布分析")
    print("   6. 数据质量: 使用字段22-23判断数据可靠性")
    print("=" * 80)
    
    return field_analysis

if __name__ == "__main__":
    analyze_game_fields() 