import tkinter as tk
import os
import sys
import logging
import threading
from tkinter import ttk
import time
from football_analysis_system.ui.app import FootballAnalysisApp
from football_analysis_system.config import DATA_DIR, APP_TITLE, COLOR_BG

class SplashScreen:
    """简化的启动画面类"""
    def __init__(self, root):
        self.root = root
        self.root.overrideredirect(True)  # 无边框窗口
        self.root.geometry("400x200")
        self.root.configure(bg=COLOR_BG)

        # 居中显示
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = int((screen_width - 400) / 2)
        y = int((screen_height - 200) / 2)
        self.root.geometry(f"400x200+{x}+{y}")

        # 创建标签
        self.label = tk.Label(
            root,
            text="足球分析系统正在启动...",
            font=("Helvetica", 16),
            bg=COLOR_BG,
            fg="white"
        )
        self.label.pack(pady=50)

        # 创建进度条
        self.progress = ttk.Progressbar(
            root,
            orient="horizontal",
            length=350,
            mode="indeterminate"
        )
        self.progress.pack(pady=20)
        self.progress.start(10)

        # 创建状态标签
        self.status_label = tk.Label(
            root,
            text="正在初始化...",
            font=("Helvetica", 10),
            bg=COLOR_BG,
            fg="white"
        )
        self.status_label.pack(pady=10)

    def update_status(self, text):
        """更新状态文本"""
        self.status_label.config(text=text)
        self.root.update()

def launch_main_app():
    """启动主应用"""
    # 设置控制台输出编码
    try:
        # 设置标准输出和标准错误的编码为utf-8
        if sys.stdout.encoding != 'utf-8':
            sys.stdout.reconfigure(encoding='utf-8')
        if sys.stderr.encoding != 'utf-8':
            sys.stderr.reconfigure(encoding='utf-8')

        # 对于Windows系统，还需要设置控制台代码页
        if sys.platform == 'win32':
            import subprocess
            subprocess.run(['chcp', '65001'], shell=True, check=False)
            print("已设置控制台编码为UTF-8")
    except Exception as e:
        print(f"设置控制台编码失败: {e}")

    # 确保必要的目录存在
    os.makedirs('logs', exist_ok=True)

    # 设置基本日志配置
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 创建启动画面窗口
    splash_root = tk.Tk()
    splash = SplashScreen(splash_root)
    splash_root.update()

    def init_main_app():
        # 创建主应用等待一段时间
        splash.update_status("正在加载程序...")

        # 创建主窗口 - 但还不显示
        main_root = tk.Tk()
        main_root.withdraw()
        main_root.title(APP_TITLE)
        main_root.geometry("1400x900")
        main_root.configure(bg=COLOR_BG)

        # 初始化应用
        splash.update_status("正在初始化应用...")
        app = FootballAnalysisApp(main_root)

        # 关闭启动窗口，显示主窗口
        splash_root.destroy()
        main_root.deiconify()

        # 启动主循环
        main_root.mainloop()

    # 延迟一点时间后启动主应用
    splash_root.after(1000, init_main_app)
    splash_root.mainloop()

if __name__ == "__main__":
    launch_main_app()