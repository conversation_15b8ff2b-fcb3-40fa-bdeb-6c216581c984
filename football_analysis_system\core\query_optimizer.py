"""
数据库查询优化器
提供查询优化、批量操作、索引管理等功能
"""

import sqlite3
import time
from typing import Dict, List, Any, Optional, Tuple, Union
from contextlib import contextmanager
from functools import wraps
from collections import defaultdict

from .database_manager import database_manager
from .logger_manager import get_logger
from .exception_handler import handle_exceptions, DatabaseError


class QueryOptimizer:
    """查询优化器"""
    
    def __init__(self):
        self.logger = get_logger('query_optimizer')
        self.query_stats = defaultdict(lambda: {"count": 0, "total_time": 0.0, "avg_time": 0.0})
        self.slow_query_threshold = 1.0  # 慢查询阈值（秒）
    
    def analyze_query_performance(self, query: str, execution_time: float):
        """
        分析查询性能
        
        Args:
            query: SQL查询语句
            execution_time: 执行时间（秒）
        """
        # 简化查询语句用于统计（移除参数值）
        normalized_query = self._normalize_query(query)
        
        stats = self.query_stats[normalized_query]
        stats["count"] += 1
        stats["total_time"] += execution_time
        stats["avg_time"] = stats["total_time"] / stats["count"]
        
        # 记录慢查询
        if execution_time > self.slow_query_threshold:
            self.logger.warning(
                f"慢查询检测: {execution_time:.3f}s - {query[:100]}..."
            )
    
    def _normalize_query(self, query: str) -> str:
        """标准化查询语句"""
        import re
        # 移除多余空格
        query = re.sub(r'\s+', ' ', query.strip())
        # 替换参数占位符
        query = re.sub(r'= \?', '= ?', query)
        query = re.sub(r'IN \([?,\s]+\)', 'IN (?)', query)
        return query[:200]  # 限制长度
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        slow_queries = []
        frequent_queries = []
        
        for query, stats in self.query_stats.items():
            if stats["avg_time"] > self.slow_query_threshold:
                slow_queries.append({
                    "query": query,
                    "avg_time": stats["avg_time"],
                    "count": stats["count"],
                    "total_time": stats["total_time"]
                })
            
            if stats["count"] > 10:  # 频繁查询阈值
                frequent_queries.append({
                    "query": query,
                    "count": stats["count"],
                    "avg_time": stats["avg_time"]
                })
        
        # 按平均时间排序
        slow_queries.sort(key=lambda x: x["avg_time"], reverse=True)
        frequent_queries.sort(key=lambda x: x["count"], reverse=True)
        
        return {
            "total_queries": sum(stats["count"] for stats in self.query_stats.values()),
            "unique_queries": len(self.query_stats),
            "slow_queries": slow_queries[:10],  # 前10个慢查询
            "frequent_queries": frequent_queries[:10],  # 前10个频繁查询
            "avg_query_time": sum(stats["total_time"] for stats in self.query_stats.values()) / 
                             max(1, sum(stats["count"] for stats in self.query_stats.values()))
        }


class BatchQueryExecutor:
    """批量查询执行器"""
    
    def __init__(self, db_name: str):
        self.db_name = db_name
        self.logger = get_logger('batch_executor')
        self.pending_queries = []
        self.batch_size = 100
    
    def add_query(self, query: str, params: tuple = None):
        """添加查询到批次"""
        self.pending_queries.append((query, params))
    
    @handle_exceptions(fallback_value=[], reraise=True)
    def execute_batch(self) -> List[Any]:
        """执行批量查询"""
        if not self.pending_queries:
            return []
        
        results = []
        
        try:
            with database_manager.get_connection(self.db_name) as conn:
                cursor = conn.cursor()
                
                for query, params in self.pending_queries:
                    if params:
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query)
                    
                    if cursor.description:
                        columns = [desc[0] for desc in cursor.description]
                        rows = cursor.fetchall()
                        results.append([dict(zip(columns, row)) for row in rows])
                    else:
                        results.append(cursor.rowcount)
                
                conn.commit()
                
        except sqlite3.Error as e:
            raise DatabaseError(f"批量查询执行失败: {str(e)}", operation="BATCH")
        finally:
            self.pending_queries.clear()
        
        return results
    
    def execute_batch_updates(self, query: str, params_list: List[tuple]) -> int:
        """执行批量更新"""
        if not params_list:
            return 0
        
        try:
            with database_manager.get_connection(self.db_name) as conn:
                cursor = conn.cursor()
                cursor.executemany(query, params_list)
                conn.commit()
                return cursor.rowcount
                
        except sqlite3.Error as e:
            raise DatabaseError(f"批量更新执行失败: {str(e)}", operation="BATCH_UPDATE")


class IndexManager:
    """索引管理器"""
    
    def __init__(self, db_name: str):
        self.db_name = db_name
        self.logger = get_logger('index_manager')
    
    @handle_exceptions(fallback_value=[], reraise=False)
    def analyze_missing_indexes(self) -> List[Dict[str, Any]]:
        """分析缺失的索引"""
        suggestions = []
        
        try:
            with database_manager.get_connection(self.db_name) as conn:
                cursor = conn.cursor()
                
                # 获取所有表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                for table in tables:
                    # 获取表结构
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = cursor.fetchall()
                    
                    # 获取现有索引
                    cursor.execute(f"PRAGMA index_list({table})")
                    existing_indexes = cursor.fetchall()
                    
                    indexed_columns = set()
                    for index in existing_indexes:
                        cursor.execute(f"PRAGMA index_info({index[1]})")
                        index_columns = cursor.fetchall()
                        for col in index_columns:
                            indexed_columns.add(col[2])
                    
                    # 建议为外键字段创建索引
                    for col in columns:
                        col_name = col[1]
                        col_type = col[2]
                        
                        if (col_name.endswith('_id') and col_name not in indexed_columns):
                            suggestions.append({
                                "table": table,
                                "column": col_name,
                                "reason": "外键字段建议创建索引",
                                "suggested_index": f"CREATE INDEX idx_{table}_{col_name} ON {table}({col_name})"
                            })
                        
                        if (col_name in ['created_at', 'updated_at', 'match_time'] and 
                            col_name not in indexed_columns):
                            suggestions.append({
                                "table": table,
                                "column": col_name,
                                "reason": "时间字段建议创建索引",
                                "suggested_index": f"CREATE INDEX idx_{table}_{col_name} ON {table}({col_name})"
                            })
        
        except sqlite3.Error as e:
            self.logger.error(f"分析索引失败: {e}")
        
        return suggestions
    
    @handle_exceptions(fallback_value=False, reraise=False)
    def create_recommended_indexes(self) -> bool:
        """创建推荐的索引"""
        suggestions = self.analyze_missing_indexes()
        created_count = 0
        
        try:
            with database_manager.get_connection(self.db_name) as conn:
                cursor = conn.cursor()
                
                for suggestion in suggestions:
                    try:
                        cursor.execute(suggestion["suggested_index"])
                        created_count += 1
                        self.logger.info(f"创建索引: {suggestion['suggested_index']}")
                    except sqlite3.Error as e:
                        self.logger.warning(f"创建索引失败: {e}")
                
                conn.commit()
                
        except sqlite3.Error as e:
            self.logger.error(f"批量创建索引失败: {e}")
            return False
        
        self.logger.info(f"成功创建 {created_count} 个索引")
        return created_count > 0


class QueryCache:
    """查询缓存"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 300):
        self.cache = {}
        self.access_times = {}
        self.max_size = max_size
        self.ttl = ttl  # 缓存时间（秒）
        self.logger = get_logger('query_cache')
    
    def _generate_cache_key(self, query: str, params: tuple = None) -> str:
        """生成缓存键"""
        import hashlib
        content = query + str(params or ())
        return hashlib.md5(content.encode()).hexdigest()
    
    def get(self, query: str, params: tuple = None) -> Optional[Any]:
        """获取缓存结果"""
        cache_key = self._generate_cache_key(query, params)
        
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            
            # 检查是否过期
            if time.time() - timestamp < self.ttl:
                self.access_times[cache_key] = time.time()
                return cached_data
            else:
                # 删除过期缓存
                del self.cache[cache_key]
                if cache_key in self.access_times:
                    del self.access_times[cache_key]
        
        return None
    
    def set(self, query: str, result: Any, params: tuple = None):
        """设置缓存"""
        cache_key = self._generate_cache_key(query, params)
        
        # 如果缓存已满，删除最久未访问的项
        if len(self.cache) >= self.max_size:
            self._evict_lru()
        
        self.cache[cache_key] = (result, time.time())
        self.access_times[cache_key] = time.time()
    
    def _evict_lru(self):
        """删除最久未访问的缓存项"""
        if not self.access_times:
            return
        
        # 找到最久未访问的键
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        if lru_key in self.cache:
            del self.cache[lru_key]
        del self.access_times[lru_key]
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
        self.logger.info("查询缓存已清空")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        current_time = time.time()
        expired_count = 0
        
        for key, (_, timestamp) in list(self.cache.items()):
            if current_time - timestamp >= self.ttl:
                expired_count += 1
        
        return {
            "total_entries": len(self.cache),
            "expired_entries": expired_count,
            "cache_hit_ratio": "需要实时统计",  # 需要在实际使用中统计
            "max_size": self.max_size,
            "ttl": self.ttl
        }


# 性能监控装饰器
def monitor_query_performance(query_optimizer: QueryOptimizer):
    """查询性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 尝试从参数中获取查询语句
                query = None
                if len(args) > 1 and isinstance(args[1], str):
                    query = args[1]
                elif 'query' in kwargs:
                    query = kwargs['query']
                
                if query:
                    query_optimizer.analyze_query_performance(query, execution_time)
                
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                query_optimizer.logger.error(f"查询失败 ({execution_time:.3f}s): {str(e)}")
                raise
        return wrapper
    return decorator


# 全局实例
query_optimizer = QueryOptimizer()
query_cache = QueryCache()


def optimize_database_performance(db_name: str) -> Dict[str, Any]:
    """
    优化数据库性能
    
    Args:
        db_name: 数据库名称
        
    Returns:
        优化结果报告
    """
    result = {
        "database": db_name,
        "optimizations": [],
        "performance_before": None,
        "performance_after": None,
        "success": False
    }
    
    try:
        # 创建索引管理器
        index_manager = IndexManager(db_name)
        
        # 分析并创建推荐索引
        missing_indexes = index_manager.analyze_missing_indexes()
        if missing_indexes:
            result["optimizations"].append(f"发现 {len(missing_indexes)} 个建议索引")
            
            if index_manager.create_recommended_indexes():
                result["optimizations"].append("成功创建推荐索引")
        
        # 执行数据库优化命令
        try:
            with database_manager.get_connection(db_name) as conn:
                conn.execute("ANALYZE")
                conn.execute("VACUUM")
                result["optimizations"].append("执行了ANALYZE和VACUUM操作")
        except Exception as e:
            result["optimizations"].append(f"数据库优化操作失败: {str(e)}")
        
        result["success"] = True
        
    except Exception as e:
        result["error"] = str(e)
    
    return result