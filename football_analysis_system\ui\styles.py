import tkinter as tk
from tkinter import ttk
from football_analysis_system.config import (
    COLOR_PRIMARY, COLOR_SECONDARY, COLOR_ACCENT, COLOR_BG,
    COLOR_TEXT, COLOR_TEXT_LIGHT, COLOR_HOME, COLOR_AWAY, COLOR_DRAW,
    COLOR_WARNING, COLOR_HIGHLIGHT, FONT_NORMAL, FONT_LARGE,
    FONT_HEADER, FONT_SUBHEADER
)

class StyleManager:
    """UI样式管理类"""

    @staticmethod
    def setup_styles():
        """设置应用样式"""
        style = ttk.Style()

        # 使用更现代的主题
        style.theme_use('clam')

        # 配置全局颜色和字体
        style.configure('TFrame', background=COLOR_BG)
        style.configure('TLabel', background=COLOR_BG, foreground=COLOR_TEXT, font=FONT_NORMAL)
        style.configure('TButton',
                       font=FONT_NORMAL,
                       background=COLOR_SECONDARY,
                       foreground='white',
                       borderwidth=0,
                       focusthickness=0,
                       padding=[12, 8])
        style.map('TButton',
                 background=[('active', COLOR_SECONDARY), ('pressed', COLOR_PRIMARY)],
                 relief=[('pressed', 'flat'), ('!pressed', 'flat')])

        style.configure('TCombobox',
                       font=FONT_NORMAL,
                       background=COLOR_BG,
                       fieldbackground='white',
                       arrowcolor=COLOR_PRIMARY,
                       borderwidth=1,
                       padding=5)
        style.map('TCombobox',
                 fieldbackground=[('readonly', 'white')],
                 selectbackground=[('readonly', COLOR_SECONDARY)],
                 selectforeground=[('readonly', 'white')])

        # 自定义按钮样式
        style.configure('Primary.TButton',
                       font=FONT_NORMAL,
                       background=COLOR_PRIMARY,
                       foreground='white',
                       borderwidth=0,
                       focusthickness=0,
                       padding=[12, 8])
        style.map('Primary.TButton',
                 background=[('active', COLOR_SECONDARY), ('pressed', COLOR_SECONDARY)],
                 foreground=[('active', 'white'), ('pressed', 'white')],
                 relief=[('pressed', 'flat'), ('!pressed', 'flat')])

        # 添加更醒目的Action按钮样式，用于关键操作
        style.configure('Action.TButton',
                       font=('Arial', 11, 'bold'),
                       background='#4a86e8',  # 更亮的蓝色
                       foreground='white',
                       borderwidth=1,
                       focusthickness=1,
                       relief='raised',
                       padding=[12, 10])
        style.map('Action.TButton',
                 background=[('active', '#2b5797'), ('pressed', '#1c3c6d')],
                 foreground=[('active', 'white'), ('pressed', 'white')],
                 relief=[('pressed', 'sunken'), ('!pressed', 'raised')])

        style.configure('Success.TButton',
                       font=FONT_NORMAL,
                       background=COLOR_ACCENT,
                       foreground='white',
                       borderwidth=0,
                       focusthickness=0,
                       padding=[12, 8])
        style.map('Success.TButton',
                 background=[('active', '#2ecc71'), ('pressed', '#2ecc71')],
                 foreground=[('active', 'white'), ('pressed', 'white')],
                 relief=[('pressed', 'flat'), ('!pressed', 'flat')])

        style.configure('Warning.TButton',
                       font=FONT_NORMAL,
                       background=COLOR_WARNING,
                       foreground='white',
                       borderwidth=0,
                       focusthickness=0,
                       padding=[12, 8])
        style.map('Warning.TButton',
                 background=[('active', '#c0392b'), ('pressed', '#c0392b')],
                 foreground=[('active', 'white'), ('pressed', 'white')],
                 relief=[('pressed', 'flat'), ('!pressed', 'flat')])

        # 自定义标题标签样式
        style.configure('Header.TLabel',
                       font=FONT_HEADER,
                       foreground=COLOR_PRIMARY,
                       background=COLOR_BG)
        style.configure('Subheader.TLabel',
                       font=FONT_SUBHEADER,
                       foreground=COLOR_PRIMARY,
                       background=COLOR_BG)

        # 比赛信息标签样式
        style.configure('MatchInfo.TLabel',
                       font=FONT_LARGE,
                       foreground=COLOR_TEXT,
                       background=COLOR_BG)

        # 结果标签样式
        style.configure('Result.TLabel',
                       font=FONT_LARGE,
                       foreground=COLOR_ACCENT,
                       background=COLOR_BG)
        style.configure('Error.TLabel',
                       font=FONT_NORMAL,
                       foreground=COLOR_WARNING,
                       background=COLOR_BG)

        # 设置主客队颜色
        style.configure('Home.TLabel',
                       foreground=COLOR_HOME,
                       background=COLOR_BG,
                       font=FONT_LARGE)
        style.configure('Away.TLabel',
                       foreground=COLOR_AWAY,
                       background=COLOR_BG,
                       font=FONT_LARGE)
        style.configure('Draw.TLabel',
                       foreground=COLOR_DRAW,
                       background=COLOR_BG,
                       font=FONT_LARGE)

        # 树形视图样式设置 - 更现代的外观
        style.configure('Treeview',
                       background='white',
                       foreground=COLOR_TEXT,
                       rowheight=36,  # 增加行高
                       font=FONT_NORMAL,
                       fieldbackground='white',
                       borderwidth=0)
        style.configure('Treeview.Heading',
                       font=FONT_LARGE,
                       background=COLOR_PRIMARY,
                       foreground='white',
                       relief='flat',
                       borderwidth=0,
                       padding=[5, 5])
        style.map('Treeview',
                 background=[('selected', COLOR_SECONDARY)],
                 foreground=[('selected', 'white')])

        # 设置选项卡样式 - 更现代的标签页
        style.configure('TNotebook',
                       background=COLOR_BG,
                       tabposition='n',
                       borderwidth=0)
        style.configure('TNotebook.Tab',
                       font=FONT_NORMAL,
                       padding=[15, 8],  # 增加内边距
                       background=COLOR_BG,
                       foreground=COLOR_TEXT,
                       borderwidth=0)
        style.map('TNotebook.Tab',
                 background=[('selected', COLOR_SECONDARY)],
                 foreground=[('selected', 'white')],
                 expand=[('selected', [0, 0, 0, 2])])

        # 设置标签框架样式 - 添加圆角和阴影效果
        style.configure('TLabelframe',
                       background=COLOR_BG,
                       borderwidth=1,
                       relief='solid')
        style.configure('TLabelframe.Label',
                       font=FONT_LARGE,
                       background=COLOR_BG,
                       foreground=COLOR_PRIMARY,
                       padding=[5, 2])

        # 滚动条样式 - 更细的现代滚动条
        style.configure('TScrollbar',
                       background=COLOR_BG,
                       arrowcolor=COLOR_TEXT,
                       bordercolor=COLOR_BG,
                       troughcolor=COLOR_BG,
                       relief='flat',
                       borderwidth=0,
                       width=10)  # 更窄的滚动条
        style.map('TScrollbar',
                 background=[('active', COLOR_SECONDARY), ('pressed', COLOR_PRIMARY)],
                 arrowcolor=[('active', 'white')])

        # 进度条样式 - 更平滑的进度条
        style.configure('TProgressbar',
                       background=COLOR_ACCENT,
                       troughcolor='#e0e0e0',  # 更亮的轨道颜色
                       bordercolor=COLOR_BG,
                       lightcolor=COLOR_ACCENT,
                       darkcolor=COLOR_ACCENT,
                       borderwidth=0,
                       thickness=10)  # 更粗的进度条

        # 设置一些自定义颜色
        COLOR_LIGHT_BG = "#F8F9FA"  # 卡片头部背景色
        COLOR_CARD_BORDER = "#E0E0E0"  # 卡片边框颜色
        
        # 设置文本框样式
        style.configure("TEntry",
                       font=FONT_NORMAL,
                       fieldbackground="white",
                       foreground=COLOR_TEXT,
                       borderwidth=1,
                       relief="flat",
                       padding=[5, 5])
        
        # 设置下拉框样式
        style.configure("TCombobox",
                       font=FONT_NORMAL,
                       background="white",
                       fieldbackground="white",
                       foreground=COLOR_TEXT,
                       arrowcolor=COLOR_TEXT,
                       borderwidth=1,
                       padding=[5, 5])
        style.map("TCombobox",
                 fieldbackground=[('readonly', "white")],
                 selectbackground=[('readonly', COLOR_PRIMARY)],
                 selectforeground=[('readonly', "white")])
        
        # 设置卡片样式 - 用于赔率分析法则标签页
        style.configure('Card.TFrame',
                       background='white',
                       relief='solid',
                       borderwidth=1,
                       bordercolor=COLOR_CARD_BORDER)
        style.configure('CardHeader.TFrame',
                       background=COLOR_LIGHT_BG,
                       relief='flat')
        style.configure('CardContent.TFrame',
                       background='white',
                       relief='flat')

        return style

    @staticmethod
    def create_football_icon(size=64):
        """
        创建简单的足球图标

        Args:
            size: 图标大小

        Returns:
            ImageTk.PhotoImage: 图标图像
        """
        try:
            from PIL import Image, ImageTk, ImageDraw
            import numpy as np

            # 创建一个透明背景的图像
            icon = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(icon)

            # 画圆作为足球轮廓
            draw.ellipse([(2, 2), (size-2, size-2)], fill='white', outline='black')

            # 画六边形图案代表足球
            center_x, center_y = size//2, size//2
            radius = size//2 - 4
            for i in range(5):
                angle = i * 2 * np.pi / 5
                x1 = center_x + int(radius * 0.7 * np.cos(angle))
                y1 = center_y + int(radius * 0.7 * np.sin(angle))

                # 检查是否支持regular_polygon方法
                if hasattr(draw, 'regular_polygon'):
                    draw.regular_polygon((x1, y1, radius//3), 6, rotation=30, fill='black', outline='black')
                else:
                    # 手动绘制六边形
                    points = []
                    for j in range(6):
                        a = j * 2 * np.pi / 6 + np.pi/6  # 30度旋转
                        px = x1 + int((radius//3) * np.cos(a))
                        py = y1 + int((radius//3) * np.sin(a))
                        points.append((px, py))
                    draw.polygon(points, fill='black', outline='black')

            # 转换为PhotoImage
            return ImageTk.PhotoImage(icon)
        except Exception as e:
            print(f"创建足球图标时出错: {e}")
            return None