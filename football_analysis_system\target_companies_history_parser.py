#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
目标公司历史赔率解析器 - 专门解析配置的目标公司的完整历史赔率数据
"""

import time
import requests
import re
import json
import sqlite3
from datetime import datetime

# 目标公司列表
TARGET_COMPANIES = [
    '澳门', '威廉希尔', '易胜博', '香港马会', 'BWIN', '伟德', 
    'Nordicbet', '利记', 'BetISn', 'iddaa'
]

def get_match_js_content(match_id):
    """获取比赛的JS内容"""
    timestamp_part = str(int(time.time() * 1000))
    if len(timestamp_part) > 13:
        timestamp_part = timestamp_part[-13:]
    else:
        timestamp_part = timestamp_part.ljust(13, '0')
    timestamp = "007" + timestamp_part
    
    url = f"https://1x2d.titan007.com/{match_id}.js?r={timestamp}"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Referer': f'https://op1.titan007.com/oddslist/{match_id}.htm',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        if response.status_code == 200:
            print(f"✅ 成功获取比赛 {match_id} 的JS数据")
            return response.text
        return None
    except Exception as e:
        print(f"❌ 请求出错: {e}")
        return None

def get_target_company_mapping(js_content, target_companies):
    """从game数组获取目标公司的ID到名称的映射"""
    company_mapping = {}
    
    game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', js_content, re.DOTALL)
    if not game_match:
        print("❌ 未找到game数组")
        return {}
    
    game_data = game_match.group(1)
    company_records = re.findall(r'"([^"]*)"', game_data)
    
    print(f"📋 开始匹配目标公司...")
    
    for record in company_records:
        fields = record.split('|')
        if len(fields) >= 3:
            company_id = fields[0]
            record_id = fields[1]
            company_name = fields[2].strip()
            
            # 匹配目标公司
            matched_target_name = None
            for target_name in target_companies:
                normalized_target_name = target_name.lower().replace(' ','')
                normalized_company_name = company_name.lower().replace(' ','')
                
                # 精确匹配
                if normalized_target_name == normalized_company_name:
                    matched_target_name = target_name
                    break
                
                # 特殊匹配规则
                if target_name == '威廉希尔' and (company_id == '115' or company_name.startswith('威') or 'william hill' in normalized_company_name):
                    matched_target_name = target_name
                    break
                if target_name == '易胜博' and (company_id == '90' or company_name.startswith('易') or 'easybets' in normalized_company_name):
                    matched_target_name = target_name
                    break
                if target_name == '香港马会' and (company_id == '432' or company_name.startswith('香港马') or 'hkjc' in normalized_company_name):
                    matched_target_name = target_name
                    break
                if target_name == '澳门' and (company_id == '80' or company_name.startswith('澳') or 'macao' in normalized_company_name):
                    matched_target_name = target_name
                    break
                if target_name == 'BWIN' and (company_id == '255' or company_name.startswith('Bwi')):
                    matched_target_name = target_name
                    break
                if target_name == '伟德' and (company_id == '81' or company_name.startswith('伟')):
                    matched_target_name = target_name
                    break
                if target_name == 'Nordicbet' and (company_id == '4' or company_name.startswith('Nordic')):
                    matched_target_name = target_name
                    break
                if target_name == '利记' and (company_id == '474' or company_name.startswith('利')):
                    matched_target_name = target_name
                    break
                if target_name == 'BetISn' and (company_id == '937' or 'betisn' in normalized_company_name):
                    matched_target_name = target_name
                    break
                if target_name == 'iddaa' and (company_id == '657' or 'iddaa' in normalized_company_name):
                    matched_target_name = target_name
                    break
            
            if matched_target_name:
                company_mapping[record_id] = {
                    'company_id': company_id,
                    'company_name': company_name,
                    'target_name': matched_target_name
                }
                print(f"  ✅ 匹配到: {matched_target_name} -> {company_name} (ID: {company_id}, 记录ID: {record_id})")
    
    print(f"📊 总共匹配到 {len(company_mapping)} 家目标公司")
    return company_mapping

def parse_target_companies_history(js_content, target_companies):
    """解析目标公司的历史赔率数据"""
    
    # 获取目标公司映射
    company_mapping = get_target_company_mapping(js_content, target_companies)
    if not company_mapping:
        print("❌ 未找到任何目标公司")
        return {}
    
    # 提取gameDetail数据
    gamedetail_match = re.search(r'var\s+gameDetail\s*=\s*Array\((.*?)\);', js_content, re.DOTALL)
    if not gamedetail_match:
        print("❌ 未找到gameDetail数组")
        return {}
    
    gamedetail_data = gamedetail_match.group(1)
    company_records = re.findall(r'"([^"]*)"', gamedetail_data)
    
    print(f"🔍 开始解析目标公司的历史赔率...")
    
    target_companies_history = {}
    
    for record in company_records:
        try:
            # gameDetail格式: record_id^历史数据
            parts = record.split('^')
            if len(parts) < 2:
                continue
            
            record_id = parts[0]
            history_data = parts[1]
            
            # 检查是否为目标公司
            if record_id not in company_mapping:
                continue
            
            company_info = company_mapping[record_id]
            target_name = company_info['target_name']
            
            print(f"  📈 解析 {target_name} 的历史数据...")
            
            # 解析历史数据，用分号分隔不同时间点的记录
            history_entries = history_data.split(';')
            
            odds_history = []
            for entry in history_entries:
                if entry.strip():
                    # 格式: 主胜|平局|客胜|时间|kelly1|kelly2|kelly3|年份
                    parts_entry = entry.split('|')
                    
                    if len(parts_entry) >= 8:
                        try:
                            history_record = {
                                'home_odds': float(parts_entry[0]) if parts_entry[0] else None,
                                'draw_odds': float(parts_entry[1]) if parts_entry[1] else None,
                                'away_odds': float(parts_entry[2]) if parts_entry[2] else None,
                                'update_time': parts_entry[3],  # 格式: MM-DD HH:MM
                                'kelly_home': float(parts_entry[4]) if parts_entry[4] else None,
                                'kelly_draw': float(parts_entry[5]) if parts_entry[5] else None,
                                'kelly_away': float(parts_entry[6]) if parts_entry[6] else None,
                                'year': parts_entry[7]
                            }
                            odds_history.append(history_record)
                        except ValueError:
                            continue
            
            if odds_history:
                target_companies_history[target_name] = {
                    'company_id': company_info['company_id'],
                    'company_name': company_info['company_name'],
                    'record_id': record_id,
                    'target_name': target_name,
                    'history_count': len(odds_history),
                    'odds_history': odds_history
                }
                
                print(f"    ✅ {target_name}: 解析出 {len(odds_history)} 条历史记录")
                
        except Exception as e:
            print(f"    ❌ 解析记录失败: {e}")
            continue
    
    return target_companies_history

def display_target_companies_summary(target_companies_history):
    """显示目标公司历史赔率摘要"""
    
    print(f"\n" + "="*60)
    print(f"📊 目标公司历史赔率摘要")
    print(f"="*60)
    
    if not target_companies_history:
        print("❌ 没有找到任何目标公司的历史数据")
        return
    
    total_records = sum(data['history_count'] for data in target_companies_history.values())
    
    print(f"🎯 匹配的目标公司数: {len(target_companies_history)}")
    print(f"📈 总历史记录数: {total_records}")
    
    print(f"\n--- 各公司详细信息 ---")
    for target_name, data in target_companies_history.items():
        print(f"\n🏢 {target_name}")
        print(f"   公司名称: {data['company_name']}")
        print(f"   公司ID: {data['company_id']}")
        print(f"   记录ID: {data['record_id']}")
        print(f"   历史记录数: {data['history_count']}")
        
        # 显示最新和最早的赔率
        if data['odds_history']:
            latest = data['odds_history'][0]
            earliest = data['odds_history'][-1]
            
            print(f"   最新赔率 ({latest['update_time']}): {latest['home_odds']:.2f} / {latest['draw_odds']:.2f} / {latest['away_odds']:.2f}")
            print(f"   最早赔率 ({earliest['update_time']}): {earliest['home_odds']:.2f} / {earliest['draw_odds']:.2f} / {earliest['away_odds']:.2f}")
            
            # 计算变化
            if latest['home_odds'] and earliest['home_odds']:
                home_change = latest['home_odds'] - earliest['home_odds']
                draw_change = latest['draw_odds'] - earliest['draw_odds']
                away_change = latest['away_odds'] - earliest['away_odds']
                print(f"   赔率变化: {home_change:+.3f} / {draw_change:+.3f} / {away_change:+.3f}")

def export_target_companies_report(match_id, target_companies_history):
    """导出目标公司历史赔率报告"""
    
    if not target_companies_history:
        print("❌ 没有数据需要导出")
        return
    
    try:
        # 导出详细报告
        report_file = f"target_companies_odds_history_{match_id}.txt"
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(f"目标公司历史赔率报告\n")
            f.write(f"比赛ID: {match_id}\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"目标公司数: {len(target_companies_history)}\n")
            f.write(f"总历史记录数: {sum(data['history_count'] for data in target_companies_history.values())}\n\n")
            
            f.write("="*80 + "\n")
            f.write("目标公司完整历史赔率数据\n")
            f.write("="*80 + "\n")
            
            for target_name, company_data in target_companies_history.items():
                f.write(f"\n【{target_name}】\n")
                f.write(f"公司名称: {company_data['company_name']}\n")
                f.write(f"公司ID: {company_data['company_id']}\n")
                f.write(f"记录ID: {company_data['record_id']}\n")
                f.write(f"历史记录数: {company_data['history_count']}\n")
                f.write(f"\n完整历史赔率变化:\n")
                f.write(f"{'序号':<4} {'时间':<12} {'主胜':<6} {'平局':<6} {'客胜':<6} {'Kelly主':<6} {'Kelly平':<6} {'Kelly客':<6}\n")
                f.write("-" * 70 + "\n")
                
                for i, entry in enumerate(company_data['odds_history'], 1):
                    f.write(f"{i:<4} {entry['update_time']:<12} {entry['home_odds']:<6.2f} {entry['draw_odds']:<6.2f} {entry['away_odds']:<6.2f} ")
                    f.write(f"{entry['kelly_home']:<6.2f} {entry['kelly_draw']:<6.2f} {entry['kelly_away']:<6.2f}\n")
                
                f.write("\n" + "-" * 80 + "\n")
        
        print(f"✅ 详细报告已保存到: {report_file}")
        
        # 同时保存JSON格式
        json_file = f"target_companies_odds_history_{match_id}.json"
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump({
                'match_id': match_id,
                'target_companies_history': target_companies_history,
                'summary': {
                    'total_companies': len(target_companies_history),
                    'total_records': sum(data['history_count'] for data in target_companies_history.values())
                }
            }, f, ensure_ascii=False, indent=2)
        
        print(f"✅ JSON数据已保存到: {json_file}")
        
    except Exception as e:
        print(f"❌ 导出报告失败: {e}")

def main():
    """主函数"""
    
    print("=" * 60)
    print("🎯 目标公司历史赔率解析器")
    print("=" * 60)
    
    # 显示配置的目标公司
    print(f"📋 配置的目标公司: {', '.join(TARGET_COMPANIES)}")
    
    # 测试比赛ID
    test_match_id = "2696009"
    
    print(f"\n🔍 分析比赛ID: {test_match_id}")
    
    # 1. 获取JS内容
    print("\n1️⃣ 获取JS数据...")
    js_content = get_match_js_content(test_match_id)
    if not js_content:
        print("❌ 无法获取JS内容")
        return
    
    # 2. 解析目标公司历史数据
    print("\n2️⃣ 解析目标公司历史赔率...")
    target_companies_history = parse_target_companies_history(js_content, TARGET_COMPANIES)
    if not target_companies_history:
        print("❌ 未找到任何目标公司的历史数据")
        return
    
    # 3. 显示摘要
    print("\n3️⃣ 显示解析结果...")
    display_target_companies_summary(target_companies_history)
    
    # 4. 导出报告
    print(f"\n4️⃣ 导出报告...")
    export_target_companies_report(test_match_id, target_companies_history)
    
    print(f"\n🎉 目标公司历史赔率解析完成！")

if __name__ == "__main__":
    main() 