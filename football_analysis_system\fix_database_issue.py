#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断和修复数据库连接问题
"""

import os
import sqlite3
import time
import threading
from datetime import datetime

def diagnose_database_issue():
    """诊断数据库问题"""
    
    print("=" * 60)
    print("🔍 数据库问题诊断")
    print("=" * 60)
    
    # 1. 检查数据库文件路径
    db_paths = [
        "data/matches_and_odds.db",
        "./data/matches_and_odds.db",
        os.path.abspath("data/matches_and_odds.db"),
        os.path.join(os.getcwd(), "data", "matches_and_odds.db")
    ]
    
    print("=== 检查数据库文件路径 ===")
    valid_paths = []
    for path in db_paths:
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"✅ 找到: {path} (大小: {size:,} 字节)")
            valid_paths.append(path)
        else:
            print(f"❌ 不存在: {path}")
    
    if not valid_paths:
        print("❌ 未找到任何有效的数据库文件")
        return None
    
    # 使用第一个有效路径
    db_path = valid_paths[0]
    print(f"\n使用数据库路径: {db_path}")
    
    # 2. 检查文件权限
    print(f"\n=== 检查文件权限 ===")
    try:
        # 检查读权限
        with open(db_path, 'rb') as f:
            f.read(1)
        print("✅ 文件可读")
        
        # 检查写权限
        with open(db_path, 'ab') as f:
            pass
        print("✅ 文件可写")
        
    except Exception as e:
        print(f"❌ 文件权限问题: {e}")
        return None
    
    # 3. 测试数据库连接
    print(f"\n=== 测试数据库连接 ===")
    try:
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 测试基本查询
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"✅ 连接成功，找到 {len(tables)} 个表")
        
        # 检查记录ID表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='odds_record_ids';")
        record_table = cursor.fetchone()
        if record_table:
            print("✅ odds_record_ids 表存在")
            
            # 测试插入操作
            test_data = (
                'test_match',
                'test_company_id', 
                'test_company_name',
                'test_record_id',
                'test_timestamp',
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
            
            cursor.execute('''
                INSERT OR REPLACE INTO odds_record_ids 
                (match_id, company_id, company_name, record_id, update_timestamp, scrape_time)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', test_data)
            
            conn.commit()
            print("✅ 测试插入成功")
            
            # 删除测试数据
            cursor.execute("DELETE FROM odds_record_ids WHERE match_id='test_match'")
            conn.commit()
            print("✅ 测试删除成功")
            
        else:
            print("❌ odds_record_ids 表不存在")
        
        conn.close()
        return db_path
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def create_robust_database_manager():
    """创建健壮的数据库管理器"""
    
    class RobustDatabaseManager:
        def __init__(self, db_path, max_retries=3, timeout=30.0):
            self.db_path = os.path.abspath(db_path)
            self.max_retries = max_retries
            self.timeout = timeout
            self._lock = threading.Lock()
            
            # 确保目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            # 初始化数据库
            self._initialize_database()
        
        def _initialize_database(self):
            """初始化数据库和表"""
            try:
                with self._lock:
                    conn = sqlite3.connect(self.db_path, timeout=self.timeout)
                    cursor = conn.cursor()
                    
                    # 启用WAL模式以支持并发访问
                    cursor.execute("PRAGMA journal_mode=WAL;")
                    cursor.execute("PRAGMA synchronous=NORMAL;")
                    cursor.execute("PRAGMA cache_size=10000;")
                    cursor.execute("PRAGMA temp_store=MEMORY;")
                    
                    # 创建记录ID表
                    cursor.execute('''
                        CREATE TABLE IF NOT EXISTS odds_record_ids (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            match_id TEXT NOT NULL,
                            company_id TEXT NOT NULL,
                            company_name TEXT NOT NULL,
                            record_id TEXT NOT NULL,
                            update_timestamp TEXT NOT NULL,
                            scrape_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            UNIQUE(match_id, company_id, record_id)
                        )
                    ''')
                    
                    # 创建索引
                    cursor.execute('''
                        CREATE INDEX IF NOT EXISTS idx_odds_record_ids_match_company 
                        ON odds_record_ids(match_id, company_id)
                    ''')
                    
                    conn.commit()
                    conn.close()
                    
                    print(f"✅ 数据库初始化成功: {self.db_path}")
                    
            except Exception as e:
                print(f"❌ 数据库初始化失败: {e}")
                raise
        
        def execute_with_retry(self, operation_func, *args, **kwargs):
            """带重试机制的数据库操作"""
            for attempt in range(self.max_retries):
                try:
                    with self._lock:
                        conn = sqlite3.connect(self.db_path, timeout=self.timeout)
                        try:
                            result = operation_func(conn, *args, **kwargs)
                            conn.commit()
                            return result
                        finally:
                            conn.close()
                            
                except sqlite3.OperationalError as e:
                    if "database is locked" in str(e) or "unable to open" in str(e):
                        if attempt < self.max_retries - 1:
                            wait_time = (attempt + 1) * 0.5
                            print(f"数据库锁定，等待 {wait_time} 秒后重试... (尝试 {attempt + 1}/{self.max_retries})")
                            time.sleep(wait_time)
                            continue
                    raise
                except Exception as e:
                    if attempt < self.max_retries - 1:
                        wait_time = (attempt + 1) * 0.5
                        print(f"数据库操作失败，等待 {wait_time} 秒后重试: {e}")
                        time.sleep(wait_time)
                        continue
                    raise
            
            raise Exception(f"数据库操作在 {self.max_retries} 次尝试后仍然失败")
        
        def save_record_ids(self, match_id, record_ids_data):
            """保存记录ID到数据库"""
            if not record_ids_data:
                return
            
            def _save_operation(conn, match_id, record_ids_data):
                cursor = conn.cursor()
                saved_count = 0
                
                for company_name, data in record_ids_data.items():
                    cursor.execute('''
                        INSERT OR REPLACE INTO odds_record_ids 
                        (match_id, company_id, company_name, record_id, update_timestamp, scrape_time)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        str(match_id),
                        data['company_id'],
                        data['company_name'],
                        data['record_id'],
                        data['update_timestamp'],
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ))
                    saved_count += 1
                
                return saved_count
            
            try:
                saved_count = self.execute_with_retry(_save_operation, match_id, record_ids_data)
                print(f"✅ 比赛 {match_id} 的记录ID保存完成: {saved_count} 条记录")
                return True
                
            except Exception as e:
                print(f"❌ 保存记录ID失败: {e}")
                return False
    
    return RobustDatabaseManager

def test_robust_manager():
    """测试健壮的数据库管理器"""
    
    print("\n" + "=" * 60)
    print("🧪 测试健壮的数据库管理器")
    print("=" * 60)
    
    try:
        # 创建管理器
        RobustDatabaseManager = create_robust_database_manager()
        db_manager = RobustDatabaseManager("data/matches_and_odds.db")
        
        # 测试数据
        test_record_ids = {
            '威廉希尔': {
                'company_id': '115',
                'company_name': 'William Hill',
                'record_id': '*********',
                'update_timestamp': '2025,08-1,03,05,59,00'
            },
            '易胜博': {
                'company_id': '90',
                'company_name': 'Easybets',
                'record_id': '*********',
                'update_timestamp': '2025,08-1,03,05,56,00'
            }
        }
        
        # 测试保存
        success = db_manager.save_record_ids("test_match_robust", test_record_ids)
        
        if success:
            print("✅ 健壮数据库管理器测试成功")
            return db_manager
        else:
            print("❌ 健壮数据库管理器测试失败")
            return None
            
    except Exception as e:
        print(f"❌ 创建健壮数据库管理器失败: {e}")
        return None

def main():
    """主函数"""
    
    # 1. 诊断问题
    db_path = diagnose_database_issue()
    
    if not db_path:
        print("\n❌ 数据库诊断失败，无法继续")
        return
    
    # 2. 测试健壮的管理器
    db_manager = test_robust_manager()
    
    if db_manager:
        print(f"\n✅ 数据库问题已修复！")
        print(f"使用以下代码替换原有的数据库操作：")
        print(f"""
# 在您的爬虫类中使用健壮的数据库管理器
class EnhancedOddsScraper:
    def __init__(self, ...):
        # ... 其他初始化代码 ...
        from fix_database_issue import create_robust_database_manager
        RobustDatabaseManager = create_robust_database_manager()
        self.db_manager = RobustDatabaseManager("data/matches_and_odds.db")
    
    def _save_record_ids_to_db(self, match_id, record_ids_data):
        return self.db_manager.save_record_ids(match_id, record_ids_data)
""")
    else:
        print(f"\n❌ 无法创建健壮的数据库管理器")

if __name__ == "__main__":
    main() 