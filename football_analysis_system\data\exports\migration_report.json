{"migration_date": "2025-06-29 22:12:51", "source_directory": "/mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data", "databases_analyzed": {"matches_and_odds.db": {"config": {"priority": 1, "description": "比赛和赔率数据", "tables": ["matches", "odds", "match_predictions", "overunder_odds", "overunder_crawl_status"]}, "analysis": {"file_path": "/mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/matches_and_odds.db", "file_size": 495616, "tables": {"matches": {"columns": [[0, "match_id", "TEXT", 0, null, 1], [1, "league_name", "TEXT", 0, null, 0], [2, "home_team", "TEXT", 0, null, 0], [3, "away_team", "TEXT", 0, null, 0], [4, "start_time", "TEXT", 0, null, 0], [5, "scrape_time", "TEXT", 0, null, 0], [6, "is_beidan", "INTEGER", 0, "0", 0]], "record_count": 44, "create_sql": "CREATE TABLE matches (\n        match_id TEXT PRIMARY KEY,\n        league_name TEXT,\n        home_team TEXT,\n        away_team TEXT,\n        start_time TEXT,\n        scrape_time TEXT\n    , is_beidan INTEGER DEFAULT 0)"}, "odds": {"columns": [[0, "id", "INTEGER", 0, null, 1], [1, "match_id", "TEXT", 0, null, 0], [2, "bookmaker", "TEXT", 0, null, 0], [3, "initial_home_win", "REAL", 0, null, 0], [4, "initial_draw", "REAL", 0, null, 0], [5, "initial_away_win", "REAL", 0, null, 0], [6, "instant_home_win", "REAL", 0, null, 0], [7, "instant_draw", "REAL", 0, null, 0], [8, "instant_away_win", "REAL", 0, null, 0], [9, "payout_rate", "REAL", 0, null, 0], [10, "scrape_time", "TEXT", 0, null, 0], [11, "initial_payout_rate", "REAL", 0, null, 0], [12, "earliest_open_time", "TEXT", 0, null, 0]], "record_count": 313, "create_sql": "CREATE TABLE odds (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        match_id TEXT,\n        bookmaker TEXT,\n        initial_home_win REAL,\n        initial_draw REAL,\n        initial_away_win REAL,\n        instant_home_win REAL,\n        instant_draw REAL,\n        instant_away_win REAL,\n        payout_rate REAL,\n        scrape_time TEXT, initial_payout_rate REAL, earliest_open_time TEXT,\n        FOREIGN KEY (match_id) REFERENCES matches (match_id)\n    )"}, "match_predictions": {"columns": [[0, "id", "INTEGER", 0, null, 1], [1, "match_id", "TEXT", 0, null, 0], [2, "league_name", "TEXT", 0, null, 0], [3, "home_team", "TEXT", 0, null, 0], [4, "away_team", "TEXT", 0, null, 0], [5, "match_time", "TEXT", 0, null, 0], [6, "bookmaker", "TEXT", 0, null, 0], [7, "odds_type", "TEXT", 0, null, 0], [8, "team_type", "TEXT", 0, null, 0], [9, "gap_diff", "REAL", 0, null, 0], [10, "interval_type", "TEXT", 0, null, 0], [11, "rule_value", "TEXT", 0, null, 0], [12, "rule_diff", "TEXT", 0, null, 0], [13, "first_choice", "TEXT", 0, null, 0], [14, "standard_draw", "TEXT", 0, null, 0], [15, "actual_draw", "TEXT", 0, null, 0], [16, "draw_interval", "TEXT", 0, null, 0], [17, "prediction_time", "TEXT", 0, null, 0]], "record_count": 1, "create_sql": "CREATE TABLE match_predictions (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                match_id TEXT,\n                league_name TEXT,\n                home_team TEXT,\n                away_team TEXT,\n                match_time TEXT,\n                bookmaker TEXT,\n                odds_type TEXT,\n                team_type TEXT,\n                gap_diff REAL,\n                interval_type TEXT,\n                rule_value TEXT,\n                rule_diff TEXT,\n                first_choice TEXT,\n                standard_draw TEXT,\n                actual_draw TEXT,\n                draw_interval TEXT,\n                prediction_time TEXT\n            )"}, "overunder_odds": {"columns": [[0, "id", "INTEGER", 0, null, 1], [1, "match_id", "INTEGER", 1, null, 0], [2, "matchid", "TEXT", 0, null, 0], [3, "company_id", "TEXT", 0, null, 0], [4, "company_name", "TEXT", 1, null, 0], [5, "initial_over", "REAL", 0, null, 0], [6, "initial_line", "TEXT", 0, null, 0], [7, "initial_under", "REAL", 0, null, 0], [8, "instant_over", "REAL", 0, null, 0], [9, "instant_line", "TEXT", 0, null, 0], [10, "instant_under", "REAL", 0, null, 0], [11, "line_numeric", "REAL", 0, null, 0], [12, "update_time", "TEXT", 1, null, 0], [13, "crawl_time", "TIMESTAMP", 0, "CURRENT_TIMESTAMP", 0]], "record_count": 1052, "create_sql": "CREATE TABLE overunder_odds (\n                    id INTEGER PRIMARY KEY AUTOINCREMENT,      /* 自增ID */\n                    match_id INTEGER NOT NULL,                 /* 比赛ID */\n                    matchid TEXT,                              /* 比赛ID(字符串格式) */\n                    company_id TEXT,                           /* 博彩公司ID */\n                    company_name TEXT NOT NULL,                /* 博彩公司名称 */\n                    initial_over REAL,                         /* 初始大球赔率 */\n                    initial_line TEXT,                         /* 初始盘口（字符串） */\n                    initial_under REAL,                        /* 初始小球赔率 */\n                    instant_over REAL,                         /* 即时大球赔率 */\n                    instant_line TEXT,                         /* 即时盘口（字符串） */\n                    instant_under REAL,                        /* 即时小球赔率 */\n                    line_numeric REAL,                         /* 盘口数值 */\n                    update_time TEXT NOT NULL,                 /* 更新时间 */\n                    crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, /* 爬取时间 */\n                    UNIQUE (match_id, company_id, update_time) /* 防止重复数据 */\n                )"}, "overunder_crawl_status": {"columns": [[0, "id", "INTEGER", 0, null, 1], [1, "match_id", "INTEGER", 1, null, 0], [2, "matchid", "TEXT", 0, null, 0], [3, "status", "TEXT", 0, "'pending'", 0], [4, "retry_count", "INTEGER", 0, "0", 0], [5, "last_attempt_time", "TEXT", 0, null, 0], [6, "success_time", "TEXT", 0, null, 0], [7, "error_message", "TEXT", 0, null, 0], [8, "odds_count", "INTEGER", 0, "0", 0], [9, "timestamp", "TIMESTAMP", 0, "CURRENT_TIMESTAMP", 0]], "record_count": 2, "create_sql": "CREATE TABLE overunder_crawl_status (\n                    id INTEGER PRIMARY KEY AUTOINCREMENT,\n                    match_id INTEGER UNIQUE NOT NULL,          /* 比赛ID */\n                    matchid TEXT,                              /* 比赛ID(字符串格式) */\n                    status TEXT DEFAULT 'pending',             /* 爬取状态：pending, success, failed, skipped */\n                    retry_count INTEGER DEFAULT 0,             /* 重试次数 */\n                    last_attempt_time TEXT,                    /* 最后尝试时间 */\n                    success_time TEXT,                         /* 成功时间 */\n                    error_message TEXT,                        /* 错误信息 */\n                    odds_count INTEGER DEFAULT 0,             /* 获取到的赔率数量 */\n                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n                )"}}, "total_records": 1412}}, "guangyishili.db": {"config": {"priority": 1, "description": "广义实力数据", "tables": ["team_power_ratings", "match_predictions"]}, "analysis": {"file_path": "/mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/guangyishili.db", "file_size": 1581056, "tables": {"team_power_ratings": {"columns": [[0, "id", "INTEGER", 0, null, 1], [1, "company_id", "TEXT", 0, null, 0], [2, "company_name", "TEXT", 0, null, 0], [3, "league_id", "INTEGER", 0, null, 0], [4, "league_name", "TEXT", 0, null, 0], [5, "team_id", "INTEGER", 0, null, 0], [6, "team_name", "TEXT", 0, null, 0], [7, "total_power", "REAL", 0, null, 0], [8, "recent_power", "REAL", 0, null, 0], [9, "combined_power", "REAL", 0, null, 0], [10, "relative_power", "REAL", 0, null, 0], [11, "power_level", "REAL", 0, null, 0], [12, "power_category", "TEXT", 0, null, 0], [13, "rank", "INTEGER", 0, null, 0], [14, "update_time", "TIMESTAMP", 0, "CURRENT_TIMESTAMP", 0]], "record_count": 10318, "create_sql": "CREATE TABLE team_power_ratings (\n            id INTEGER PRIMARY KEY,\n            company_id TEXT,          -- 博彩公司ID\n            company_name TEXT,        -- 博彩公司名称\n            league_id INTEGER,        -- 联赛ID\n            league_name TEXT,         -- 联赛名称\n            team_id INTEGER,          -- 球队ID\n            team_name TEXT,           -- 球队名称\n            total_power REAL,         -- 初赔总场广义实力\n            recent_power REAL,        -- 近况广义实力\n            combined_power REAL,      -- 综合广义实力\n            relative_power REAL,      -- 相对广义实力\n            power_level REAL,         -- 档距\n            power_category TEXT,      -- 档次分类\n            rank INTEGER,             -- 联赛内排名\n            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP  -- 更新时间\n        )"}, "match_predictions": {"columns": [[0, "id", "INTEGER", 0, null, 1], [1, "match_id", "TEXT", 0, null, 0], [2, "league_name", "TEXT", 0, null, 0], [3, "home_team", "TEXT", 0, null, 0], [4, "away_team", "TEXT", 0, null, 0], [5, "match_time", "TEXT", 0, null, 0], [6, "bookmaker", "TEXT", 0, null, 0], [7, "odds_type", "TEXT", 0, null, 0], [8, "team_type", "TEXT", 0, null, 0], [9, "gap_diff", "REAL", 0, null, 0], [10, "interval_type", "TEXT", 0, null, 0], [11, "rule_value", "TEXT", 0, null, 0], [12, "rule_diff", "TEXT", 0, null, 0], [13, "first_choice", "TEXT", 0, null, 0], [14, "standard_draw", "TEXT", 0, null, 0], [15, "actual_draw", "TEXT", 0, null, 0], [16, "draw_interval", "TEXT", 0, null, 0], [17, "prediction_time", "TEXT", 0, null, 0]], "record_count": 0, "create_sql": "CREATE TABLE match_predictions (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                match_id TEXT,\n                league_name TEXT,\n                home_team TEXT,\n                away_team TEXT,\n                match_time TEXT,\n                bookmaker TEXT,\n                odds_type TEXT,\n                team_type TEXT,\n                gap_diff REAL,\n                interval_type TEXT,\n                rule_value TEXT,\n                rule_diff TEXT,\n                first_choice TEXT,\n                standard_draw TEXT,\n                actual_draw TEXT,\n                draw_interval TEXT,\n                prediction_time TEXT\n            )"}}, "total_records": 10318}}, "football.db": {"config": {"priority": 2, "description": "历史比赛数据", "tables": ["matches", "standings", "odds_companies", "euro_odds", "crawl_status"]}, "analysis": {"file_path": "/mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/football.db", "file_size": 49147904, "tables": {"matches": {"columns": [[0, "match_id", "INTEGER", 0, null, 2], [1, "league_id", "INTEGER", 1, null, 1], [2, "league_name", "TEXT", 1, null, 0], [3, "round_number", "INTEGER", 1, null, 0], [4, "match_status", "INTEGER", 1, null, 0], [5, "match_time", "TEXT", 1, null, 0], [6, "home_team_id", "INTEGER", 1, null, 0], [7, "home_team_name", "TEXT", 1, null, 0], [8, "away_team_id", "INTEGER", 1, null, 0], [9, "away_team_name", "TEXT", 1, null, 0], [10, "home_score", "INTEGER", 0, null, 0], [11, "away_score", "INTEGER", 0, null, 0], [12, "home_half_score", "INTEGER", 0, null, 0], [13, "away_half_score", "INTEGER", 0, null, 0], [14, "home_rank", "TEXT", 0, null, 0], [15, "away_rank", "TEXT", 0, null, 0], [16, "handicap", "REAL", 0, null, 0], [17, "over_under", "TEXT", 0, null, 0], [18, "is_included_in_stat", "INTEGER", 0, "1", 0], [19, "matchid", "TEXT", 0, null, 0], [20, "odds_updated", "INTEGER", 0, "0", 0], [21, "update_time", "TIMESTAMP", 0, "CURRENT_TIMESTAMP", 0]], "record_count": 16907, "create_sql": "CREATE TABLE matches (\n                    match_id INTEGER,                    /* 比赛ID */\n                    league_id INTEGER NOT NULL,          /* 联赛ID */\n                    league_name TEXT NOT NULL,           /* 联赛中文名称 */\n                    round_number INTEGER NOT NULL,       /* 轮次 */\n                    match_status INTEGER NOT NULL,       /* 比赛状态：-1=已完成比赛，0=未开始，1=上半场，2=中场，3=下半场，4=完场，5=取消，6=延期，7=腰斩 */\n                    match_time TEXT NOT NULL,            /* 比赛时间 */\n                    home_team_id INTEGER NOT NULL,       /* 主队ID */\n                    home_team_name TEXT NOT NULL,        /* 主队名称 */\n                    away_team_id INTEGER NOT NULL,       /* 客队ID */\n                    away_team_name TEXT NOT NULL,        /* 客队名称 */\n                    home_score INTEGER,                  /* 主队得分 */\n                    away_score INTEGER,                  /* 客队得分 */\n                    home_half_score INTEGER,             /* 主队半场得分 */\n                    away_half_score INTEGER,             /* 客队半场得分 */\n                    home_rank TEXT,                      /* 主队排名 */\n                    away_rank TEXT,                      /* 客队排名 */\n                    handicap REAL,                       /* 让球盘口 */\n                    over_under TEXT,                     /* 大小球盘口 */\n                    is_included_in_stat INTEGER DEFAULT 1, /* 是否计入统计：1=是，0=否 */\n                    matchid TEXT,                        /* 比赛ID(字符串格式) */\n                    odds_updated INTEGER DEFAULT 0,      /* 是否已更新赔率：0=未更新，1=已更新 */\n                    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, /* 更新时间 */\n                    PRIMARY KEY (league_id, match_id)    /* 联赛ID和比赛ID共同组成主键 */\n                )"}, "standings": {"columns": [[0, "id", "INTEGER", 0, null, 1], [1, "league_id", "INTEGER", 1, null, 0], [2, "league_name", "TEXT", 1, null, 0], [3, "team_id", "INTEGER", 1, null, 0], [4, "team_name", "TEXT", 1, null, 0], [5, "standing_type", "TEXT", 1, null, 0], [6, "rank_no", "INTEGER", 1, null, 0], [7, "matches_played", "INTEGER", 1, null, 0], [8, "wins", "INTEGER", 1, null, 0], [9, "draws", "INTEGER", 1, null, 0], [10, "losses", "INTEGER", 1, null, 0], [11, "goals_for", "INTEGER", 1, null, 0], [12, "goals_against", "INTEGER", 1, null, 0], [13, "goal_difference", "INTEGER", 1, null, 0], [14, "win_rate", "REAL", 0, null, 0], [15, "draw_rate", "REAL", 0, null, 0], [16, "loss_rate", "REAL", 0, null, 0], [17, "goals_for_avg", "REAL", 0, null, 0], [18, "goals_against_avg", "REAL", 0, null, 0], [19, "points", "INTEGER", 1, null, 0], [20, "update_time", "TEXT", 1, null, 0]], "record_count": 4272, "create_sql": "CREATE TABLE standings (\n                    id INTEGER PRIMARY KEY AUTOINCREMENT,  /* 自增ID */\n                    league_id INTEGER NOT NULL,            /* 联赛ID */\n                    league_name TEXT NOT NULL,             /* 联赛中文名称 */\n                    team_id INTEGER NOT NULL,              /* 球队ID */\n                    team_name TEXT NOT NULL,               /* 球队名称 */\n                    standing_type TEXT NOT NULL,           /* 积分类型：total=总积分, home=主场积分, away=客场积分 */\n                    rank_no INTEGER NOT NULL,              /* 排名 */\n                    matches_played INTEGER NOT NULL,       /* 已赛场次 */\n                    wins INTEGER NOT NULL,                 /* 胜场数 */\n                    draws INTEGER NOT NULL,                /* 平场数 */\n                    losses INTEGER NOT NULL,               /* 负场数 */\n                    goals_for INTEGER NOT NULL,            /* 进球数 */\n                    goals_against INTEGER NOT NULL,        /* 失球数 */\n                    goal_difference INTEGER NOT NULL,      /* 净胜球 */\n                    win_rate REAL,                        /* 胜率（百分比） */\n                    draw_rate REAL,                       /* 平率（百分比） */\n                    loss_rate REAL,                       /* 负率（百分比） */\n                    goals_for_avg REAL,                   /* 场均进球 */\n                    goals_against_avg REAL,               /* 场均失球 */\n                    points INTEGER NOT NULL,               /* 积分 */\n                    update_time TEXT NOT NULL,            /* 更新时间 */\n                    UNIQUE (league_id, team_id, standing_type)  /* 确保每支球队在每个联赛的每个积分类型中只有一条记录 */\n                )"}, "odds_companies": {"columns": [[0, "company_id", "INTEGER", 0, null, 1], [1, "company_name", "TEXT", 0, null, 0], [2, "company_name_cn", "TEXT", 0, null, 0]], "record_count": 12, "create_sql": "CREATE TABLE odds_companies (\n                    company_id INTEGER PRIMARY KEY,\n                    company_name TEXT,\n                    company_name_cn TEXT\n                )"}, "euro_odds": {"columns": [[0, "id", "INTEGER", 0, null, 1], [1, "match_id", "INTEGER", 0, null, 0], [2, "matchid", "TEXT", 0, null, 0], [3, "company_id", "INTEGER", 0, null, 0], [4, "company_odds_id", "TEXT", 0, null, 0], [5, "init_home_win", "REAL", 0, null, 0], [6, "init_draw", "REAL", 0, null, 0], [7, "init_away_win", "REAL", 0, null, 0], [8, "init_home_win_rate", "REAL", 0, null, 0], [9, "init_draw_rate", "REAL", 0, null, 0], [10, "init_away_win_rate", "REAL", 0, null, 0], [11, "init_return_rate", "REAL", 0, null, 0], [12, "real_home_win", "REAL", 0, null, 0], [13, "real_draw", "REAL", 0, null, 0], [14, "real_away_win", "REAL", 0, null, 0], [15, "real_home_win_rate", "REAL", 0, null, 0], [16, "real_draw_rate", "REAL", 0, null, 0], [17, "real_away_win_rate", "REAL", 0, null, 0], [18, "real_return_rate", "REAL", 0, null, 0], [19, "k_home", "REAL", 0, null, 0], [20, "k_draw", "REAL", 0, null, 0], [21, "k_away", "REAL", 0, null, 0], [22, "update_time", "TEXT", 0, null, 0], [23, "crawl_time", "TIMESTAMP", 0, "CURRENT_TIMESTAMP", 0]], "record_count": 155304, "create_sql": "CREATE TABLE euro_odds (\n                    id INTEGER PRIMARY KEY AUTOINCREMENT,\n                    match_id INTEGER,\n                    matchid TEXT,\n                    company_id INTEGER,\n                    company_odds_id TEXT,\n                    init_home_win REAL,\n                    init_draw REAL,\n                    init_away_win REAL,\n                    init_home_win_rate REAL,\n                    init_draw_rate REAL,\n                    init_away_win_rate REAL,\n                    init_return_rate REAL,\n                    real_home_win REAL,\n                    real_draw REAL,\n                    real_away_win REAL,\n                    real_home_win_rate REAL,\n                    real_draw_rate REAL,\n                    real_away_win_rate REAL,\n                    real_return_rate REAL,\n                    k_home REAL,\n                    k_draw REAL,\n                    k_away REAL,\n                    update_time TEXT,\n                    crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    FOREIGN KEY (match_id) REFERENCES matches(match_id)\n                )"}, "crawl_status": {"columns": [[0, "id", "INTEGER", 0, null, 1], [1, "matchid", "TEXT", 0, null, 0], [2, "status", "TEXT", 0, null, 0], [3, "retry_count", "INTEGER", 0, "0", 0], [4, "error_message", "TEXT", 0, null, 0], [5, "timestamp", "TIMESTAMP", 0, "CURRENT_TIMESTAMP", 0]], "record_count": 0, "create_sql": "CREATE TABLE crawl_status (\n                    id INTEGER PRIMARY KEY AUTOINCREMENT,\n                    matchid TEXT UNIQUE,\n                    status TEXT,\n                    retry_count INTEGER DEFAULT 0,\n                    error_message TEXT,\n                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n                )"}}, "total_records": 176495}}, "StandardOdds.db": {"config": {"priority": 3, "description": "满水返还赔率表", "tables": ["满水返还赔率表"]}, "analysis": {"file_path": "/mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/StandardOdds.db", "file_size": 45056, "tables": {"满水返还赔率表": {"columns": [[0, "间", "TEXT", 0, null, 0], [1, "胜", "REAL", 0, null, 0], [2, "平", "REAL", 0, null, 0], [3, "负", "REAL", 0, null, 0], [4, "距", "REAL", 0, null, 0], [5, "id", "INTEGER", 0, null, 0]], "record_count": 401, "create_sql": "CREATE TABLE \"满水返还赔率表\" (\n\"间\" TEXT,\n  \"胜\" REAL,\n  \"平\" REAL,\n  \"负\" REAL,\n  \"距\" REAL,\n  \"id\" INTEGER\n)"}}, "total_records": 401}}, "sports_database.db": {"config": {"priority": 3, "description": "实力对阵表", "tables": ["match_odds"]}, "analysis": {"file_path": "/mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/sports_database.db", "file_size": 61440, "tables": {"match_odds": {"columns": [[0, "Column1", "TEXT", 0, null, 0], [1, "Column2", "TEXT", 0, null, 0], [2, "Column3", "TEXT", 0, null, 0], [3, "Column4", "TEXT", 0, null, 0], [4, "Column5", "TEXT", 0, null, 0], [5, "Column6", "TEXT", 0, null, 0], [6, "Column7", "TEXT", 0, null, 0], [7, "Column8", "TEXT", 0, null, 0], [8, "Column9", "TEXT", 0, null, 0], [9, "Column10", "TEXT", 0, null, 0], [10, "Column11", "TEXT", 0, null, 0], [11, "Column12", "TEXT", 0, null, 0], [12, "Column13", "TEXT", 0, null, 0], [13, "Column14", "TEXT", 0, null, 0], [14, "Column15", "TEXT", 0, null, 0], [15, "Column16", "TEXT", 0, null, 0], [16, "Column17", "TEXT", 0, null, 0]], "record_count": 17, "create_sql": "CREATE TABLE \"match_odds\" (\n\"Column1\" TEXT,\n  \"Column2\" TEXT,\n  \"Column3\" TEXT,\n  \"Column4\" TEXT,\n  \"Column5\" TEXT,\n  \"Column6\" TEXT,\n  \"Column7\" TEXT,\n  \"Column8\" TEXT,\n  \"Column9\" TEXT,\n  \"Column10\" TEXT,\n  \"Column11\" TEXT,\n  \"Column12\" TEXT,\n  \"Column13\" TEXT,\n  \"Column14\" TEXT,\n  \"Column15\" TEXT,\n  \"Column16\" TEXT,\n  \"Column17\" TEXT\n)"}}, "total_records": 17}}, "probabilities.db": {"config": {"priority": 3, "description": "概率模型数据", "tables": ["match_probabilities"]}, "analysis": {"file_path": "/mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/probabilities.db", "file_size": 36864, "tables": {"match_probabilities": {"columns": [[0, "id", "INTEGER", 0, null, 1], [1, "home_strength", "TEXT", 1, null, 0], [2, "away_strength", "TEXT", 1, null, 0], [3, "win_probability", "REAL", 1, null, 0], [4, "draw_probability", "REAL", 1, null, 0], [5, "loss_probability", "REAL", 1, null, 0]], "record_count": 256, "create_sql": "CREATE TABLE match_probabilities (\n    id INTEGER PRIMARY KEY,\n    home_strength TEXT NOT NULL,  -- 直接使用原始名称如\"人强1\"\n    away_strength TEXT NOT NULL,  -- 直接使用原始名称如\"中游2\"\n    win_probability REAL NOT NULL,\n    draw_probability REAL NOT NULL,\n    loss_probability REAL NOT NULL,\n    CONSTRAINT unique_strength_combination UNIQUE (home_strength, away_strength),\n    CHECK (ABS(win_probability + draw_probability + loss_probability - 1.0) < 0.01)\n)"}}, "total_records": 256}}, "football_probabilities_new.db": {"config": {"priority": 4, "description": "新版概率模型数据", "tables": ["match_probabilities"]}, "analysis": {"file_path": "/mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/football_probabilities_new.db", "file_size": 36864, "tables": {"match_probabilities": {"columns": [[0, "id", "INTEGER", 0, null, 1], [1, "home_strength", "TEXT", 1, null, 0], [2, "away_strength", "TEXT", 1, null, 0], [3, "win_probability", "REAL", 1, null, 0], [4, "draw_probability", "REAL", 1, null, 0], [5, "loss_probability", "REAL", 1, null, 0]], "record_count": 256, "create_sql": "CREATE TABLE match_probabilities (\n    id INTEGER PRIMARY KEY,\n    home_strength TEXT NOT NULL,  -- 直接使用原始名称如\"人强1\"\n    away_strength TEXT NOT NULL,  -- 直接使用原始名称如\"中游2\"\n    win_probability REAL NOT NULL,\n    draw_probability REAL NOT NULL,\n    loss_probability REAL NOT NULL,\n    CONSTRAINT unique_strength_combination UNIQUE (home_strength, away_strength),\n    CHECK (ABS(win_probability + draw_probability + loss_probability - 1.0) < 0.01)\n)"}}, "total_records": 256}}}, "total_tables": 16, "total_records": 189155, "migration_log": ["成功导出: matches_and_odds.db -> /mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/exports/matches_and_odds_export.sql", "成功导出: guangyishili.db -> /mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/exports/guangyishili_export.sql", "成功导出: football.db -> /mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/exports/football_export.sql", "成功导出: StandardOdds.db -> /mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/exports/StandardOdds_export.sql", "成功导出: sports_database.db -> /mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/exports/sports_database_export.sql", "成功导出: probabilities.db -> /mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/exports/probabilities_export.sql", "成功导出: football_probabilities_new.db -> /mnt/i/football_analysis_system 0428/football_analysis_system/football_analysis_system/data/exports/football_probabilities_new_export.sql"]}