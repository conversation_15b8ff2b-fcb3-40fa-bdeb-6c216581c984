"""
泊松分布分析模块 - 核心计算逻辑
从possinon.py中提取的与UI无关的核心计算功能
"""

import pandas as pd
import numpy as np
from scipy.stats import poisson

def load_match_data(db_connection, league_name=None):
    """
    从数据库加载比赛数据
    
    Args:
        db_connection: 数据库连接对象
        league_name: 联赛名称过滤
        
    Returns:
        DataFrame: 比赛数据
    """
    try:
        # 首先尝试获取数据库中实际存在的表
        cursor = db_connection.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        if not tables:
            raise Exception("数据库中没有表")
            
        # 默认使用matches表，但如果不存在，尝试使用第一个表
        table_name = 'matches'
        if 'matches' not in tables:
            table_name = tables[0]
            print(f"警告：未找到matches表，使用替代表: {table_name}")
        
        # 验证表名安全性 - 只允许字母、数字和下划线
        import re
        if not re.match(r'^[a-zA-Z0-9_]+$', table_name):
            raise ValueError(f"表名包含非法字符: {table_name}")
        
        # 获取表的列信息 - 表名已验证安全
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns_info = cursor.fetchall()
        column_names = [col[1] for col in columns_info]
        
        # 构建安全的参数化查询
        if league_name and 'league_name' in column_names:
            # 使用参数化查询防止SQL注入
            query = f"SELECT * FROM {table_name} WHERE league_name = ?"
            df = pd.read_sql_query(query, db_connection, params=[league_name])
        else:
            # 无过滤条件的查询
            query = f"SELECT * FROM {table_name}"
            df = pd.read_sql_query(query, db_connection)
        
        # 如果DataFrame为空，返回空DataFrame
        if df.empty:
            return df
            
        # 映射列名以符合后续处理需求
        column_mapping = {}
        
        # 自动映射已知的列名
        required_columns = {
            'match_id': ['match_id', 'id', 'match_id'],
            'league_name': ['league_name', 'league', 'competition'],
            'match_date': ['match_time', 'date', 'match_date'],
            'HomeTeam': ['home_team_name', 'home_team', 'hometeam'],
            'AwayTeam': ['away_team_name', 'away_team', 'awayteam'],
            'FTHG': ['home_score', 'home_goals', 'fthg'],
            'FTAG': ['away_score', 'away_goals', 'ftag'],
            'HTHG': ['home_half_score', 'ht_home_goals', 'hthg'],
            'HTAG': ['away_half_score', 'ht_away_goals', 'htag']
        }
        
        for target, possible_names in required_columns.items():
            for name in possible_names:
                if name in df.columns:
                    column_mapping[name] = target
                    break
        
        # 重命名列
        if column_mapping:
            df = df.rename(columns=column_mapping)
            
        # 确保核心列存在（即使为空值）
        for col in ['FTHG', 'FTAG', 'HomeTeam', 'AwayTeam']:
            if col not in df.columns:
                df[col] = None
        
        return df
    except Exception as e:
        print(f"加载比赛数据时出错: {str(e)}")
        # 返回空DataFrame
        return pd.DataFrame()

def get_available_leagues(db_connection):
    """
    获取可用的联赛列表
    
    Args:
        db_connection: 数据库连接对象
        
    Returns:
        list: 联赛名称列表
    """
    try:
        cursor = db_connection.cursor()
        
        # 获取数据库中实际存在的表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        if not tables:
            return []
            
        # 默认使用matches表，但如果不存在，尝试使用第一个表
        table_name = 'matches'
        if 'matches' not in tables:
            table_name = tables[0]
        
        # 获取表的列信息
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns_info = cursor.fetchall()
        column_names = [col[1] for col in columns_info]
        
        # 查找可能的联赛名称列
        league_column = None
        for possible_name in ['league_name', 'league', 'competition']:
            if possible_name in column_names:
                league_column = possible_name
                break
        
        if not league_column:
            print("警告：找不到联赛名称列")
            return []
        
        # 获取唯一的联赛名称
        cursor.execute(f"SELECT DISTINCT {league_column} FROM {table_name}")
        leagues = [row[0] for row in cursor.fetchall() if row[0]]
        return leagues
    except Exception as e:
        print(f"获取联赛列表时出错: {str(e)}")
        return []

def calculate_league_averages(match_data):
    """
    计算联赛平均值
    
    Args:
        match_data: 比赛数据DataFrame
        
    Returns:
        tuple: (主场平均进球数, 客场平均进球数)
    """
    league_avg_home = match_data['FTHG'].mean()
    league_avg_away = match_data['FTAG'].mean()
    return league_avg_home, league_avg_away

def calculate_team_stats(match_data, teams, recent_matches=10):
    """
    计算球队统计数据
    
    Args:
        match_data: 比赛数据DataFrame
        teams: 球队列表
        recent_matches: 最近比赛数量
        
    Returns:
        dict: 球队统计数据
    """
    df = match_data
    stats = {}
    league_avg_home, league_avg_away = calculate_league_averages(match_data)
    
    for team in teams:
        # 获取该队所有比赛（主场和客场）
        home_games = df[df['HomeTeam'] == team].copy()
        away_games = df[df['AwayTeam'] == team].copy()
        
        # 为每场比赛添加是否为主场标记
        home_games['is_home'] = True
        away_games['is_home'] = False
        
        # 合并所有比赛
        all_games = pd.concat([home_games, away_games])
        
        # 如果有日期字段，按日期排序
        if 'match_date' in df.columns:
            try:
                all_games['match_date'] = pd.to_datetime(all_games['match_date'])
                all_games = all_games.sort_values('match_date', ascending=False)
            except:
                pass
        
        # 只取最近N场比赛
        recent_games = all_games.head(recent_matches)
        
        # 分离回主场和客场比赛，用于计算
        recent_home = recent_games[recent_games['is_home'] == True]
        recent_away = recent_games[recent_games['is_home'] == False]
        
        # 基本统计
        home_goals = recent_home['FTHG'].sum() if not recent_home.empty else 0
        home_conceded = recent_home['FTAG'].sum() if not recent_home.empty else 0
        away_goals = recent_away['FTAG'].sum() if not recent_away.empty else 0
        away_conceded = recent_away['FTHG'].sum() if not recent_away.empty else 0
        
        # 计算攻防强度
        home_attack = home_goals / len(recent_home) / league_avg_home if len(recent_home) > 0 else 1
        home_defense = home_conceded / len(recent_home) / league_avg_away if len(recent_home) > 0 else 1
        away_attack = away_goals / len(recent_away) / league_avg_away if len(recent_away) > 0 else 1
        away_defense = away_conceded / len(recent_away) / league_avg_home if len(recent_away) > 0 else 1
        
        # 球队统计数据
        team_stats = {
            'home_games': len(recent_home),
            'away_games': len(recent_away),
            'home_goals': home_goals,
            'away_goals': away_goals,
            'home_conceded': home_conceded,
            'away_conceded': away_conceded,
            'home_attack': home_attack,
            'home_defense': home_defense,
            'away_attack': away_attack,
            'away_defense': away_defense,
            'total_games': len(recent_games)
        }
        
        # 添加半场进球统计（如果有）
        if 'HTHG' in df.columns and 'HTAG' in df.columns:
            ht_home_goals = recent_home['HTHG'].sum() if not recent_home.empty else 0
            ht_home_conceded = recent_home['HTAG'].sum() if not recent_home.empty else 0
            ht_away_goals = recent_away['HTAG'].sum() if not recent_away.empty else 0
            ht_away_conceded = recent_away['HTHG'].sum() if not recent_away.empty else 0
            
            team_stats.update({
                'ht_home_goals': ht_home_goals,
                'ht_home_conceded': ht_home_conceded,
                'ht_away_goals': ht_away_goals,
                'ht_away_conceded': ht_away_conceded,
                'avg_ht_home_goals': ht_home_goals / len(recent_home) if len(recent_home) > 0 else 0,
                'avg_ht_away_goals': ht_away_goals / len(recent_away) if len(recent_away) > 0 else 0
            })
        
        stats[team] = team_stats
    
    return stats

def calculate_poisson_probabilities(home_team, away_team, team_stats, league_avg_home, league_avg_away, max_goals=5):
    """
    计算泊松分布概率
    
    Args:
        home_team: 主队名称
        away_team: 客队名称
        team_stats: 球队统计数据
        league_avg_home: 联赛主场平均进球
        league_avg_away: 联赛客场平均进球
        max_goals: 最大进球数
        
    Returns:
        dict: 泊松分布结果
    """
    if home_team not in team_stats or away_team not in team_stats:
        return None
        
    home_stats = team_stats[home_team]
    away_stats = team_stats[away_team]
    
    # 计算预期进球
    home_exp_goals = home_stats['home_attack'] * away_stats['away_defense'] * league_avg_home
    away_exp_goals = away_stats['away_attack'] * home_stats['home_defense'] * league_avg_away
    
    # 计算概率矩阵
    prob_matrix = []
    
    for home_goals in range(max_goals + 1):
        row = []
        for away_goals in range(max_goals + 1):
            # 泊松概率公式: P(X=k) = (λ^k * e^-λ) / k!
            home_prob = poisson.pmf(home_goals, home_exp_goals)
            away_prob = poisson.pmf(away_goals, away_exp_goals)
            
            score_prob = home_prob * away_prob
            
            odds = 1/score_prob if score_prob > 0 else float('inf')
            
            row.append({
                'home_goals': home_goals,
                'away_goals': away_goals,
                'probability': score_prob,
                'percentage': score_prob * 100,
                'odds': odds
            })
        
        prob_matrix.append(row)
    
    # 扁平化并按概率排序
    flat_probs = []
    for row in prob_matrix:
        flat_probs.extend(row)
    
    flat_probs.sort(key=lambda x: x['probability'], reverse=True)
    
    # 计算比赛结果概率
    outcomes = calculate_match_outcomes(flat_probs)
    
    return {
        'home_team': home_team,
        'away_team': away_team,
        'expected_goals': {
            'home': home_exp_goals,
            'away': away_exp_goals
        },
        'probabilities': flat_probs,
        'outcomes': outcomes,
        'most_likely': flat_probs[0],
        'team_stats': {
            'home': home_stats,
            'away': away_stats
        }
    }

def calculate_match_outcomes(probabilities):
    """
    计算比赛结果概率
    
    Args:
        probabilities: 概率列表
        
    Returns:
        dict: 主胜、平局、客胜概率
    """
    home_win = 0
    draw = 0
    away_win = 0
    
    for prob in probabilities:
        if prob['home_goals'] > prob['away_goals']:
            home_win += prob['probability']
        elif prob['home_goals'] == prob['away_goals']:
            draw += prob['probability']
        else:
            away_win += prob['probability']
    
    return {
        'home_win': home_win * 100,
        'draw': draw * 100,
        'away_win': away_win * 100
    }

def create_heatmap_data(probabilities, max_goals):
    """
    为热图创建数据
    
    Args:
        probabilities: 概率列表
        max_goals: 最大进球数
        
    Returns:
        numpy.ndarray: 热图数据矩阵
    """
    heatmap_data = np.zeros((max_goals + 1, max_goals + 1))
    
    for prob in probabilities:
        h = prob['home_goals']
        a = prob['away_goals']
        if h <= max_goals and a <= max_goals:
            heatmap_data[a, h] = prob['percentage']
    
    return heatmap_data

def create_total_goals_data(probabilities):
    """
    创建总进球分布数据
    
    Args:
        probabilities: 概率列表
        
    Returns:
        list: (进球数, 概率百分比) 元组列表
    """
    total_goals = {}
    for prob in probabilities:
        total = prob['home_goals'] + prob['away_goals']
        if total not in total_goals:
            total_goals[total] = 0
        total_goals[total] += prob['probability']
    
    # 按总进球数排序
    total_goals_data = sorted([(k, v * 100) for k, v in total_goals.items()])
    return total_goals_data 