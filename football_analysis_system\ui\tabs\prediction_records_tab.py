import tkinter as tk
from tkinter import ttk, messagebox
import logging
import sqlite3
from datetime import datetime
from football_analysis_system.config import COLOR_BG, COLOR_PRIMARY, FONT_SUBHEADER, DB_MATCHES
import os

class PredictionRecordsTab(tk.Frame):
    """预测记录展示标签页"""

    def __init__(self, parent, db_path=None):
        """
        初始化预测记录展示标签页

        Args:
            parent: 父容器
            db_path: 数据库路径
        """
        super().__init__(parent, bg=COLOR_BG)

        # 从配置获取数据库路径，确保与区间分析标签页使用相同的路径
        self.db_path = db_path or DB_MATCHES
        
        # 记录数据库路径用于调试
        logging.info(f"预测记录标签页使用数据库路径: {self.db_path}")
        
        # 检查数据库文件是否存在
        if not os.path.exists(self.db_path):
            logging.error(f"数据库文件不存在: {self.db_path}")
        else:
            logging.info(f"数据库文件存在: {self.db_path}")
            
            # 检查表是否存在
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='match_predictions'")
                if cursor.fetchone():
                    logging.info("match_predictions表存在")
                else:
                    logging.error("match_predictions表不存在，需要先在区间分析标签页中保存预测")
                conn.close()
            except Exception as e:
                logging.error(f"检查表是否存在时出错: {e}")
                
        self.filter_options = {
            "league": tk.StringVar(),
            "date_from": tk.StringVar(),
            "date_to": tk.StringVar()
        }
        self.current_records = []  # 当前显示的记录
        
        self.create_widgets()
        self.ensure_predictions_table()
        self.load_prediction_records()

    def create_widgets(self):
        """创建标签页控件"""
        # 容器框架
        main_container = ttk.Frame(self)
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # 顶部标题和过滤区域
        top_frame = ttk.Frame(main_container)
        top_frame.pack(fill=tk.X, padx=5, pady=5)

        # 标题
        title_label = ttk.Label(top_frame, text="预测记录查询",
                             font=FONT_SUBHEADER,
                             foreground=COLOR_PRIMARY,
                             background=COLOR_BG,
                             style="Subheader.TLabel")
        title_label.pack(side=tk.LEFT, padx=5)

        # 刷新按钮
        refresh_button = ttk.Button(top_frame, text="刷新", width=8,
                                  command=self.load_prediction_records,
                                  style="Primary.TButton")
        refresh_button.pack(side=tk.RIGHT, padx=5)

        # 删除按钮
        delete_button = ttk.Button(top_frame, text="删除", width=8,
                                 command=self.delete_selected_records,
                                 style="Primary.TButton")
        delete_button.pack(side=tk.RIGHT, padx=5)

        # 过滤区域
        filter_frame = ttk.LabelFrame(main_container, text="筛选条件")
        filter_frame.pack(fill=tk.X, padx=5, pady=5)

        # 联赛过滤
        league_label = ttk.Label(filter_frame, text="联赛:")
        league_label.grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        
        self.league_combobox = ttk.Combobox(filter_frame, textvariable=self.filter_options["league"], width=20)
        self.league_combobox.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 日期范围过滤
        date_from_label = ttk.Label(filter_frame, text="开始日期:")
        date_from_label.grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        
        date_from_entry = ttk.Entry(filter_frame, textvariable=self.filter_options["date_from"], width=12)
        date_from_entry.grid(row=0, column=3, padx=5, pady=5, sticky=tk.W)
        
        date_to_label = ttk.Label(filter_frame, text="结束日期:")
        date_to_label.grid(row=0, column=4, padx=5, pady=5, sticky=tk.W)
        
        date_to_entry = ttk.Entry(filter_frame, textvariable=self.filter_options["date_to"], width=12)
        date_to_entry.grid(row=0, column=5, padx=5, pady=5, sticky=tk.W)
        
        # 应用过滤按钮
        apply_filter_button = ttk.Button(filter_frame, text="应用过滤", width=10,
                                       command=self.apply_filter,
                                       style="Primary.TButton")
        apply_filter_button.grid(row=0, column=6, padx=10, pady=5)
        
        # 清除过滤按钮
        clear_filter_button = ttk.Button(filter_frame, text="清除过滤", width=10,
                                      command=self.clear_filter,
                                      style="Primary.TButton")
        clear_filter_button.grid(row=0, column=7, padx=5, pady=5)

        # 记录表格区域
        records_frame = ttk.LabelFrame(main_container, text="预测记录")
        records_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Treeview用于显示预测记录 - 简化显示字段
        columns = ("select", "id", "match_time", "league_name", "home_team", "away_team", 
                 "bookmaker", "first_choice", "prediction_time")
                 
        self.records_tree = ttk.Treeview(records_frame, columns=columns, show="headings", height=20)

        # 定义列
        self.records_tree.heading("select", text="选择")
        self.records_tree.heading("id", text="ID")
        self.records_tree.heading("match_time", text="比赛时间")
        self.records_tree.heading("league_name", text="联赛")
        self.records_tree.heading("home_team", text="主队")
        self.records_tree.heading("away_team", text="客队")
        self.records_tree.heading("bookmaker", text="博彩公司")
        self.records_tree.heading("first_choice", text="首选")
        self.records_tree.heading("prediction_time", text="预测时间")

        # 设置列宽度
        self.records_tree.column("select", width=40, anchor=tk.CENTER)
        self.records_tree.column("id", width=50, anchor=tk.CENTER)
        self.records_tree.column("match_time", width=120)
        self.records_tree.column("league_name", width=120)
        self.records_tree.column("home_team", width=120)
        self.records_tree.column("away_team", width=120)
        self.records_tree.column("bookmaker", width=120)
        self.records_tree.column("first_choice", width=100, anchor=tk.CENTER)
        self.records_tree.column("prediction_time", width=140)

        # 滚动条
        records_scroll_y = ttk.Scrollbar(records_frame, orient="vertical", command=self.records_tree.yview)
        records_scroll_x = ttk.Scrollbar(records_frame, orient="horizontal", command=self.records_tree.xview)
        self.records_tree.configure(yscrollcommand=records_scroll_y.set, xscrollcommand=records_scroll_x.set)

        # 布局
        self.records_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        records_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        records_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # 绑定点击事件处理选择/取消选择
        self.records_tree.bind("<ButtonRelease-1>", self.on_records_tree_click)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_container, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM, padx=5)

    def on_records_tree_click(self, event):
        """处理记录表格点击事件"""
        # 获取点击的列和项
        region = self.records_tree.identify("region", event.x, event.y)
        column = self.records_tree.identify_column(event.x)
        item = self.records_tree.identify_row(event.y)
        
        # 添加调试日志
        logging.info(f"记录表格点击: region={region}, column={column}, item={item}")
        
        # 如果点击的是表格单元格并且是有效行
        if region == "cell" and item:
            try:
                values = self.records_tree.item(item, "values")
                
                # 记录原始值以便调试
                logging.info(f"原始值: {values}")
                
                # 切换选择状态
                current_state = "✓" if values[0] == "✓" else ""
                new_state = "" if current_state else "✓"
                
                # 更新项目值
                new_values = list(values)
                new_values[0] = new_state
                self.records_tree.item(item, values=new_values)
                
                # 记录新值以便调试
                logging.info(f"更新后的值: {new_values}")
            except Exception as e:
                logging.error(f"处理点击事件时出错: {e}")
                messagebox.showerror("错误", f"选择操作失败: {e}")

    def load_prediction_records(self):
        """从数据库加载预测记录"""
        try:
            # 清空现有记录
            for item in self.records_tree.get_children():
                self.records_tree.delete(item)
                
            # 清空联赛下拉框选项
            self.league_combobox['values'] = []
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 读取所有预测记录 - 简化查询字段
            cursor.execute('''
            SELECT id, match_time, league_name, home_team, away_team, 
                   bookmaker, first_choice, prediction_time
            FROM match_predictions
            ORDER BY prediction_time DESC
            ''')
            
            records = cursor.fetchall()
            self.current_records = records
            
            # 获取所有联赛名称用于过滤
            cursor.execute('SELECT DISTINCT league_name FROM match_predictions ORDER BY league_name')
            leagues = [row[0] for row in cursor.fetchall()]
            self.league_combobox['values'] = [""] + leagues
            
            conn.close()
            
            # 显示记录
            for record in records:
                # 将None转换为空字符串
                record_values = ["" if v is None else v for v in record]
                self.records_tree.insert("", tk.END, values=("",) + tuple(record_values))
                
            # 更新状态栏
            self.status_var.set(f"已加载 {len(records)} 条预测记录")
            
        except Exception as e:
            logging.error(f"加载预测记录时出错: {e}")
            messagebox.showerror("错误", f"加载预测记录失败: {e}")
            self.status_var.set("加载预测记录失败")

    def apply_filter(self):
        """应用过滤条件"""
        try:
            # 获取过滤条件
            league = self.filter_options["league"].get()
            date_from = self.filter_options["date_from"].get()
            date_to = self.filter_options["date_to"].get()
            
            # 构建SQL查询 - 简化查询字段
            query = '''
            SELECT id, match_time, league_name, home_team, away_team, 
                   bookmaker, first_choice, prediction_time
            FROM match_predictions
            WHERE 1=1
            '''
            params = []
            
            if league:
                query += " AND league_name = ?"
                params.append(league)
                
            if date_from:
                query += " AND prediction_time >= ?"
                params.append(date_from)
                
            if date_to:
                query += " AND prediction_time <= ?"
                params.append(date_to + " 23:59:59")
                
            query += " ORDER BY prediction_time DESC"
            
            # 执行查询
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(query, params)
            records = cursor.fetchall()
            self.current_records = records
            conn.close()
            
            # 清空现有记录
            for item in self.records_tree.get_children():
                self.records_tree.delete(item)
                
            # 显示记录
            for record in records:
                # 将None转换为空字符串
                record_values = ["" if v is None else v for v in record]
                self.records_tree.insert("", tk.END, values=("",) + tuple(record_values))
                
            # 更新状态栏
            self.status_var.set(f"已筛选 {len(records)} 条预测记录")
            
        except Exception as e:
            logging.error(f"应用过滤条件时出错: {e}")
            messagebox.showerror("错误", f"应用过滤条件失败: {e}")
            self.status_var.set("应用过滤条件失败")

    def clear_filter(self):
        """清除过滤条件"""
        self.filter_options["league"].set("")
        self.filter_options["date_from"].set("")
        self.filter_options["date_to"].set("")
        self.load_prediction_records()

    def delete_selected_records(self):
        """删除选中的记录"""
        try:
            # 获取选中的记录ID
            selected_ids = []
            for item in self.records_tree.get_children():
                values = self.records_tree.item(item, "values")
                if values[0] == "✓":  # 第一列是选择状态
                    selected_ids.append(values[1])  # 第二列是ID
                    
            if not selected_ids:
                messagebox.showwarning("提示", "请至少选择一条记录进行删除")
                return
                
            # 确认删除
            confirm = messagebox.askyesno("确认", f"确定要删除选中的 {len(selected_ids)} 条记录吗？")
            if not confirm:
                return
                
            # 执行删除
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for record_id in selected_ids:
                cursor.execute("DELETE FROM match_predictions WHERE id = ?", (record_id,))
                
            conn.commit()
            conn.close()
            
            # 重新加载记录
            self.load_prediction_records()
            
            # 更新状态栏
            self.status_var.set(f"已删除 {len(selected_ids)} 条预测记录")
            
        except Exception as e:
            logging.error(f"删除预测记录时出错: {e}")
            messagebox.showerror("错误", f"删除预测记录失败: {e}")
            self.status_var.set("删除预测记录失败")

    def ensure_predictions_table(self):
        """确保预测结果表存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建预测结果表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS match_predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT,
                league_name TEXT,
                home_team TEXT,
                away_team TEXT,
                match_time TEXT,
                bookmaker TEXT,
                odds_type TEXT,
                team_type TEXT,
                gap_diff REAL,
                interval_type TEXT,
                rule_value TEXT,
                rule_diff TEXT,
                first_choice TEXT,
                standard_draw TEXT,
                actual_draw TEXT,
                draw_interval TEXT,
                prediction_time TEXT
            )
            ''')
            
            conn.commit()
            conn.close()
            logging.info("预测结果表创建成功")
        except Exception as e:
            logging.error(f"创建预测结果表时出错: {e}") 