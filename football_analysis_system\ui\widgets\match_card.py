import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk, ImageDraw
import os
from datetime import datetime

class MatchInfoCard(ttk.Frame):
    """现代化比赛信息卡片组件"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, style="Card.TFrame", **kwargs)
        
        # 初始化数据
        self.match_data = None
        self.home_logo = None
        self.away_logo = None
        self.match_status = "未开始"
        
        # 创建组件
        self._create_widgets()
    
    def _create_widgets(self):
        """创建卡片内容"""
        # 主容器
        main_container = ttk.Frame(self, style="Card.TFrame")
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 上部：比赛信息
        info_frame = ttk.Frame(main_container, style="Card.TFrame")
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 联赛信息
        self.league_frame = ttk.Frame(info_frame, style="Card.TFrame")
        self.league_frame.pack(fill=tk.X)
        
        self.league_label = ttk.Label(
            self.league_frame,
            text="英超",
            font=("Segoe UI", 14, "bold"),
            foreground="#4361ee",
            style="Card.TLabel"
        )
        self.league_label.pack(side=tk.LEFT)
        
        # 比赛时间
        self.time_label = ttk.Label(
            self.league_frame,
            text="2023-09-25 21:00",
            style="Card.TLabel"
        )
        self.time_label.pack(side=tk.RIGHT)
        
        # 状态标签
        status_frame = ttk.Frame(info_frame, style="Card.TFrame")
        status_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.status_label = ttk.Label(
            status_frame,
            text="未开始",
            style="Card.TLabel",
            foreground="#4361ee"
        )
        self.status_label.pack(side=tk.RIGHT)
        
        # 中部：球队信息和比分
        teams_frame = ttk.Frame(main_container, style="Card.TFrame")
        teams_frame.pack(fill=tk.X, pady=10)
        teams_frame.columnconfigure(0, weight=2)  # 主队
        teams_frame.columnconfigure(1, weight=1)  # 比分/VS
        teams_frame.columnconfigure(2, weight=2)  # 客队
        
        # 主队信息
        home_frame = ttk.Frame(teams_frame, style="Card.TFrame")
        home_frame.grid(row=0, column=0, sticky="ew")
        
        # 主队标志占位区
        self.home_logo_label = ttk.Label(home_frame, style="Card.TLabel")
        self.home_logo_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # 主队名称
        self.home_name_label = ttk.Label(
            home_frame,
            text="主队名称",
            font=("Segoe UI", 16, "bold"),
            foreground="#3f8ef7",
            style="Card.TLabel"
        )
        self.home_name_label.pack(side=tk.LEFT, fill=tk.X)
        
        # 比分/VS
        score_frame = ttk.Frame(teams_frame, style="Card.TFrame")
        score_frame.grid(row=0, column=1)
        
        self.score_label = ttk.Label(
            score_frame,
            text="VS",
            font=("Segoe UI", 20, "bold"),
            style="Card.TLabel"
        )
        self.score_label.pack(expand=True)
        
        # 客队信息
        away_frame = ttk.Frame(teams_frame, style="Card.TFrame")
        away_frame.grid(row=0, column=2, sticky="ew")
        
        # 客队名称
        self.away_name_label = ttk.Label(
            away_frame,
            text="客队名称",
            font=("Segoe UI", 16, "bold"),
            foreground="#f97316",
            style="Card.TLabel"
        )
        self.away_name_label.pack(side=tk.RIGHT, fill=tk.X)
        
        # 客队标志占位区
        self.away_logo_label = ttk.Label(away_frame, style="Card.TLabel")
        self.away_logo_label.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 下部：赔率信息
        odds_frame = ttk.Frame(main_container, style="CardHeader.TFrame")
        odds_frame.pack(fill=tk.X, pady=(15, 0))
        
        # 赔率标题
        odds_title_frame = ttk.Frame(odds_frame, style="CardHeader.TFrame")
        odds_title_frame.pack(fill=tk.X, padx=10, pady=5)
        
        odds_title = ttk.Label(
            odds_title_frame,
            text="主要赔率",
            font=("Segoe UI", 12, "bold"),
            style="CardHeader.TLabel"
        )
        odds_title.pack(side=tk.LEFT)
        
        self.company_name = ttk.Label(
            odds_title_frame,
            text="威廉希尔",
            style="CardHeader.TLabel"
        )
        self.company_name.pack(side=tk.RIGHT)
        
        # 赔率详情
        odds_detail_frame = ttk.Frame(main_container, style="Card.TFrame")
        odds_detail_frame.pack(fill=tk.X, pady=10)
        odds_detail_frame.columnconfigure(0, weight=1)
        odds_detail_frame.columnconfigure(1, weight=1)
        odds_detail_frame.columnconfigure(2, weight=1)
        
        # 胜平负标签
        home_win_header = ttk.Label(
            odds_detail_frame,
            text="主胜",
            style="Card.TLabel"
        )
        home_win_header.grid(row=0, column=0, pady=5)
        
        draw_header = ttk.Label(
            odds_detail_frame,
            text="平局",
            style="Card.TLabel"
        )
        draw_header.grid(row=0, column=1, pady=5)
        
        away_win_header = ttk.Label(
            odds_detail_frame,
            text="客胜",
            style="Card.TLabel"
        )
        away_win_header.grid(row=0, column=2, pady=5)
        
        # 赔率值
        self.home_win_odds = ttk.Label(
            odds_detail_frame,
            text="2.10",
            font=("Segoe UI", 14, "bold"),
            foreground="#3f8ef7",
            style="Card.TLabel"
        )
        self.home_win_odds.grid(row=1, column=0, pady=5)
        
        self.draw_odds = ttk.Label(
            odds_detail_frame,
            text="3.25",
            font=("Segoe UI", 14, "bold"),
            foreground="#9333ea",
            style="Card.TLabel"
        )
        self.draw_odds.grid(row=1, column=1, pady=5)
        
        self.away_win_odds = ttk.Label(
            odds_detail_frame,
            text="3.40",
            font=("Segoe UI", 14, "bold"),
            foreground="#f97316",
            style="Card.TLabel"
        )
        self.away_win_odds.grid(row=1, column=2, pady=5)
    
    def update_match_data(self, match_data):
        """更新比赛数据"""
        self.match_data = match_data
        
        if match_data:
            # 更新联赛信息
            self.league_label.configure(text=match_data.get('league', '未知联赛'))
            
            # 更新比赛时间
            match_time = match_data.get('match_time', '')
            if match_time:
                try:
                    dt = datetime.strptime(match_time, '%Y-%m-%d %H:%M:%S')
                    formatted_time = dt.strftime('%m月%d日 %H:%M')
                    self.time_label.configure(text=formatted_time)
                except:
                    self.time_label.configure(text=match_time)
            
            # 更新状态
            status = match_data.get('status', '未开始')
            self.status_label.configure(text=status)
            
            # 更新颜色
            if status == '已结束':
                self.status_label.configure(foreground="#10b981")  # 绿色
            elif status == '进行中':
                self.status_label.configure(foreground="#f59e0b")  # 黄色
            else:
                self.status_label.configure(foreground="#4361ee")  # 蓝色
            
            # 更新队伍信息
            self.home_name_label.configure(text=match_data.get('home_team', '主队'))
            self.away_name_label.configure(text=match_data.get('away_team', '客队'))
            
            # 更新比分
            home_score = match_data.get('home_score')
            away_score = match_data.get('away_score')
            
            if home_score is not None and away_score is not None:
                self.score_label.configure(text=f"{home_score} - {away_score}")
            else:
                self.score_label.configure(text="VS")
            
            # 更新赔率
            odds_data = match_data.get('odds', {})
            if odds_data:
                company = odds_data.get('company', '')
                if company:
                    self.company_name.configure(text=company)
                
                home_win = odds_data.get('home_win', '')
                if home_win:
                    self.home_win_odds.configure(text=f"{home_win:.2f}")
                
                draw = odds_data.get('draw', '')
                if draw:
                    self.draw_odds.configure(text=f"{draw:.2f}")
                
                away_win = odds_data.get('away_win', '')
                if away_win:
                    self.away_win_odds.configure(text=f"{away_win:.2f}")
            
            # 更新队伍logo
            self._update_team_logos(match_data)
    
    def _update_team_logos(self, match_data):
        """更新队伍logo"""
        # 实际应用中，这里可以根据队伍名称加载对应的logo图像
        # 这里使用简单的圆形占位图像
        home_logo = self._create_team_logo(40, "#3f8ef7")  # 蓝色为主队
        away_logo = self._create_team_logo(40, "#f97316")  # 橙色为客队
        
        self.home_logo = home_logo
        self.away_logo = away_logo
        
        self.home_logo_label.configure(image=self.home_logo)
        self.away_logo_label.configure(image=self.away_logo)
    
    def _create_team_logo(self, size, color):
        """创建团队Logo占位图像"""
        logo = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(logo)
        
        # 绘制圆形背景
        draw.ellipse([2, 2, size-2, size-2], fill=color)
        
        # 绘制队伍首字母(示例)
        # 实际应用中应该加载真实logo
        
        return ImageTk.PhotoImage(logo)


# 测试代码
if __name__ == "__main__":
    root = tk.Tk()
    root.title("比赛信息卡片测试")
    root.geometry("500x400")
    
    sample_data = {
        'league': '英超',
        'match_time': '2023-10-25 20:00:00',
        'status': '未开始',
        'home_team': '曼联',
        'away_team': '利物浦',
        'home_score': None,
        'away_score': None,
        'odds': {
            'company': '威廉希尔',
            'home_win': 2.10,
            'draw': 3.25,
            'away_win': 3.40
        }
    }
    
    card = MatchInfoCard(root)
    card.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    card.update_match_data(sample_data)
    
    root.mainloop() 