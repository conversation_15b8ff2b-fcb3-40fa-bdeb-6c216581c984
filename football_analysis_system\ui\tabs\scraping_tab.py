import tkinter as tk
from tkinter import ttk
from football_analysis_system.config import COLOR_BG, COLOR_PRIMARY, COLOR_TEXT, FONT_LARGE, FONT_NORMAL, FONT_SUBHEADER

class ScrapingTab(tk.Frame):
    """爬虫状态标签页"""

    def __init__(self, parent, log_queue):
        """
        初始化爬虫状态标签页

        Args:
            parent: 父容器
            log_queue: 日志消息队列
        """
        super().__init__(parent, bg=COLOR_BG)

        self.log_queue = log_queue
        self.create_widgets()

        # 设置自动刷新日志
        self.after(100, self.process_log_queue)

    def create_widgets(self):
        """创建标签页控件"""
        # 爬虫状态标签页
        scraping_container = tk.Frame(self, bg=COLOR_BG)
        scraping_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 操作区域
        control_frame = tk.Frame(scraping_container, bg=COLOR_BG)
        control_frame.pack(fill=tk.X, pady=10)

        # 爬虫状态提示
        self.scrape_status_label = tk.Label(control_frame,
                                         text="数据更新状态: 就绪",
                                         font=FONT_LARGE,
                                         bg=COLOR_BG, fg=COLOR_TEXT)
        self.scrape_status_label.pack(side=tk.LEFT, padx=10)

        # 进度条
        progress_frame = tk.Frame(scraping_container, bg=COLOR_BG)
        progress_frame.pack(fill=tk.X, pady=10)

        self.progress_label = tk.Label(progress_frame, text="总体进度: ",
                                    font=FONT_NORMAL, bg=COLOR_BG, fg=COLOR_TEXT)
        self.progress_label.pack(side=tk.LEFT, padx=10)

        self.progress_bar = ttk.Progressbar(progress_frame,
                                          orient=tk.HORIZONTAL,
                                          length=500,
                                          mode='determinate')
        self.progress_bar.pack(side=tk.LEFT, padx=10, expand=True, fill=tk.X)

        self.progress_percent = tk.Label(progress_frame, text="0%",
                                      font=FONT_NORMAL, bg=COLOR_BG, fg=COLOR_TEXT)
        self.progress_percent.pack(side=tk.LEFT, padx=10)

        # 日志区域
        log_frame = tk.LabelFrame(scraping_container, text="爬取日志",
                                font=FONT_SUBHEADER,
                                bg=COLOR_BG, fg=COLOR_PRIMARY)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # 日志文本框
        self.log_text = tk.Text(log_frame, wrap=tk.WORD,
                             height=20, width=80,
                             bg=COLOR_BG, fg=COLOR_TEXT,
                             font=("Consolas", 10))
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 日志滚动条
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        # 禁用文本框编辑
        self.log_text.config(state=tk.DISABLED)

        # 设置标签页颜色
        self.log_text.tag_configure("debug", foreground="gray")
        self.log_text.tag_configure("info", foreground=COLOR_TEXT)
        self.log_text.tag_configure("success", foreground="#27ae60")
        self.log_text.tag_configure("error", foreground="#e74c3c")
        self.log_text.tag_configure("warning", foreground="#f39c12")

    def process_log_queue(self):
        """处理日志队列中的消息"""
        try:
            while not self.log_queue.empty():
                message = self.log_queue.get_nowait()

                # 处理特殊消息类型
                if isinstance(message, tuple) and len(message) == 2:
                    if message[0] == "update_progress":
                        # 处理进度更新消息
                        self.update_progress(message[1])
                    else:
                        # 正常日志消息
                        self.add_log(message[0], message[1])
                else:
                    # 其他消息
                    self.add_log(str(message), "info")

                # 确保UI更新
                self.update_idletasks()

                # 标记任务完成
                self.log_queue.task_done()
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"处理日志消息出错: {e}")
        finally:
            # 每50毫秒处理一次，提高响应速度
            self.after(50, self.process_log_queue)

    def set_status(self, status_text):
        """
        设置爬取状态

        Args:
            status_text: 状态文本
        """
        self.scrape_status_label.config(text=f"数据更新状态: {status_text}")

    def update_progress(self, value):
        """
        更新进度条

        Args:
            value: 进度值（0-100）
        """
        self.progress_bar['value'] = value
        self.progress_percent.config(text=f"{value}%")

    def add_log(self, message, log_type="info"):
        """
        添加日志消息

        Args:
            message: 日志消息
            log_type: 日志类型
        """
        try:
            # 启用编辑
            self.log_text.config(state=tk.NORMAL)

            # 确保消息以换行符结束
            if not message.endswith('\n'):
                message += '\n'

            # 插入消息
            self.log_text.insert(tk.END, message, log_type)

            # 滚动到最后
            self.log_text.see(tk.END)

            # 禁用编辑
            self.log_text.config(state=tk.DISABLED)

            # 立即更新UI
            self.update_idletasks()

            # 打印到控制台，便于调试
            print(f"UI日志: {log_type} - {message.strip()}")
        except Exception as e:
            print(f"添加日志到UI时出错: {e}")

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.update_progress(0)