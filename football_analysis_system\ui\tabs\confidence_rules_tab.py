import tkinter as tk
from tkinter import ttk
import logging
from football_analysis_system.config import COLOR_BG, COLOR_PRIMARY, COLOR_SECONDARY, COLOR_TEXT, COLOR_ACCENT

class ConfidenceRulesTab(tk.Frame):
    """信心等级规则标签页"""

    def __init__(self, parent):
        """
        初始化信心等级规则标签页

        Args:
            parent: 父容器
        """
        super().__init__(parent, bg=COLOR_BG)
        
        # 定义颜色常量 - 使用更柔和的颜色
        self.COLOR_LEVEL_0 = "#E74C3C"  # 红色 - 欠缺信心
        self.COLOR_LEVEL_1 = "#F39C12"  # 橙色 - 信心不足
        self.COLOR_LEVEL_2 = "#F1C40F"  # 黄色 - 信心中下
        self.COLOR_LEVEL_3 = "#3498DB"  # 蓝色 - 信心中庸
        self.COLOR_LEVEL_4 = "#2ECC71"  # 绿色 - 信心中上
        self.COLOR_LEVEL_5 = "#27AE60"  # 深绿色 - 信心充足
        self.COLOR_LEVEL_6 = "#1E8449"  # 更深绿色 - 极致信心
        
        # 监听窗口大小变化
        self.bind("<Configure>", self.on_resize)
        
        # 创建标签页内容
        self.create_widgets()

    def create_widgets(self):
        """创建标签页控件"""
        # 标题框架
        title_frame = ttk.Frame(self, style="TFrame")
        title_frame.pack(fill=tk.X, padx=15, pady=(15, 5))
        
        # 大标题
        title_label = ttk.Label(
            title_frame, 
            text="信心等级规则表", 
            font=('思源黑体', 16, 'bold'),
            foreground=COLOR_PRIMARY, 
            background=COLOR_BG
        )
        title_label.pack(side=tk.LEFT)
        
        # 创建表格容器 - 使用滚动区域
        self.canvas = tk.Canvas(self, bg=COLOR_BG, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas, style="TFrame")
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas_frame = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # 设置Canvas大小随窗口调整
        self.canvas.bind("<Configure>", self.on_canvas_configure)
        
        # 绑定鼠标滚轮事件
        self.bind_mousewheel()
        
        # 放置滚动区域
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=15, pady=5)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 创建表格
        self.create_compact_table()
        
    def on_resize(self, event=None):
        """处理窗口大小变化"""
        if event and event.widget == self:
            # 获取当前宽度
            width = event.width
            # 根据宽度调整表格布局
            if width < 800:
                self.adjust_table_for_small_width()
            else:
                self.adjust_table_for_normal_width()
            
    def adjust_table_for_small_width(self):
        """调整表格布局以适应小宽度"""
        if hasattr(self, 'table_frame'):
            # 小宽度时减小第二列的宽度
            self.table_frame.grid_columnconfigure(0, minsize=50, weight=1)  # 信心值列
            self.table_frame.grid_columnconfigure(1, minsize=80, weight=1)  # 信心等级列
            self.table_frame.grid_columnconfigure(2, minsize=200, weight=3) # 规则列
            
            # 调整规则文本的换行宽度
            for row_widgets in self.rule_widgets:
                for widget in row_widgets:
                    if isinstance(widget, tk.Label) and widget.cget("wraplength") > 0:
                        widget.configure(wraplength=200)  # 小宽度时设置更小的换行宽度

    def adjust_table_for_normal_width(self):
        """调整表格布局以适应正常宽度"""
        if hasattr(self, 'table_frame'):
            # 正常宽度时恢复列宽
            self.table_frame.grid_columnconfigure(0, minsize=70, weight=1)  # 信心值列
            self.table_frame.grid_columnconfigure(1, minsize=100, weight=1)  # 信心等级列
            self.table_frame.grid_columnconfigure(2, minsize=280, weight=3) # 规则列
            
            # 调整规则文本的换行宽度
            for row_widgets in self.rule_widgets:
                for widget in row_widgets:
                    if isinstance(widget, tk.Label) and widget.cget("wraplength") > 0:
                        widget.configure(wraplength=280)  # 正常宽度时恢复换行宽度
            
    def on_canvas_configure(self, event=None):
        """处理Canvas大小变化"""
        # 调整Canvas中frame的宽度
        if event:
            self.canvas.itemconfig(self.canvas_frame, width=event.width)
            
    def bind_mousewheel(self):
        """绑定鼠标滚轮事件到滚动区域"""
        def _on_mousewheel(event):
            # 处理不同平台的滚轮事件
            if event.num == 4 or event.delta > 0:  # 向上滚动
                self.canvas.yview_scroll(-1, "units")
            elif event.num == 5 or event.delta < 0:  # 向下滚动
                self.canvas.yview_scroll(1, "units")
                
        # 绑定鼠标滚轮事件
        # Linux系统
        self.canvas.bind("<Button-4>", _on_mousewheel)
        self.canvas.bind("<Button-5>", _on_mousewheel)
        # Windows/Mac系统
        self.canvas.bind("<MouseWheel>", _on_mousewheel)
    
    def create_compact_table(self):
        """创建紧凑型信心等级规则表格"""
        # 创建表格框架
        self.table_frame = ttk.Frame(self.scrollable_frame)
        self.table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建表头
        self.create_header(self.table_frame)
        
        # 定义信心等级规则数据 - 使用更简洁的规则描述
        confidence_rules = [
            {"level_value": "0", "level_name": "欠缺信心", "color": self.COLOR_LEVEL_0, "rules": 
                "实开盘高水/中高水/中水/中低水/低水"
            },
            {"level_value": "1.4", "level_name": "信心不足", "color": self.COLOR_LEVEL_1, "rules": 
                "韬光盘低水/中低水；实开盘低水/中低水/中水"
            },
            {"level_value": "2.8", "level_name": "信心中下", "color": self.COLOR_LEVEL_2, "rules": 
                "韬光盘低水/中低水；实开盘各水位"
            },
            {"level_value": "4.2", "level_name": "信心中庸", "color": self.COLOR_LEVEL_3, "rules": 
                "实开盘中低水/低水/超高水/高水；超实盘高水/中高水；韬光盘低水/超低水/中高水"
            },
            {"level_value": "6.8", "level_name": "信心中上", "color": self.COLOR_LEVEL_4, "rules": 
                "超实盘超高水/高水/中高水/中水/中低水；实开盘超低水/低水/中低水"
            },
            {"level_value": "7.9", "level_name": "信心充足", "color": self.COLOR_LEVEL_5, "rules": 
                "超实盘高水/中高水/中水/中低水/低水/超低水"
            },
            {"level_value": "9", "level_name": "极致信心", "color": self.COLOR_LEVEL_6, "rules": 
                "超实盘低水/中低水/超低水；超深盘中上水/中下水；超散盘中高水"
            }
        ]
        
        # 存储规则部件的引用
        self.rule_widgets = []
        
        # 创建表格内容 - 更紧凑的布局
        for i, level in enumerate(confidence_rules):
            # 设置单元格背景色 - 更柔和的颜色
            level_bg = self.get_lighter_color(level["color"], factor=0.85)
            level_style = {'background': level_bg, 'pady': 4}
            
            # 确定行号
            row = i + 1
            
            # 信心值
            level_value_label = tk.Label(
                self.table_frame, 
                text=level["level_value"], 
                font=('思源黑体', 11, 'bold'),
                foreground=self.get_text_color_for_bg(level_bg),
                **level_style
            )
            level_value_label.grid(row=row, column=0, sticky="nsew", padx=2, pady=2)
            
            # 信心等级
            level_name_label = tk.Label(
                self.table_frame, 
                text=level["level_name"], 
                font=('思源黑体', 11),
                foreground=self.get_text_color_for_bg(level_bg),
                **level_style
            )
            level_name_label.grid(row=row, column=1, sticky="nsew", padx=2, pady=2)
            
            # 规则 - 简化为一个带自动换行的标签
            rules_label = tk.Label(
                self.table_frame, 
                text=level["rules"], 
                font=('思源黑体', 10),
                foreground=self.get_text_color_for_bg(level_bg),
                wraplength=280,  # 自动换行宽度
                justify="left",
                anchor="w",
                **level_style
            )
            rules_label.grid(row=row, column=2, sticky="nsew", padx=2, pady=2)
            
            # 存储规则部件引用
            self.rule_widgets.append([level_value_label, level_name_label, rules_label])
            
        # 设置列宽
        self.table_frame.grid_columnconfigure(0, minsize=70, weight=1)  # 信心值列
        self.table_frame.grid_columnconfigure(1, minsize=100, weight=1) # 信心等级列
        self.table_frame.grid_columnconfigure(2, minsize=280, weight=3) # 规则列
    
    def create_header(self, parent):
        """创建表头"""
        # 表头样式 - 使用ttk样式，与整体界面更协调
        header_style = {'font': ('思源黑体', 12, 'bold'), 'background': COLOR_PRIMARY, 'foreground': 'white', 'padx': 8, 'pady': 5}
        
        # 创建表头
        tk.Label(parent, text="信心值", **header_style).grid(row=0, column=0, sticky="nsew", padx=2, pady=2)
        tk.Label(parent, text="信心等级", **header_style).grid(row=0, column=1, sticky="nsew", padx=2, pady=2)
        tk.Label(parent, text="判断规则", **header_style).grid(row=0, column=2, sticky="nsew", padx=2, pady=2)
    
    def get_lighter_color(self, hex_color, factor=0.8):
        """获取更浅的颜色"""
        # 将十六进制颜色转换为RGB
        r = int(hex_color[1:3], 16)
        g = int(hex_color[3:5], 16)
        b = int(hex_color[5:7], 16)
        
        # 计算更浅的颜色
        r = min(255, int(r + (255 - r) * factor))
        g = min(255, int(g + (255 - g) * factor))
        b = min(255, int(b + (255 - b) * factor))
        
        # 转换回十六进制
        return f"#{r:02x}{g:02x}{b:02x}"
        
    def get_text_color_for_bg(self, bg_color):
        """根据背景色选择适合的文本颜色"""
        # 将十六进制颜色转换为RGB
        r = int(bg_color[1:3], 16)
        g = int(bg_color[3:5], 16)
        b = int(bg_color[5:7], 16)
        
        # 计算亮度 (按照人眼对RGB的不同敏感度)
        brightness = (r * 0.299 + g * 0.587 + b * 0.114) / 255
        
        # 根据亮度选择黑色或白色文本
        return "#000000" if brightness > 0.6 else "#FFFFFF" 