import tkinter as tk
from tkinter import ttk
import logging
from config import COLOR_BG, COLOR_PRIMARY, FONT_SUBHEADER

class IntervalAnalysisTab(tk.Frame):
    """区间分析标签页"""
    
    def __init__(self, parent, odds_analyzer, interval_analyzer):
        """
        初始化区间分析标签页
        
        Args:
            parent: 父容器
            odds_analyzer: 赔率分析器
            interval_analyzer: 区间分析器
        """
        super().__init__(parent, bg=COLOR_BG)
        
        self.odds_analyzer = odds_analyzer
        self.interval_analyzer = interval_analyzer
        self.create_widgets()
    
    def create_widgets(self):
        """创建标签页控件"""
        # 容器框架
        interval_container = tk.Frame(self, bg=COLOR_BG)
        interval_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)  # 减小上下边距
        
        # 区间分析表格框架
        self.interval_frame = tk.LabelFrame(interval_container, text="区间分析结果", 
                                          font=FONT_SUBHEADER, 
                                          bg=COLOR_BG, fg=COLOR_PRIMARY)
        self.interval_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=2)  # 减小内边距
        
        # 创建Treeview用于显示区间分析 - 改变列结构，分别显示主队和客队
        columns = ("company", "team_type", "gap_diff", "interval_type", "rule_value", "rule_diff", "choice")
        self.interval_tree = ttk.Treeview(self.interval_frame, columns=columns, show="headings", height=25)  # 增加显示行数
        
        # 设置行高为18像素（默认为20）
        style = ttk.Style()
        style.configure("Treeview", rowheight=18)
        
        # 定义列
        self.interval_tree.heading("company", text="投注公司")
        self.interval_tree.heading("team_type", text="队伍")
        self.interval_tree.heading("gap_diff", text="档距差")
        self.interval_tree.heading("interval_type", text="盘口类型")
        self.interval_tree.heading("rule_value", text="规则值")
        self.interval_tree.heading("rule_diff", text="规差值")
        self.interval_tree.heading("choice", text="首选")
        
        # 设置列宽度
        self.interval_tree.column("company", width=100)
        self.interval_tree.column("team_type", width=50, anchor=tk.CENTER)
        self.interval_tree.column("gap_diff", width=70, anchor=tk.CENTER)
        self.interval_tree.column("interval_type", width=100, anchor=tk.CENTER)
        self.interval_tree.column("rule_value", width=70, anchor=tk.CENTER)
        self.interval_tree.column("rule_diff", width=70, anchor=tk.CENTER)
        self.interval_tree.column("choice", width=80, anchor=tk.CENTER)
        
        # 滚动条
        interval_scroll_y = ttk.Scrollbar(self.interval_frame, orient="vertical", command=self.interval_tree.yview)
        interval_scroll_x = ttk.Scrollbar(self.interval_frame, orient="horizontal", command=self.interval_tree.xview)
        self.interval_tree.configure(yscrollcommand=interval_scroll_y.set, xscrollcommand=interval_scroll_x.set)
        
        # 布局
        self.interval_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        interval_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        interval_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 添加平赔区间分析框架
        self.draw_interval_frame = tk.LabelFrame(interval_container, text="平赔区间分析", 
                                              font=FONT_SUBHEADER, 
                                              bg=COLOR_BG, fg=COLOR_PRIMARY)
        self.draw_interval_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=2)  # 减小内边距
        
        # 创建平赔区间分析表格
        draw_columns = ("company", "standard_draw", "actual_draw", "draw_interval")
        self.draw_interval_tree = ttk.Treeview(self.draw_interval_frame, columns=draw_columns, show="headings", height=10)  # 增加显示行数
        
        # 定义列
        self.draw_interval_tree.heading("company", text="投注公司")
        self.draw_interval_tree.heading("standard_draw", text="标准平赔")
        self.draw_interval_tree.heading("actual_draw", text="实际平赔")
        self.draw_interval_tree.heading("draw_interval", text="平赔区间")
        
        # 设置列宽度
        self.draw_interval_tree.column("company", width=100)
        self.draw_interval_tree.column("standard_draw", width=100, anchor=tk.CENTER)
        self.draw_interval_tree.column("actual_draw", width=100, anchor=tk.CENTER)
        self.draw_interval_tree.column("draw_interval", width=100, anchor=tk.CENTER)
        
        # 滚动条
        draw_scroll_y = ttk.Scrollbar(self.draw_interval_frame, orient="vertical", command=self.draw_interval_tree.yview)
        draw_scroll_x = ttk.Scrollbar(self.draw_interval_frame, orient="horizontal", command=self.draw_interval_tree.xview)
        self.draw_interval_tree.configure(yscrollcommand=draw_scroll_y.set, xscrollcommand=draw_scroll_x.set)
        
        # 布局
        self.draw_interval_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        draw_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        draw_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 应用特殊样式 - 使用更细的分隔线
        self.interval_tree.tag_configure('separator', background='#f0f0f0')  # 更浅的分隔线颜色
        
        # 为规则值不同的范围配置不同的颜色标签
        self.interval_tree.tag_configure('good', background='#90EE90')  # 浅绿色，规则值 ≤ 3
        self.interval_tree.tag_configure('normal', background='#FFFF99') # 浅黄色，规则值 3-4.5
        self.interval_tree.tag_configure('bad', background='#FFA07A')   # 浅橙色，规则值 4.5-6
        self.interval_tree.tag_configure('very_bad', background='#FF6B6B') # 浅红色，规则值 > 6
        
        # 为主队和客队单独创建标签
        self.interval_tree.tag_configure('home_good', background='#90EE90')
        self.interval_tree.tag_configure('home_normal', background='#FFFF99')
        self.interval_tree.tag_configure('home_bad', background='#FFA07A')
        self.interval_tree.tag_configure('home_very_bad', background='#FF6B6B')
        
        self.interval_tree.tag_configure('away_good', background='#90EE90')
        self.interval_tree.tag_configure('away_normal', background='#FFFF99')
        self.interval_tree.tag_configure('away_bad', background='#FFA07A')
        self.interval_tree.tag_configure('away_very_bad', background='#FF6B6B')
    
    def update_interval_data(self, match_data, gap_difference):
        """更新区间分析数据"""
        try:
            # 清空区间分析表格
            for item in self.interval_tree.get_children():
                self.interval_tree.delete(item)
                
            # 清空平赔区间分析表格
            for item in self.draw_interval_tree.get_children():
                self.draw_interval_tree.delete(item)
                
            if not match_data or 'odds' not in match_data:
                logging.warning("没有有效数据可分析")
                return
                
            # 获取主客队对象和多公司档距差数据
            home_team_data = match_data.get('home_team_obj')
            away_team_data = match_data.get('away_team_obj')
            
            # 获取多公司档距差数据
            company_gap_diffs = {}
            
            if home_team_data and away_team_data:
                home_ratings = getattr(home_team_data, 'multi_company_ratings', {})
                away_ratings = getattr(away_team_data, 'multi_company_ratings', {})
                
                # 计算每家公司的档距差
                all_company_ids = set(list(home_ratings.keys()) + list(away_ratings.keys()))
                
                for company_id in all_company_ids:
                    home_data = home_ratings.get(company_id, {})
                    away_data = away_ratings.get(company_id, {})
                    
                    home_rating = home_data.get('rating')
                    away_rating = away_data.get('rating')
                    
                    if home_rating is not None and away_rating is not None:
                        try:
                            h_rating = float(home_rating)
                            a_rating = float(away_rating)
                            company_gap = round(h_rating - a_rating, 2)
                            company_gap_diffs[company_id] = {
                                'gap_diff': company_gap,
                                'company_name': home_data.get('company_name') or away_data.get('company_name')
                            }
                        except (ValueError, TypeError):
                            pass
            
            # 按特定顺序排列博彩公司，香港马会始终在第一位，澳门在第二位
            sorted_bookmakers = []
            odds_dict = match_data.get('odds', {})
            
            # 先添加香港马会（如果存在）
            for bookmaker in odds_dict.keys():
                if bookmaker and ("马会" in bookmaker or "HKJC" in bookmaker or "香港" in bookmaker):
                    sorted_bookmakers.append(bookmaker)
                    break
            
            # 再添加澳门（如果存在）
            for bookmaker in odds_dict.keys():
                if (bookmaker not in sorted_bookmakers and 
                    ("澳门" in bookmaker or "Macau" in bookmaker.lower() or "澳" in bookmaker)):
                    sorted_bookmakers.append(bookmaker)
                    break
            
            # 最后添加其他公司（包括立博）
            for bookmaker in odds_dict.keys():
                # 排除已添加的马会和澳门
                if bookmaker not in sorted_bookmakers:
                    sorted_bookmakers.append(bookmaker)
            
            # 遍历所有博彩公司（按排序后的顺序）
            for bookmaker in sorted_bookmakers:
                odds_data = odds_dict[bookmaker]
                
                # 只有香港马会(HKJC)和澳门使用初赔数据，其他公司使用即时赔率
                use_initial_odds = False
                if bookmaker and ("马会" in bookmaker or "HKJC" in bookmaker or "香港" in bookmaker or "澳门" in bookmaker or "Macau" in bookmaker):
                    use_initial_odds = True
                    
                if use_initial_odds:
                    # 香港马会和澳门使用初始赔率
                    home_win = odds_data.get('initial_home_win') or odds_data.get('instant_home_win')
                    draw = odds_data.get('initial_draw') or odds_data.get('instant_draw')
                    away_win = odds_data.get('initial_away_win') or odds_data.get('instant_away_win')
                    if "澳门" in bookmaker or "Macau" in bookmaker:
                        logging.info(f"区间分析: 使用澳门初赔数据: {home_win}/{draw}/{away_win}")
                    else:
                        logging.info(f"区间分析: 使用香港马会初赔数据: {home_win}/{draw}/{away_win}")
                else:
                    # 其他公司使用即时赔率
                    home_win = odds_data.get('instant_home_win')
                    draw = odds_data.get('instant_draw')
                    away_win = odds_data.get('instant_away_win')
                
                payout_rate = odds_data.get('payout_rate')
                
                # 计算满水赔率
                home_win_odds = self.odds_analyzer.calculate_true_odds(home_win, payout_rate)
                draw_true = self.odds_analyzer.calculate_true_odds(draw, payout_rate)
                away_win_odds = self.odds_analyzer.calculate_true_odds(away_win, payout_rate)
                
                # 查找对应的档距
                home_gap = None
                away_gap = None
                if home_win_odds:
                    home_gap = self.odds_analyzer.find_gap_for_odds(home_win_odds, "win")
                if away_win_odds:
                    away_gap = self.odds_analyzer.find_gap_for_odds(away_win_odds, "loss")
                
                # 确定使用哪个档距差
                current_gap_diff = gap_difference  # 默认值
                
                # 尝试匹配博彩公司名称与多公司档距差数据
                matched_company_id = None
                for company_id, company_data in company_gap_diffs.items():
                    if company_data['company_name'] == bookmaker:
                        matched_company_id = company_id
                        break
                
                if matched_company_id:
                    current_gap_diff = company_gap_diffs[matched_company_id]['gap_diff']
                
                # 计算主队区间
                home_interval = self.interval_analyzer.find_interval_for_gap(home_gap, current_gap_diff, True)
                home_type = home_interval.get('interval_type', '未知')
                home_rule_value = home_interval.get('rule_value', '未知')
                
                # 计算客队区间
                away_interval = self.interval_analyzer.find_interval_for_gap(away_gap, current_gap_diff, False)
                away_type = away_interval.get('interval_type', '未知')
                away_rule_value = away_interval.get('rule_value', '未知')
                
                # 计算规差值
                rule_diff = None
                if home_rule_value != '未知' and away_rule_value != '未知':
                    try:
                        rule_diff = float(home_rule_value) - float(away_rule_value)
                    except (ValueError, TypeError):
                        pass
                
                # 获取首选推荐
                first_choice = self.interval_analyzer.get_first_choice(rule_diff, current_gap_diff)
                
                # 确定标签
                home_tag = None
                away_tag = None
                
                try:
                    # 处理主队规则值
                    if home_rule_value != "未知":
                        home_rule_float = float(home_rule_value)
                        if home_rule_float <= 3:
                            home_tag = 'home_good'
                        elif home_rule_float <= 4.5:
                            home_tag = 'home_normal'
                        elif home_rule_float <= 6:
                            home_tag = 'home_bad'
                        else:
                            home_tag = 'home_very_bad'
                    
                    # 处理客队规则值
                    if away_rule_value != "未知":
                        away_rule_float = float(away_rule_value)
                        if away_rule_float <= 3:
                            away_tag = 'away_good'
                        elif away_rule_float <= 4.5:
                            away_tag = 'away_normal'
                        elif away_rule_float <= 6:
                            away_tag = 'away_bad'
                        else:
                            away_tag = 'away_very_bad'
                            
                    # 打印日志便于调试
                    logging.debug(f"规则值：主队={home_rule_value}({home_tag})，客队={away_rule_value}({away_tag})")
                    
                except (ValueError, TypeError) as e:
                    logging.warning(f"计算规则值颜色标记时出错: {e}")
                
                # 创建主队行
                self.interval_tree.insert("", tk.END, values=(
                    bookmaker,
                    "主队",
                    current_gap_diff,
                    home_type,
                    home_rule_value,
                    rule_diff,
                    first_choice
                ), tags=(home_tag,) if home_tag else ())
                
                # 创建客队行
                self.interval_tree.insert("", tk.END, values=(
                    "",  # 留空，与主队合并显示
                    "客队",
                    "",  # 留空，与主队合并显示
                    away_type,
                    away_rule_value,
                    "",  # 留空，与主队合并显示
                    ""   # 留空，与主队合并显示
                ), tags=(away_tag,) if away_tag else ())
                
                # 添加分隔行（更细）
                self.interval_tree.insert("", tk.END, values=("", "", "", "", "", "", ""), tags=('separator',))
                
                # 如果是澳门，添加马会即时赔率
                if "澳门" in bookmaker or "Macau" in bookmaker:
                    # 查找马会数据
                    hkjc_bookmaker = None
                    for b in odds_dict.keys():
                        if b and ("马会" in b or "HKJC" in b or "香港" in b):
                            hkjc_bookmaker = b
                            break
                    
                    if hkjc_bookmaker:
                        hkjc_data = odds_dict[hkjc_bookmaker]
                        # 使用马会即时赔率
                        hkjc_home_win = hkjc_data.get('instant_home_win')
                        hkjc_draw = hkjc_data.get('instant_draw')
                        hkjc_away_win = hkjc_data.get('instant_away_win')
                        hkjc_payout_rate = hkjc_data.get('payout_rate')
                        
                        if hkjc_home_win and hkjc_draw and hkjc_away_win:
                            # 计算满水赔率
                            hkjc_home_win_odds = self.odds_analyzer.calculate_true_odds(hkjc_home_win, hkjc_payout_rate)
                            hkjc_draw_true = self.odds_analyzer.calculate_true_odds(hkjc_draw, hkjc_payout_rate)
                            hkjc_away_win_odds = self.odds_analyzer.calculate_true_odds(hkjc_away_win, hkjc_payout_rate)
                            
                            # 查找对应的档距
                            hkjc_home_gap = None
                            hkjc_away_gap = None
                            if hkjc_home_win_odds:
                                hkjc_home_gap = self.odds_analyzer.find_gap_for_odds(hkjc_home_win_odds, "win")
                            if hkjc_away_win_odds:
                                hkjc_away_gap = self.odds_analyzer.find_gap_for_odds(hkjc_away_win_odds, "loss")
                            
                            # 计算主队区间
                            hkjc_home_interval = self.interval_analyzer.find_interval_for_gap(hkjc_home_gap, current_gap_diff, True)
                            hkjc_home_type = hkjc_home_interval.get('interval_type', '未知')
                            hkjc_home_rule_value = hkjc_home_interval.get('rule_value', '未知')
                            
                            # 计算客队区间
                            hkjc_away_interval = self.interval_analyzer.find_interval_for_gap(hkjc_away_gap, current_gap_diff, False)
                            hkjc_away_type = hkjc_away_interval.get('interval_type', '未知')
                            hkjc_away_rule_value = hkjc_away_interval.get('rule_value', '未知')
                            
                            # 计算规差值
                            hkjc_rule_diff = None
                            if hkjc_home_rule_value != '未知' and hkjc_away_rule_value != '未知':
                                try:
                                    hkjc_rule_diff = float(hkjc_home_rule_value) - float(hkjc_away_rule_value)
                                except (ValueError, TypeError):
                                    pass
                            
                            # 获取首选推荐
                            hkjc_first_choice = self.interval_analyzer.get_first_choice(hkjc_rule_diff, current_gap_diff)
                            
                            # 确定标签
                            hkjc_home_tag = None
                            hkjc_away_tag = None
                            
                            try:
                                # 处理主队规则值
                                if hkjc_home_rule_value != "未知":
                                    hkjc_home_rule_float = float(hkjc_home_rule_value)
                                    if hkjc_home_rule_float <= 3:
                                        hkjc_home_tag = 'home_good'
                                    elif hkjc_home_rule_float <= 4.5:
                                        hkjc_home_tag = 'home_normal'
                                    elif hkjc_home_rule_float <= 6:
                                        hkjc_home_tag = 'home_bad'
                                    else:
                                        hkjc_home_tag = 'home_very_bad'
                                
                                # 处理客队规则值
                                if hkjc_away_rule_value != "未知":
                                    hkjc_away_rule_float = float(hkjc_away_rule_value)
                                    if hkjc_away_rule_float <= 3:
                                        hkjc_away_tag = 'away_good'
                                    elif hkjc_away_rule_float <= 4.5:
                                        hkjc_away_tag = 'away_normal'
                                    elif hkjc_away_rule_float <= 6:
                                        hkjc_away_tag = 'away_bad'
                                    else:
                                        hkjc_away_tag = 'away_very_bad'
                                        
                                # 打印日志便于调试
                                logging.debug(f"规则值：主队={hkjc_home_rule_value}({hkjc_home_tag})，客队={hkjc_away_rule_value}({hkjc_away_tag})")
                                
                            except (ValueError, TypeError) as e:
                                logging.warning(f"计算规则值颜色标记时出错: {e}")
                            
                            # 创建主队行
                            self.interval_tree.insert("", tk.END, values=(
                                f"{hkjc_bookmaker}(即时)",
                                "主队",
                                current_gap_diff,
                                hkjc_home_type,
                                hkjc_home_rule_value,
                                hkjc_rule_diff,
                                hkjc_first_choice
                            ), tags=(hkjc_home_tag,) if hkjc_home_tag else ())
                            
                            # 创建客队行
                            self.interval_tree.insert("", tk.END, values=(
                                "",  # 留空，与主队合并显示
                                "客队",
                                "",  # 留空，与主队合并显示
                                hkjc_away_type,
                                hkjc_away_rule_value,
                                "",  # 留空，与主队合并显示
                                ""   # 留空，与主队合并显示
                            ), tags=(hkjc_away_tag,) if hkjc_away_tag else ())
                            
                            # 添加分隔行（更细）
                            self.interval_tree.insert("", tk.END, values=("", "", "", "", "", "", ""), tags=('separator',))
                
                # 平赔区间分析
                if home_win_odds and away_win_odds and draw_true:
                    # 计算标准平赔
                    standard_draw = self.odds_analyzer.find_standard_draw_odds(home_win_odds, away_win_odds)
                    
                    # 计算平赔区间
                    draw_intervals = self.odds_analyzer.calculate_draw_odds_intervals(standard_draw)
                    
                    # 确定实际平赔所在区间
                    draw_interval_type = self.odds_analyzer.determine_draw_interval(draw_true, draw_intervals)
                    
                    # 添加到平赔区间分析表格
                    self.draw_interval_tree.insert("", tk.END, values=(
                        bookmaker,
                        f"{standard_draw:.3f}" if standard_draw else "未知",
                        f"{draw_true:.3f}" if draw_true else "未知",
                        draw_interval_type
                    ))
                
        except Exception as e:
            logging.error(f"更新区间分析数据时出错: {e}")
    
    def clear(self):
        """清除显示的数据"""
        for item in self.interval_tree.get_children():
            self.interval_tree.delete(item)
        for item in self.draw_interval_tree.get_children():
            self.draw_interval_tree.delete(item)