"""
缓存管理器
提供多层次、多类型的缓存解决方案
"""

import time
import threading
import pickle
import os
import json
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime, timedelta
from functools import wraps
from pathlib import Path
from collections import OrderedDict
from enum import Enum

from .config_manager import config_manager
from .logger_manager import get_logger
from .exception_handler import handle_exceptions


class CacheType(Enum):
    """缓存类型"""
    MEMORY = "memory"
    DISK = "disk"
    HYBRID = "hybrid"


class CachePolicy(Enum):
    """缓存策略"""
    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最不常用
    FIFO = "fifo"  # 先进先出
    TTL = "ttl"  # 基于时间


class CacheEntry:
    """缓存条目"""
    
    def __init__(self, key: str, value: Any, ttl: int = None):
        self.key = key
        self.value = value
        self.created_time = time.time()
        self.last_access_time = time.time()
        self.access_count = 1
        self.ttl = ttl
        self.size = self._calculate_size(value)
    
    def _calculate_size(self, value: Any) -> int:
        """计算值的大小（字节）"""
        try:
            return len(pickle.dumps(value))
        except:
            return len(str(value).encode('utf-8'))
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_time > self.ttl
    
    def touch(self):
        """更新访问时间和计数"""
        self.last_access_time = time.time()
        self.access_count += 1


class MemoryCache:
    """内存缓存"""
    
    def __init__(self, max_size: int = 1000, max_memory: int = 100 * 1024 * 1024,
                 policy: CachePolicy = CachePolicy.LRU):
        """
        初始化内存缓存
        
        Args:
            max_size: 最大条目数
            max_memory: 最大内存使用（字节）
            policy: 缓存策略
        """
        self.max_size = max_size
        self.max_memory = max_memory
        self.policy = policy
        self.cache = OrderedDict()
        self.lock = threading.RLock()
        self.logger = get_logger('memory_cache')
        
        # 统计信息
        self.hits = 0
        self.misses = 0
        self.evictions = 0
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                
                if entry.is_expired():
                    del self.cache[key]
                    self.misses += 1
                    return None
                
                entry.touch()
                
                # LRU策略：移动到末尾
                if self.policy == CachePolicy.LRU:
                    self.cache.move_to_end(key)
                
                self.hits += 1
                return entry.value
            else:
                self.misses += 1
                return None
    
    def set(self, key: str, value: Any, ttl: int = None):
        """设置缓存值"""
        with self.lock:
            entry = CacheEntry(key, value, ttl)
            
            # 检查是否需要清理空间
            self._ensure_capacity(entry.size)
            
            self.cache[key] = entry
            
            # LRU策略：移动到末尾
            if self.policy == CachePolicy.LRU:
                self.cache.move_to_end(key)
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.hits = 0
            self.misses = 0
            self.evictions = 0
    
    def _ensure_capacity(self, new_entry_size: int):
        """确保有足够的容量"""
        current_memory = sum(entry.size for entry in self.cache.values())
        
        # 检查内存限制
        while (len(self.cache) >= self.max_size or 
               current_memory + new_entry_size > self.max_memory):
            
            if not self.cache:
                break
            
            evicted_key = self._select_eviction_candidate()
            if evicted_key:
                evicted_entry = self.cache.pop(evicted_key)
                current_memory -= evicted_entry.size
                self.evictions += 1
            else:
                break
    
    def _select_eviction_candidate(self) -> Optional[str]:
        """选择要驱逐的候选项"""
        if not self.cache:
            return None
        
        if self.policy == CachePolicy.LRU:
            # 最近最少使用的在前面
            return next(iter(self.cache))
        elif self.policy == CachePolicy.LFU:
            # 最不常用的
            return min(self.cache.keys(), 
                      key=lambda k: self.cache[k].access_count)
        elif self.policy == CachePolicy.FIFO:
            # 先进先出
            return next(iter(self.cache))
        elif self.policy == CachePolicy.TTL:
            # 最早过期的
            return min(self.cache.keys(), 
                      key=lambda k: self.cache[k].created_time)
        else:
            return next(iter(self.cache))
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.hits + self.misses
            hit_rate = self.hits / total_requests if total_requests > 0 else 0
            total_memory = sum(entry.size for entry in self.cache.values())
            
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "memory_used": total_memory,
                "max_memory": self.max_memory,
                "hit_rate": hit_rate,
                "hits": self.hits,
                "misses": self.misses,
                "evictions": self.evictions,
                "policy": self.policy.value
            }


class DiskCache:
    """磁盘缓存"""
    
    def __init__(self, cache_dir: str = None, max_size: int = 10000):
        """
        初始化磁盘缓存
        
        Args:
            cache_dir: 缓存目录
            max_size: 最大文件数
        """
        self.cache_dir = Path(cache_dir or config_manager.get('paths.temp_dir')) / "cache"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size = max_size
        self.lock = threading.RLock()
        self.logger = get_logger('disk_cache')
        
        # 元数据文件
        self.metadata_file = self.cache_dir / "metadata.json"
        self.metadata = self._load_metadata()
    
    def _load_metadata(self) -> Dict[str, Dict[str, Any]]:
        """加载元数据"""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"加载缓存元数据失败: {e}")
        return {}
    
    def _save_metadata(self):
        """保存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存缓存元数据失败: {e}")
    
    def _get_file_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        import hashlib
        hash_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{hash_key}.cache"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key not in self.metadata:
                return None
            
            meta = self.metadata[key]
            
            # 检查是否过期
            if meta.get('ttl') and time.time() - meta['created_time'] > meta['ttl']:
                self.delete(key)
                return None
            
            file_path = self._get_file_path(key)
            
            try:
                with open(file_path, 'rb') as f:
                    value = pickle.load(f)
                
                # 更新访问时间
                meta['last_access_time'] = time.time()
                meta['access_count'] = meta.get('access_count', 0) + 1
                self._save_metadata()
                
                return value
            except Exception as e:
                self.logger.error(f"读取缓存文件失败: {e}")
                self.delete(key)
                return None
    
    def set(self, key: str, value: Any, ttl: int = None):
        """设置缓存值"""
        with self.lock:
            file_path = self._get_file_path(key)
            
            try:
                # 确保有足够空间
                self._ensure_capacity()
                
                # 写入文件
                with open(file_path, 'wb') as f:
                    pickle.dump(value, f)
                
                # 更新元数据
                self.metadata[key] = {
                    'created_time': time.time(),
                    'last_access_time': time.time(),
                    'access_count': 1,
                    'ttl': ttl,
                    'file_size': file_path.stat().st_size
                }
                
                self._save_metadata()
                
            except Exception as e:
                self.logger.error(f"写入缓存文件失败: {e}")
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self.lock:
            if key not in self.metadata:
                return False
            
            file_path = self._get_file_path(key)
            
            try:
                if file_path.exists():
                    file_path.unlink()
                del self.metadata[key]
                self._save_metadata()
                return True
            except Exception as e:
                self.logger.error(f"删除缓存文件失败: {e}")
                return False
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            for key in list(self.metadata.keys()):
                self.delete(key)
    
    def _ensure_capacity(self):
        """确保有足够的容量"""
        while len(self.metadata) >= self.max_size:
            # 删除最久未访问的文件
            oldest_key = min(
                self.metadata.keys(),
                key=lambda k: self.metadata[k]['last_access_time']
            )
            self.delete(oldest_key)


class CacheManager:
    """统一缓存管理器"""
    
    def __init__(self):
        self.memory_cache = MemoryCache()
        self.disk_cache = DiskCache()
        self.logger = get_logger('cache_manager')
        
        # 缓存策略配置
        self.cache_strategies = {
            'match_data': {'type': CacheType.MEMORY, 'ttl': 3600},  # 1小时
            'odds_analysis': {'type': CacheType.MEMORY, 'ttl': 1800},  # 30分钟
            'prediction_results': {'type': CacheType.DISK, 'ttl': 86400},  # 1天
            'team_stats': {'type': CacheType.HYBRID, 'ttl': 7200},  # 2小时
        }
    
    def get(self, key: str, category: str = 'default') -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            category: 缓存类别
            
        Returns:
            缓存值或None
        """
        strategy = self.cache_strategies.get(category, {
            'type': CacheType.MEMORY, 
            'ttl': 3600
        })
        
        cache_type = strategy['type']
        
        if cache_type == CacheType.MEMORY:
            return self.memory_cache.get(key)
        elif cache_type == CacheType.DISK:
            return self.disk_cache.get(key)
        elif cache_type == CacheType.HYBRID:
            # 先尝试内存，再尝试磁盘
            value = self.memory_cache.get(key)
            if value is None:
                value = self.disk_cache.get(key)
                # 将磁盘中的值加载到内存
                if value is not None:
                    self.memory_cache.set(key, value, strategy['ttl'])
            return value
        
        return None
    
    def set(self, key: str, value: Any, category: str = 'default', ttl: int = None):
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            category: 缓存类别
            ttl: 生存时间（秒）
        """
        strategy = self.cache_strategies.get(category, {
            'type': CacheType.MEMORY, 
            'ttl': 3600
        })
        
        cache_type = strategy['type']
        cache_ttl = ttl or strategy['ttl']
        
        if cache_type == CacheType.MEMORY:
            self.memory_cache.set(key, value, cache_ttl)
        elif cache_type == CacheType.DISK:
            self.disk_cache.set(key, value, cache_ttl)
        elif cache_type == CacheType.HYBRID:
            # 同时存储到内存和磁盘
            self.memory_cache.set(key, value, cache_ttl)
            self.disk_cache.set(key, value, cache_ttl)
    
    def delete(self, key: str, category: str = 'default') -> bool:
        """删除缓存条目"""
        strategy = self.cache_strategies.get(category, {
            'type': CacheType.MEMORY
        })
        
        cache_type = strategy['type']
        
        if cache_type == CacheType.MEMORY:
            return self.memory_cache.delete(key)
        elif cache_type == CacheType.DISK:
            return self.disk_cache.delete(key)
        elif cache_type == CacheType.HYBRID:
            # 从两个缓存中都删除
            mem_result = self.memory_cache.delete(key)
            disk_result = self.disk_cache.delete(key)
            return mem_result or disk_result
        
        return False
    
    def clear(self, category: str = None):
        """清空缓存"""
        if category is None:
            # 清空所有缓存
            self.memory_cache.clear()
            self.disk_cache.clear()
        else:
            # 根据类别清空对应的缓存
            strategy = self.cache_strategies.get(category)
            if strategy:
                cache_type = strategy['type']
                if cache_type == CacheType.MEMORY:
                    self.memory_cache.clear()
                elif cache_type == CacheType.DISK:
                    self.disk_cache.clear()
                elif cache_type == CacheType.HYBRID:
                    self.memory_cache.clear()
                    self.disk_cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'memory_cache': self.memory_cache.get_stats(),
            'disk_cache': {
                'size': len(self.disk_cache.metadata),
                'max_size': self.disk_cache.max_size
            }
        }


# 缓存装饰器
def cached(category: str = 'default', ttl: int = None, key_func: Callable = None):
    """
    缓存装饰器
    
    Args:
        category: 缓存类别
        ttl: 生存时间
        key_func: 自定义键生成函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash((args, tuple(sorted(kwargs.items()))))}"
            
            # 尝试从缓存获取
            cached_result = cache_manager.get(cache_key, category)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, category, ttl)
            
            return result
        
        return wrapper
    return decorator


# 全局缓存管理器实例
cache_manager = CacheManager()


def warm_up_cache():
    """预热缓存"""
    try:
        # 可以在这里预加载一些常用数据
        cache_manager.logger.info("缓存预热完成")
    except Exception as e:
        cache_manager.logger.error(f"缓存预热失败: {e}")


def cleanup_expired_cache():
    """清理过期缓存"""
    try:
        # 这里可以添加定期清理逻辑
        cache_manager.logger.info("过期缓存清理完成")
    except Exception as e:
        cache_manager.logger.error(f"清理过期缓存失败: {e}")