#!/usr/bin/env python3
"""
测试改进后的历史赔率区间分析图表功能

改进内容：
1. 合并平局区间与主客区间到一个图表
2. 使用红黄蓝颜色区分胜平负
3. 简化Y轴标签，只显示大区间
4. 点击时显示详细区间信息
5. 修复注释被边框遮挡的问题
"""

import sys
import os
import logging
import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
import random

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from football_analysis_system.analysis.historical_chart_visualizer import HistoricalChartVisualizer
from football_analysis_system.analysis.interval_visualizer import VisualizationData

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_improved_test_data():
    """创建改进的测试数据"""
    logger.info("创建改进的测试数据...")
    
    # 生成测试时间序列
    base_time = datetime.now() - timedelta(hours=12)
    timestamps = []
    for i in range(15):  # 15个数据点
        timestamp = base_time + timedelta(minutes=i * 45)
        timestamps.append(timestamp.strftime('%Y-%m-%d %H:%M:%S'))
    
    # 生成测试Y坐标数据，模拟真实的区间分布
    home_y_coords = []
    draw_y_coords = []
    away_y_coords = []
    
    # 模拟区间变化趋势
    for i in range(len(timestamps)):
        # 主胜区间：1-10
        home_y = 2 + int(3 * (1 + 0.3 * random.random()))
        home_y_coords.append(home_y)
        
        # 平局区间：11-20
        draw_y = 12 + int(4 * (1 + 0.2 * random.random()))
        draw_y_coords.append(draw_y)
        
        # 客胜区间：21-30
        away_y = 22 + int(3 * (1 + 0.4 * random.random()))
        away_y_coords.append(away_y)
    
    # 创建详细的区间标签映射
    interval_labels = {}
    
    # 主胜区间标签（详细）
    interval_labels[1] = "主胜超低水1档"
    interval_labels[2] = "主胜超低水2档"
    interval_labels[3] = "主胜低水1档"
    interval_labels[4] = "主胜低水2档"
    interval_labels[5] = "主胜中水1档"
    interval_labels[6] = "主胜中水2档"
    interval_labels[7] = "主胜高水1档"
    interval_labels[8] = "主胜高水2档"
    interval_labels[9] = "主胜超高水1档"
    interval_labels[10] = "主胜超高水2档"
    
    # 平局区间标签（详细）
    interval_labels[11] = "平局超低水1档"
    interval_labels[12] = "平局超低水2档"
    interval_labels[13] = "平局低水1档"
    interval_labels[14] = "平局低水2档"
    interval_labels[15] = "平局中水1档"
    interval_labels[16] = "平局中水2档"
    interval_labels[17] = "平局高水1档"
    interval_labels[18] = "平局高水2档"
    interval_labels[19] = "平局超高水1档"
    interval_labels[20] = "平局超高水2档"
    
    # 客胜区间标签（详细）
    interval_labels[21] = "客胜超低水1档"
    interval_labels[22] = "客胜超低水2档"
    interval_labels[23] = "客胜低水1档"
    interval_labels[24] = "客胜低水2档"
    interval_labels[25] = "客胜中水1档"
    interval_labels[26] = "客胜中水2档"
    interval_labels[27] = "客胜高水1档"
    interval_labels[28] = "客胜高水2档"
    interval_labels[29] = "客胜超高水1档"
    interval_labels[30] = "客胜超高水2档"
    
    # 创建可视化数据对象
    visualization_data = VisualizationData(
        timestamps=timestamps,
        home_y_coords=home_y_coords,
        draw_y_coords=draw_y_coords,
        away_y_coords=away_y_coords,
        interval_labels=interval_labels
    )
    
    logger.info(f"创建了包含 {len(timestamps)} 个数据点的改进测试数据")
    return visualization_data

def test_improved_chart():
    """测试改进后的图表功能"""
    logger.info("开始测试改进后的图表功能...")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("历史赔率区间分析 - 改进功能测试")
    root.geometry("1400x800")
    
    # 创建主框架
    main_frame = tk.Frame(root, bg="#F5F5F5")
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 创建说明区域
    info_frame = tk.LabelFrame(main_frame, text="功能改进说明", 
                              bg="#F5F5F5", font=("Arial", 10, "bold"))
    info_frame.pack(fill=tk.X, pady=(0, 10))
    
    info_text = """
    改进内容：
    ✓ 合并显示：平局区间与主客区间合并到一个图表中
    ✓ 颜色优化：使用红色(主胜)、黄色(平局)、蓝色(客胜)进行区分
    ✓ 简化标签：Y轴只显示大区间(低水区、中水区、高水区)，不显示具体档位
    ✓ 交互增强：点击数据点显示详细区间信息，悬停显示简化信息
    ✓ 布局优化：增加边距避免注释被边框遮挡
    
    操作说明：
    • 鼠标悬停在数据点上查看简化信息
    • 点击数据点查看详细区间信息
    • 垂直虚线标记所有时间点
    • 绿色实线标记开盘时间，红色实线标记收盘时间
    """
    
    info_label = tk.Label(info_frame, text=info_text, 
                         justify=tk.LEFT, bg="#F5F5F5",
                         font=("Arial", 9))
    info_label.pack(anchor=tk.W, padx=10, pady=5)
    
    # 创建图表区域
    chart_frame = tk.LabelFrame(main_frame, text="历史赔率区间变化趋势", 
                               bg="#F5F5F5", font=("Arial", 10, "bold"))
    chart_frame.pack(fill=tk.BOTH, expand=True)
    
    # 创建图表可视化器
    chart_visualizer = HistoricalChartVisualizer()
    
    # 创建画布
    canvas = chart_visualizer.create_canvas(chart_frame)
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 创建测试数据
    test_data = create_improved_test_data()
    
    # 绘制改进后的图表
    logger.info("绘制改进后的合并图表...")
    success = chart_visualizer.plot_historical_intervals(test_data, "改进测试博彩公司")
    
    if success:
        logger.info("✓ 合并图表显示功能正常")
        logger.info("✓ 红黄蓝颜色区分已应用")
        logger.info("✓ 简化Y轴标签已实现")
        logger.info("✓ 交互式功能已启用")
        logger.info("✓ 布局优化已完成")
        
        # 添加控制按钮
        button_frame = tk.Frame(main_frame, bg="#F5F5F5")
        button_frame.pack(fill=tk.X, pady=5)
        
        def refresh_data():
            """刷新测试数据"""
            new_data = create_improved_test_data()
            chart_visualizer.plot_historical_intervals(new_data, "刷新测试数据")
        
        def clear_chart():
            """清除图表"""
            chart_visualizer.clear_chart()
        
        refresh_btn = tk.Button(button_frame, text="刷新数据", 
                               command=refresh_data,
                               bg="#28A745", fg="white", 
                               font=("Arial", 9, "bold"),
                               relief=tk.FLAT, padx=20)
        refresh_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = tk.Button(button_frame, text="清除图表", 
                             command=clear_chart,
                             bg="#DC3545", fg="white", 
                             font=("Arial", 9, "bold"),
                             relief=tk.FLAT, padx=20)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
    else:
        logger.error("✗ 改进后的图表绘制失败")
    
    # 运行测试窗口
    logger.info("启动改进功能测试窗口，请手动测试交互功能...")
    root.mainloop()

def test_color_and_interaction():
    """专门测试颜色和交互功能"""
    logger.info("开始测试颜色和交互功能...")
    
    try:
        # 创建简单的测试窗口
        root = tk.Tk()
        root.title("颜色和交互测试")
        root.geometry("1200x600")
        
        # 创建图表
        chart_visualizer = HistoricalChartVisualizer()
        canvas = chart_visualizer.create_canvas(root)
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建简单的测试数据
        timestamps = []
        base_time = datetime.now()
        for i in range(8):
            timestamp = base_time + timedelta(hours=i)
            timestamps.append(timestamp.strftime('%Y-%m-%d %H:%M:%S'))
        
        # 创建测试数据
        test_data = VisualizationData(
            timestamps=timestamps,
            home_y_coords=[3, 4, 2, 5, 3, 4, 2, 3],  # 主胜
            draw_y_coords=[13, 15, 12, 16, 14, 15, 13, 14],  # 平局
            away_y_coords=[23, 25, 22, 26, 24, 25, 23, 24],  # 客胜
            interval_labels={
                2: "主胜超低水", 3: "主胜低水", 4: "主胜中水", 5: "主胜高水",
                12: "平局超低水", 13: "平局低水", 14: "平局中水", 15: "平局高水", 16: "平局超高水",
                22: "客胜超低水", 23: "客胜低水", 24: "客胜中水", 25: "客胜高水", 26: "客胜超高水"
            }
        )
        
        # 绘制图表
        success = chart_visualizer.plot_historical_intervals(test_data, "颜色交互测试")
        
        if success:
            logger.info("✓ 红黄蓝颜色方案测试通过")
            logger.info("✓ 交互功能测试准备就绪")
            
            # 添加测试说明
            info_label = tk.Label(root, 
                                 text="测试说明：红色=主胜，黄色=平局，蓝色=客胜\n请点击和悬停测试交互功能",
                                 bg="white", font=("Arial", 10))
            info_label.pack(pady=5)
        
        root.mainloop()
        
    except Exception as e:
        logger.error(f"颜色和交互测试失败: {e}")

def main():
    """主测试函数"""
    print("=" * 70)
    print("历史赔率区间分析 - 改进功能测试")
    print("=" * 70)
    
    try:
        # 测试1：改进后的图表功能
        print("\n1. 测试改进后的合并图表功能...")
        test_improved_chart()
        
        # 测试2：颜色和交互功能
        print("\n2. 测试颜色和交互功能...")
        test_color_and_interaction()
        
        print("\n" + "=" * 70)
        print("改进功能测试完成！")
        print("主要改进：")
        print("✓ 合并显示胜平负区间")
        print("✓ 红黄蓝颜色区分")
        print("✓ 简化Y轴标签")
        print("✓ 详细交互信息")
        print("✓ 优化布局避免遮挡")
        print("=" * 70)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()