#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立测试脚本 - 分析var game参数内容
"""

import time
import requests
import re

def test_game_content():
    """测试获取和分析var game内容"""
    
    print("=== 独立测试 var game 内容 ===")
    
    # 直接定义配置，避免循环导入
    match_id = "2696009"  # 从JSON文件中找到的比赛ID
    
    # 生成时间戳参数
    timestamp_part = str(int(time.time() * 1000))
    if len(timestamp_part) > 13:
        timestamp_part = timestamp_part[-13:]
    else:
        timestamp_part = timestamp_part.ljust(13, '0')
    timestamp = "007" + timestamp_part
    
    # 构建URL
    url = f"https://1x2d.titan007.com/{match_id}.js?r={timestamp}"
    print(f"请求URL: {url}")
    
    # 构建请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Referer': f'https://op1.titan007.com/oddslist/{match_id}.htm',
    }
    
    try:
        print("\n正在发送请求...")
        response = requests.get(url, headers=headers, timeout=15)
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            content = response.text
            
            # 保存完整响应到文件，方便查看
            with open("game_response.txt", "w", encoding="utf-8") as f:
                f.write(content)
            print("✅ 完整响应已保存到 game_response.txt")
            
            print(f"\n响应内容前500字符:")
            print("-" * 50)
            print(content[:500])
            print("-" * 50)
            
            # 检查是否包含var game
            if "var game" in content:
                print("\n✅ 找到 'var game' 内容！")
                
                # 提取game数组内容
                game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', content, re.DOTALL)
                if game_match:
                    game_data = game_match.group(1)
                    print(f"✅ 成功提取game数组，长度: {len(game_data)} 字符")
                    
                    # 保存game数据到文件
                    with open("game_data.txt", "w", encoding="utf-8") as f:
                        f.write(game_data)
                    print("✅ game数组数据已保存到 game_data.txt")
                    
                    print(f"\ngame数组前300字符:")
                    print("-" * 50)
                    print(game_data[:300])
                    print("-" * 50)
                    
                    # 提取所有记录
                    company_records = re.findall(r'"([^"]*)"', game_data)
                    print(f"\n✅ 找到 {len(company_records)} 条公司记录")
                    
                    # 分析前5条记录
                    print(f"\n=== 分析前5条记录 ===")
                    for i, record in enumerate(company_records[:5]):
                        print(f"\n--- 记录 {i+1} ---")
                        fields = record.split('|')
                        print(f"字段总数: {len(fields)}")
                        print(f"完整记录: {record}")
                        
                        print("关键字段解析:")
                        if len(fields) > 0:
                            print(f"  [0] 公司ID: {fields[0]}")
                        if len(fields) > 2:
                            print(f"  [2] 公司名称: {fields[2]}")
                        if len(fields) > 3:
                            print(f"  [3] 初始主胜: {fields[3]}")
                        if len(fields) > 4:
                            print(f"  [4] 初始平局: {fields[4]}")
                        if len(fields) > 5:
                            print(f"  [5] 初始客胜: {fields[5]}")
                        if len(fields) > 9:
                            print(f"  [9] 初始返还率: {fields[9]}")
                        if len(fields) > 10:
                            print(f"  [10] 即时主胜: {fields[10]}")
                        if len(fields) > 11:
                            print(f"  [11] 即时平局: {fields[11]}")
                        if len(fields) > 12:
                            print(f"  [12] 即时客胜: {fields[12]}")
                        if len(fields) > 16:
                            print(f"  [16] 即时返还率: {fields[16]}")
                            
                        # 显示所有字段（如果字段不太多）
                        if len(fields) <= 25:
                            print("  所有字段:")
                            for idx, field in enumerate(fields):
                                print(f"    [{idx:2d}] {field}")
                        else:
                            print(f"  （字段太多，只显示前20个）")
                            for idx, field in enumerate(fields[:20]):
                                print(f"    [{idx:2d}] {field}")
                    
                    # 保存详细分析到文件
                    with open("game_analysis.txt", "w", encoding="utf-8") as f:
                        f.write(f"比赛ID: {match_id}\n")
                        f.write(f"总记录数: {len(company_records)}\n\n")
                        
                        for i, record in enumerate(company_records):
                            f.write(f"=== 记录 {i+1} ===\n")
                            fields = record.split('|')
                            f.write(f"字段数: {len(fields)}\n")
                            f.write(f"原始记录: {record}\n")
                            f.write("字段解析:\n")
                            for idx, field in enumerate(fields):
                                f.write(f"  [{idx:2d}] {field}\n")
                            f.write("\n")
                    
                    print(f"\n✅ 详细分析已保存到 game_analysis.txt")
                    
                else:
                    print("❌ 无法提取game数组内容")
                    
            else:
                print("❌ 响应中没有找到 'var game'")
                print("让我们看看响应中包含什么变量:")
                var_matches = re.findall(r'var\s+(\w+)\s*=', content)
                if var_matches:
                    print(f"找到的变量: {', '.join(set(var_matches))}")
                else:
                    print("没有找到任何JavaScript变量")
                
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ 请求出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_game_content() 