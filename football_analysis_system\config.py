"""
统一配置模块 - 使用统一配置管理器
此文件现在作为配置的统一入口点，所有配置都通过ConfigManager获取
"""

import os
from football_analysis_system.core.config_manager import config_manager

# --- UI配置 ---
# 字体配置
FONT_NORMAL = ("Segoe UI", 10)
FONT_LARGE = ("Segoe UI", 12, "bold")
FONT_HEADER = ("Segoe UI", 18, "bold")
FONT_SUBHEADER = ("Segoe UI", 14, "bold")

# 色彩方案 - 从配置管理器获取
_ui_colors = config_manager.get('ui.colors', {})
COLOR_PRIMARY = _ui_colors.get('primary', "#1a237e")
COLOR_SECONDARY = _ui_colors.get('secondary', "#3949ab")
COLOR_ACCENT = _ui_colors.get('accent', "#00c853")
COLOR_BG = _ui_colors.get('background', "#f5f5f7")
COLOR_TEXT = _ui_colors.get('text', "#212121")
COLOR_TEXT_LIGHT = "#757575"  
COLOR_HOME = "#1e88e5"        
COLOR_AWAY = "#e53935"        
COLOR_DRAW = "#fb8c00"        
COLOR_WARNING = _ui_colors.get('warning', "#f44336")
COLOR_HIGHLIGHT = "#ffb300"   
COLOR_SUCCESS = _ui_colors.get('success', "#27ae60")

# 路径配置 - 从配置管理器获取
PROJECT_ROOT = config_manager.get('paths.project_root')
DATA_DIR = config_manager.get('paths.data_dir')

# 数据库路径 - 使用PathResolver统一管理
from football_analysis_system.core.path_resolver import PathResolver

DB_MATCHES = PathResolver.get_database_path('matches_and_odds.db')
DB_TEAMS = PathResolver.get_database_path('team_database.db')
DB_ODDS_INTERVALS = PathResolver.get_database_path('odds_database.db')
DB_STRENGTH_MATCHUP = PathResolver.get_database_path('sports_database.db')
DB_WILLIAM_HILL_TEAM = PathResolver.get_database_path('WilliamHillTeam.db')
DB_STANDARD_ODDS = PathResolver.get_database_path('StandardOdds.db')
DB_GUANGYISHILI = PathResolver.get_database_path('guangyishili.db')

# 爬虫配置 - 从配置管理器获取
DATA_URL_TEMPLATE = "https://livestatic.titan007.com/vbsxml/bfdata_ut.js?r={timestamp}"
MATCH_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
    'Referer': 'https://live.titan007.com/index2in1.aspx?',
}

ODDS_URL_TEMPLATE = "https://1x2d.titan007.com/{match_id}.js?r={timestamp}"
ODDS_HEADERS_TEMPLATE = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
    'Referer': 'https://op1.titan007.com/oddslist/{match_id}.htm',
}

# 目标博彩公司 - 从配置管理器获取
_companies = config_manager.get('companies', {})
TARGET_COMPANIES = [
    _companies.get('115', {}).get('cn', '威廉希尔'),
    _companies.get('90', {}).get('cn', '易胜博'), 
    _companies.get('432', {}).get('cn', '香港马会'),
    _companies.get('80', {}).get('cn', '澳门'),
    'BWIN', '伟德', 'Nordicbet', '利记', '18BET', 'BetISn', 'iddaa'
]

# API配置 - 从配置管理器获取
_api_config = config_manager.get('api.deepseek', {})
DEEPSEEK_API_KEY = _api_config.get('api_key')
DEEPSEEK_API_URL = _api_config.get('api_url', "https://api.deepseek.com/v1/chat/completions")
DEEPSEEK_MODEL = _api_config.get('model', "deepseek-reasoner")

# 安全检查：如果API密钥为空，给出警告
if not DEEPSEEK_API_KEY:
    import warnings
    warnings.warn(
        "DEEPSEEK_API_KEY环境变量未设置！请设置API密钥：\n"
        "Windows: set DEEPSEEK_API_KEY=your_api_key_here\n"
        "Linux/Mac: export DEEPSEEK_API_KEY=your_api_key_here",
        UserWarning
    )

# 应用信息 - 从配置管理器获取
_app_config = config_manager.get('app', {})
APP_TITLE = _app_config.get('title', "足球比赛分析系统 Plus")
APP_VERSION = _app_config.get('version', "v4.0 现代版")