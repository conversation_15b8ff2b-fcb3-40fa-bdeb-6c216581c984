"""
足球分析系统 - 主包初始化文件

这是一个综合性的足球比赛分析系统，包含以下主要功能：
- 比赛数据爬取和管理
- 赔率分析和预测
- 球队实力评估
- 泊松分布建模
- 现代化用户界面

主要模块：
- models: 数据模型（比赛、球队、赔率等）
- db: 数据库操作
- analysis: 分析算法
- scrapers: 数据爬取
- ui: 用户界面
- utils: 工具函数
- core: 核心功能
"""

__version__ = "4.0.0"
__author__ = "Football Analysis System Team"
__description__ = "足球比赛分析系统 - 综合性足球数据分析平台"

# 导入主要组件
from .ui.app import FootballAnalysisApp
from .ui.modern_app import ModernFootballAnalysisApp

# 导入核心模型
from .models import Match, Team, Odds
from .db import DatabaseManager, MatchDatabase, TeamDatabase
from .analysis import OddsAnalyzer, IntervalAnalyzer

# 导入配置
try:
    from .config import *
except ImportError:
    # 如果配置文件不存在，使用默认配置
    pass

__all__ = [
    # 主要应用类
    'FootballAnalysisApp',
    'ModernFootballAnalysisApp',
    
    # 核心模型
    'Match',
    'Team', 
    'Odds',
    
    # 数据库组件
    'DatabaseManager',
    'MatchDatabase',
    'TeamDatabase',
    
    # 分析组件
    'OddsAnalyzer',
    'IntervalAnalyzer',
]

def get_version():
    """获取版本信息"""
    return __version__

def get_description():
    """获取系统描述"""
    return __description__
