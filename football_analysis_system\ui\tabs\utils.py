import logging

def match_bookmaker(bookmaker, company_name):
    """
    检查博彩公司名称是否匹配

    Args:
        bookmaker: 实际的博彩公司名称
        company_name: 配置中的公司名称

    Returns:
        bool: 是否匹配
    """
    company_name = company_name.lower()
    bookmaker_lower = bookmaker.lower() if bookmaker else ""

    if company_name == "香港马会":
        return "马会" in bookmaker or "hkjc" in bookmaker_lower or "香港" in bookmaker
    elif company_name == "澳门":
        return "澳门" in bookmaker or "macau" in bookmaker_lower
    elif company_name == "威廉" or company_name == "威廉希尔":
        return "威廉" in bookmaker or "william" in bookmaker_lower
    elif company_name == "易胜博":
        return "易胜博" in bookmaker or "easybets" in bookmaker_lower
    elif company_name == "bwin" or company_name == "必赢":
        return "bwin" in bookmaker_lower or "必赢" in bookmaker
    elif company_name == "伟德" or company_name == "韦德":
        return "伟德" in bookmaker or "韦德" in bookmaker or "weide" in bookmaker_lower
    elif company_name == "coolbet":
        return "coolbet" in bookmaker_lower or bookmaker == "Coolbet"
    elif company_name == "iddaa":
        return "iddaa" in bookmaker_lower or bookmaker == "iddaa"
    elif company_name == "nordicbet":
        return "nordicbet" in bookmaker_lower or bookmaker == "Nordicbet"
    elif company_name == "利记" or company_name == "sb":
        return "利记" in bookmaker or "sb" in bookmaker_lower or bookmaker == "SB"
    elif company_name == "18bet":
        return "18bet" in bookmaker_lower or bookmaker == "18BET" or "18" in bookmaker
    else:
        return company_name in bookmaker_lower

def format_interval_type(interval_type):
    """
    格式化区间类型，对含有"超高水"和"超低水"的区间名称进行特殊处理
    
    Args:
        interval_type: 区间类型名称
        
    Returns:
        str: 格式化后的区间类型名称
    """
    if not interval_type or interval_type == "未知":
        return interval_type
        
    # 检查是否包含"超高水"或"超低水"
    if "超高水" in interval_type or "超低水" in interval_type:
        # 在tkinter树状图中无法直接使用HTML标签，所以使用特殊符号标记
        return f"★{interval_type}★"
    else:
        return interval_type