import tkinter as tk
from tkinter import ttk
import logging
from football_analysis_system.config import COLOR_BG, COLOR_PRIMARY, COLOR_SECONDARY, COLOR_TEXT, COLOR_ACCENT

class OddsRulesTab(tk.Frame):
    """赔率分析法则标签页"""

    def __init__(self, parent):
        """
        初始化赔率分析法则标签页

        Args:
            parent: 父容器
        """
        super().__init__(parent, bg=COLOR_BG)
        
        # 定义颜色常量
        self.COLOR_GREEN = "#2ECC71"
        self.COLOR_RED = "#E74C3C"
        self.COLOR_ORANGE = "#F39C12"
        self.COLOR_GRAY = "#95A5A6"
        self.COLOR_BLUE = "#3498DB"
        self.COLOR_PRIMARY = COLOR_PRIMARY
        
        self.COLOR_GREEN_LIGHT = "#EAFAF1"
        self.COLOR_RED_LIGHT = "#FDEDEC"
        self.COLOR_ORANGE_LIGHT = "#FEF5E7"
        self.COLOR_GRAY_LIGHT = "#F8F9F9"
        
        # 监听窗口大小变化
        self.bind("<Configure>", self.on_resize)
        
        # 创建标签页内容
        self.create_widgets()

    def create_widgets(self):
        """创建标签页控件"""
        # 标题框架
        title_frame = ttk.Frame(self, style="TFrame")
        title_frame.pack(fill=tk.X, padx=15, pady=(15, 5))
        
        # 大标题
        title_label = ttk.Label(
            title_frame, 
            text="赔率分析法则", 
            font=('思源黑体', 16, 'bold'),
            foreground=COLOR_PRIMARY, 
            background=COLOR_BG
        )
        title_label.pack(side=tk.LEFT)
        
        # 创建卡片容器 - 使用滚动区域
        self.canvas = tk.Canvas(self, bg=COLOR_BG, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas, style="TFrame")
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas_frame = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # 设置Canvas大小随窗口调整
        self.canvas.bind("<Configure>", self.on_canvas_configure)
        
        # 放置滚动区域
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=15, pady=5)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 绑定鼠标滚轮事件
        self.bind_mousewheel()
        
        # 创建卡片网格 - 使用Frame实现自适应网格布局
        self.cards_grid = ttk.Frame(self.scrollable_frame, style="TFrame")
        self.cards_grid.pack(fill=tk.BOTH, expand=True)
        
        # 创建卡片
        self.create_cards()
        
    def on_resize(self, event=None):
        """处理窗口大小变化"""
        if event and event.widget == self:
            # 获取当前宽度
            width = event.width
            # 计算每列宽度
            if width < 800:  # 小窗口时切换为单列布局
                self.update_layout(1)
            elif width < 1200:  # 中等窗口时切换为双列布局
                self.update_layout(2)
            else:  # 大窗口时保持三列布局
                self.update_layout(3)
            
    def update_layout(self, columns):
        """更新布局列数"""
        if hasattr(self, 'current_columns') and self.current_columns == columns:
            return
            
        self.current_columns = columns
        
        # 重新布局所有卡片
        for card_id, card_info in self.card_widgets.items():
            card_widget = card_info['widget']
            original_row = card_info['original_row']
            original_col = card_info['original_col']
            
            # 移除旧的网格布局
            card_widget.grid_forget()
            
            # 计算新位置
            if columns == 1:
                # 单列布局：所有卡片一列排列
                new_row = original_row * 3 + original_col
                new_col = 0
            elif columns == 2:
                # 双列布局：按原始顺序排列到2列
                if original_col == 2:  # 原来第三列的卡片
                    new_row = original_row + 1  # 移到下一行
                    new_col = original_row % 2  # 交替放在0和1列
                else:
                    new_row = original_row
                    new_col = original_col
            else:
                # 三列布局：恢复原始位置
                new_row = original_row
                new_col = original_col
            
            # 应用新布局
            card_widget.grid(row=new_row, column=new_col, padx=5, pady=5, sticky="nsew")
            
        # 配置网格权重
        for i in range(columns):
            self.cards_grid.columnconfigure(i, weight=1)
            
        # 记录更新后的视图，确保滚动区域正确显示
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            
    def on_canvas_configure(self, event=None):
        """处理Canvas大小变化"""
        # 调整Canvas中frame的宽度
        if event:
            self.canvas.itemconfig(self.canvas_frame, width=event.width)
    
    def bind_mousewheel(self):
        """绑定鼠标滚轮事件到滚动区域"""
        def _on_mousewheel(event):
            # 处理不同平台的滚轮事件
            if event.num == 4 or event.delta > 0:  # 向上滚动
                self.canvas.yview_scroll(-1, "units")
            elif event.num == 5 or event.delta < 0:  # 向下滚动
                self.canvas.yview_scroll(1, "units")
                
        # 绑定鼠标滚轮事件
        # Linux系统
        self.canvas.bind("<Button-4>", _on_mousewheel)
        self.canvas.bind("<Button-5>", _on_mousewheel)
        # Windows/Mac系统
        self.canvas.bind("<MouseWheel>", _on_mousewheel)
    
    def create_rule_card(self, parent, title, icon_color, content, card_id):
        """
        创建规则卡片
        
        Args:
            parent: 父容器
            title: 卡片标题
            icon_color: 图标颜色
            content: 卡片内容(HTML格式)
            card_id: 卡片ID，用于跟踪布局变化
        
        Returns:
            卡片框架
        """
        # 创建卡片框架
        card_frame = ttk.Frame(parent)
        card_frame.configure(style="Card.TFrame")
        
        # 卡片头部
        header_frame = ttk.Frame(card_frame, style="CardHeader.TFrame")
        header_frame.pack(fill=tk.X, ipady=5)
        
        # 图标和标题
        icon_frame = ttk.Frame(header_frame, width=24, height=24)
        icon_frame.pack(side=tk.LEFT, padx=(8, 3))
        # 保持大小固定
        icon_frame.pack_propagate(False)
        
        # 图标背景 - 创建一个彩色圆形
        icon_canvas = tk.Canvas(icon_frame, width=20, height=20, bg=COLOR_BG, highlightthickness=0)
        icon_canvas.pack(expand=True)
        icon_canvas.create_oval(2, 2, 18, 18, fill=icon_color, outline="")
        
        # 标题
        title_label = ttk.Label(
            header_frame, 
            text=title, 
            font=('思源黑体', 12, 'bold'),
            foreground=COLOR_TEXT,
            background=COLOR_BG
        )
        title_label.pack(side=tk.LEFT, padx=3)
        
        # 内容区域
        content_frame = ttk.Frame(card_frame, style="CardContent.TFrame")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建内容
        self.render_content(content_frame, content)
        
        # 存储卡片信息
        if not hasattr(self, 'card_widgets'):
            self.card_widgets = {}
        self.card_widgets[card_id] = {
            'widget': card_frame,
            'original_row': 0,
            'original_col': 0
        }
        
        return card_frame
    
    def render_content(self, parent, content_items):
        """
        渲染卡片内容
        
        Args:
            parent: 父容器
            content_items: 内容项列表
        """
        # 获取父容器宽度，用于设置文本自动换行
        parent_width = parent.winfo_width() or 300
        wraplength = max(250, parent_width - 20)
        
        for item in content_items:
            if item["type"] == "rule":
                # 规则条目 - 使用行框架
                rule_frame = ttk.Frame(parent)
                rule_frame.pack(fill=tk.X, pady=2, anchor=tk.W)
                
                # 规则文本
                rule_text = f"{item['text']} "
                rule_label = ttk.Label(
                    rule_frame, 
                    text=rule_text, 
                    font=('思源黑体', 10),
                    wraplength=wraplength,  # 设置自动换行，基于父容器宽度
                    justify=tk.LEFT
                )
                rule_label.pack(side=tk.LEFT, anchor=tk.NW)
                
                # 结论标签
                if "conclusion" in item:
                    # 获取结论文本和图标
                    conclusion_text = item["conclusion"]["text"]
                    icon = item["conclusion"]["icon"]
                    icon_color = self.get_icon_color(icon)
                    
                    # 结论文本
                    conclusion_label = ttk.Label(
                        rule_frame, 
                        text=conclusion_text,
                        font=('思源黑体', 10, 'bold')
                    )
                    conclusion_label.pack(side=tk.LEFT, padx=2)
                    
                    # 结论图标
                    icon_label = ttk.Label(
                        rule_frame, 
                        text=icon,
                        font=('思源黑体', 10, 'bold'),
                        foreground=icon_color
                    )
                    icon_label.pack(side=tk.LEFT)
            
            elif item["type"] == "section":
                # 小节标题
                section_frame = ttk.Frame(parent)
                section_frame.pack(fill=tk.X, pady=3, anchor=tk.W)
                
                section_label = ttk.Label(
                    section_frame, 
                    text=item["title"],
                    font=('思源黑体', 10, 'bold'),
                    foreground=COLOR_PRIMARY,
                    wraplength=wraplength  # 设置自动换行
                )
                section_label.pack(side=tk.LEFT, anchor=tk.W)
                
                # 递归渲染小节内容
                if "content" in item:
                    # 创建内容容器并缩进
                    content_container = ttk.Frame(parent)
                    content_container.pack(fill=tk.X, padx=(10, 0), pady=1)
                    self.render_content(content_container, item["content"])
            
            elif item["type"] == "table":
                # 表格
                table_frame = ttk.Frame(parent)
                table_frame.pack(fill=tk.X, pady=3)
                
                # 表头
                header_frame = ttk.Frame(table_frame)
                header_frame.pack(fill=tk.X, pady=1)
                
                # 创建表头列
                for i, col in enumerate(item["header"]):
                    col_label = ttk.Label(
                        header_frame, 
                        text=col,
                        font=('思源黑体', 10, 'bold'),
                        foreground=COLOR_PRIMARY,
                        wraplength=wraplength // len(item["header"]) - 10  # 考虑列数设置换行
                    )
                    col_label.grid(row=0, column=i, padx=4, sticky=tk.W)
                
                # 分隔线
                separator = ttk.Separator(table_frame, orient="horizontal")
                separator.pack(fill=tk.X, pady=3)
                
                # 表格内容
                for i, row in enumerate(item["rows"]):
                    row_frame = ttk.Frame(table_frame)
                    row_frame.pack(fill=tk.X, pady=1)
                    
                    # 行内容
                    for j, cell in enumerate(row):
                        # 检查是否有特殊格式
                        cell_text = cell["text"] if isinstance(cell, dict) else cell
                        
                        # 创建单元格标签
                        cell_label = ttk.Label(
                            row_frame, 
                            text=cell_text,
                            font=('思源黑体', 10),
                            wraplength=wraplength // len(row) - 10  # 考虑单元格数量设置换行
                        )
                        
                        # 设置颜色（如果有）
                        if isinstance(cell, dict) and "icon" in cell:
                            icon_color = self.get_icon_color(cell["icon"])
                            cell_label.configure(foreground=icon_color, font=('思源黑体', 10, 'bold'))
                        
                        cell_label.grid(row=0, column=j, padx=4, sticky=tk.W)
                        
                    # 每行后添加细分隔线
                    if i < len(item["rows"]) - 1:
                        row_separator = ttk.Separator(table_frame, orient="horizontal")
                        row_separator.pack(fill=tk.X, pady=1)
                        
            elif item["type"] == "note":
                # 注释文本
                note_frame = ttk.Frame(parent)
                note_frame.pack(fill=tk.X, pady=3)
                
                note_label = ttk.Label(
                    note_frame, 
                    text=item["text"],
                    font=('思源黑体', 9, 'italic'),
                    foreground=self.COLOR_GRAY,
                    wraplength=wraplength,  # 自适应宽度
                    justify=tk.LEFT
                )
                note_label.pack(side=tk.LEFT, anchor=tk.W)
    
    def get_icon_color(self, icon):
        """
        根据图标类型获取颜色
        
        Args:
            icon: 图标类型
        
        Returns:
            颜色代码
        """
        if icon == "✔️":
            return self.COLOR_GREEN
        elif icon == "🎣":
            return self.COLOR_RED
        elif icon == "🚧":
            return self.COLOR_ORANGE
        else:
            return self.COLOR_GRAY
    
    def create_cards(self):
        """创建所有卡片"""
        # 初始化卡片追踪字典
        self.card_widgets = {}
        
        # 卡片网格 - 自适应列布局
        self.current_columns = 3  # 默认为3列
        for i in range(3):
            self.cards_grid.columnconfigure(i, weight=1)
        
        # 卡片1: 初赔高优规则
        card1_content = [
            {"type": "rule", "text": "规则1: 弱方(非让) + 超实盘", "conclusion": {"text": "必胜", "icon": "✔️"}},
            {"type": "rule", "text": "规则2: 弱方(让方) + 超实盘", "conclusion": {"text": "几乎必胜", "icon": "✔️"}},
            {"type": "rule", "text": "规则3: 强方(让方) + 超实盘", "conclusion": {"text": "极大概率胜", "icon": "✔️"}},
            {"type": "rule", "text": "规则4: 弱方(让方) + 韬开盘", "conclusion": {"text": "极大概率胜", "icon": "✔️"}},
            {"type": "rule", "text": "规则5: 强方(让方) + 韬开盘", "conclusion": {"text": "大概率不胜", "icon": "❓/🎣"}},
            {"type": "rule", "text": "规则6: 强方(让方) + 实开盘", "conclusion": {"text": "大概率不败", "icon": "✔️/❓"}}
        ]
        card1 = self.create_rule_card(
            self.cards_grid, 
            "初赔高优规则", 
            self.COLOR_GREEN,
            card1_content,
            "card1"
        )
        card1.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        self.card_widgets["card1"]["original_row"] = 0
        self.card_widgets["card1"]["original_col"] = 0
        
        # 卡片2: 信心充足时分析
        card2_content = [
            {"type": "section", "title": "情境: 初赔低开", "content": [
                {"type": "rule", "text": "初始: 认可"},
                {"type": "rule", "text": "维持低开", "conclusion": {"text": "否定 (诱盘)", "icon": "🎣"}},
                {"type": "rule", "text": "拉低(入超实)", "conclusion": {"text": "确认 (看好)", "icon": "✔️"}},
                {"type": "rule", "text": "缓升(入实开)", "conclusion": {"text": "诱盘", "icon": "🎣"}},
                {"type": "note", "text": "如果庄家真的看中低开盘方向能够打出，会将低开盘及时拉低进入超实盘区"}
            ]},
            {"type": "section", "title": "情境: 初赔高开", "content": [
                {"type": "rule", "text": "初始: 通常诱盘", "conclusion": {"text": "", "icon": "🎣"}},
                {"type": "rule", "text": "缓升(入韬开低水)", "conclusion": {"text": "阻盘 (罕见)", "icon": "🚧"}},
                {"type": "note", "text": "若后期缓慢将赔率提升进韬开区低水，通常表示阻盘，初赔方向开错(1%可能性)，升水表示正向看好"}
            ]}
        ]
        card2 = self.create_rule_card(
            self.cards_grid, 
            "信心充足时分析", 
            self.COLOR_GREEN,
            card2_content,
            "card2"
        )
        card2.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        self.card_widgets["card2"]["original_row"] = 0
        self.card_widgets["card2"]["original_col"] = 1
        
        # 卡片3: 信心不足时分析
        card3_content = [
            {"type": "section", "title": "情境: 初赔高开", "content": [
                {"type": "rule", "text": "初始: 认可"},
                {"type": "rule", "text": "维持高开", "conclusion": {"text": "否定 (诱盘)", "icon": "🎣"}},
                {"type": "rule", "text": "拉高(入韬开)", "conclusion": {"text": "确认 (看好)", "icon": "✔️"}},
                {"type": "rule", "text": "缓降(入实开)", "conclusion": {"text": "诱盘", "icon": "🎣"}},
                {"type": "note", "text": "如果庄家真的看好高开盘方向能够打出，会将高开盘及时提升进入韬开区"}
            ]},
            {"type": "section", "title": "情境: 初赔低开", "content": [
                {"type": "rule", "text": "初始: 大概率诱盘", "conclusion": {"text": "", "icon": "🎣"}},
                {"type": "rule", "text": "缓升(入实开中低水)", "conclusion": {"text": "阻盘 (罕见)", "icon": "🚧"}},
                {"type": "note", "text": "若后期将赔率缓慢提升至实开区中低水，通常表示阻盘，初赔方向开错(1%可能性)，升水表示正向看好"}
            ]}
        ]
        card3 = self.create_rule_card(
            self.cards_grid, 
            "信心不足时分析", 
            self.COLOR_ORANGE,
            card3_content,
            "card3"
        )
        card3.grid(row=0, column=2, padx=5, pady=5, sticky="nsew")
        self.card_widgets["card3"]["original_row"] = 0
        self.card_widgets["card3"]["original_col"] = 2
        
        # 卡片5: 特殊球队规则
        card5_content = [
            {"type": "section", "title": "强队 (准强、普强、人强、超强):", "content": [
                {"type": "rule", "text": "规则: 无论状态好坏、信心强弱，任何时候都不可以开", "conclusion": {"text": "韬光盘", "icon": ""}},
                {"type": "rule", "text": "若开", "conclusion": {"text": "99% 代表利诱", "icon": "🎣"}}
            ]},
            {"type": "section", "title": "弱队 (下游与中下):", "content": [
                {"type": "rule", "text": "规则: 无论状态好坏、信心强弱，通常不能初赔直接开", "conclusion": {"text": "韬光盘", "icon": ""}},
                {"type": "rule", "text": "若开", "conclusion": {"text": "代表高开任博", "icon": "🎣"}},
                {"type": "rule", "text": "想要开韬光盘，必须另一个方向进行利诱分散"},
                {"type": "rule", "text": "用拉低分散来配合韬开盘", "conclusion": {"text": "几乎不存在", "icon": "❓"}}
            ]}
        ]
        card5 = self.create_rule_card(
            self.cards_grid, 
            "特殊球队规则", 
            self.COLOR_ORANGE,
            card5_content,
            "card5"
        )
        card5.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")
        self.card_widgets["card5"]["original_row"] = 1
        self.card_widgets["card5"]["original_col"] = 0
        
        # 卡片7: 盘口结构解读
        card7_content = [
            {"type": "rule", "text": "超韬盘：", "conclusion": {"text": "信心极度保守", "icon": "🚧"}},
            {"type": "rule", "text": "韬盘：", "conclusion": {"text": "信心保守", "icon": "🚧"}},
            {"type": "rule", "text": "高开盘：", "conclusion": {"text": "信心偏保守", "icon": "❓"}},
            {"type": "rule", "text": "实开盘：", "conclusion": {"text": "信心中庸", "icon": "❓"}},
            {"type": "rule", "text": "低开盘：", "conclusion": {"text": "信心偏充足", "icon": "✔️"}},
            {"type": "rule", "text": "超实盘：", "conclusion": {"text": "信心充足", "icon": "✔️"}},
            {"type": "rule", "text": "散盘/深盘：", "conclusion": {"text": "信心极度充足", "icon": "✔️"}},
            {"type": "section", "title": "高开低开变盘规则", "content": [
                {"type": "rule", "text": "原则：", "conclusion": {"text": "高开低开一定要变盘", "icon": "✔️"}},
                {"type": "rule", "text": "高开变盘：", "conclusion": {"text": "降到实开或升到韬光", "icon": "✔️"}},
                {"type": "rule", "text": "低开变盘：", "conclusion": {"text": "升到实开或降到超实", "icon": "✔️"}}
            ]}
        ]
        card7 = self.create_rule_card(
            self.cards_grid, 
            "盘口结构解读", 
            self.COLOR_PRIMARY,
            card7_content,
            "card7"
        )
        card7.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")
        self.card_widgets["card7"]["original_row"] = 1
        self.card_widgets["card7"]["original_col"] = 1
        
        # 检测初始窗口大小并设置布局
        self.after(100, lambda: self.on_resize())

    def clear(self):
        """清空标签页内容"""
        pass  # 这个标签页不需要清空操作

if __name__ == "__main__":
    # 测试代码
    root = tk.Tk()
    root.title("赔率分析法则")
    root.geometry("1200x800")
    
    # 设置样式
    style = ttk.Style()
    style.configure("TFrame", background=COLOR_BG)
    style.configure("Card.TFrame", background="white", relief="solid", borderwidth=1)
    style.configure("CardHeader.TFrame", background="#F8F9FA")
    style.configure("CardContent.TFrame", background="white")
    
    tab = OddsRulesTab(root)
    tab.pack(fill=tk.BOTH, expand=True)
    
    root.mainloop() 