import tkinter as tk
from tkinter import ttk
import logging
from football_analysis_system.config import COLOR_BG, COLOR_HOME, COLOR_AWAY, COLOR_PRIMARY, COLOR_ACCENT, COLOR_TEXT, COLOR_TEXT_LIGHT
from football_analysis_system.config import FONT_LARGE, FONT_NORMAL, FONT_SUBHEADER

class BasicInfoTab(tk.Frame):
    """基本信息标签页"""

    def __init__(self, parent):
        """
        初始化基本信息标签页

        Args:
            parent: 父容器
        """
        super().__init__(parent, bg=COLOR_BG)

        self.create_widgets()

    def create_widgets(self):
        """创建标签页控件"""
        # 比赛基本信息框架 - 使用现代化的卡片式设计
        self.match_info_frame = ttk.LabelFrame(self, text="比赛信息",
                                            style="TLabelframe")
        self.match_info_frame.pack(fill=tk.X, expand=False, padx=15, pady=15)

        # 主客队框架 - 使用更现代的布局
        teams_frame = ttk.Frame(self.match_info_frame, style="TFrame")
        teams_frame.pack(fill=tk.X, padx=15, pady=15)

        # 添加VS标签在中间
        vs_label = ttk.Label(teams_frame, text="VS",
                           font=FONT_LARGE,
                           foreground=COLOR_PRIMARY,
                           background=COLOR_BG)
        vs_label.grid(row=0, column=1, padx=20)

        # 主队和客队标签 - 更现代的布局
        # 主队在左侧
        self.home_team_value = ttk.Label(teams_frame, text="",
                                      font=FONT_LARGE,
                                      foreground=COLOR_HOME,
                                      background=COLOR_BG,
                                      style="Home.TLabel")
        self.home_team_value.grid(row=0, column=0, sticky=tk.E, padx=10, pady=10)

        # 客队在右侧
        self.away_team_value = ttk.Label(teams_frame, text="",
                                      font=FONT_LARGE,
                                      foreground=COLOR_AWAY,
                                      background=COLOR_BG,
                                      style="Away.TLabel")
        self.away_team_value.grid(row=0, column=2, sticky=tk.W, padx=10, pady=10)

        # 添加分隔线
        separator = ttk.Separator(self.match_info_frame, orient="horizontal")
        separator.pack(fill=tk.X, padx=15, pady=10)

        # 球队实力框架 - 使用更现代的布局
        strength_frame = ttk.Frame(self.match_info_frame)
        strength_frame.pack(fill=tk.X, padx=15, pady=10)

        # 创建两个列，左侧主队，右侧客队
        home_info_frame = ttk.Frame(strength_frame)
        home_info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)

        away_info_frame = ttk.Frame(strength_frame)
        away_info_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=10)

        # 主队信息区域
        home_title = ttk.Label(home_info_frame, text="主队信息",
                              font=FONT_LARGE,
                              foreground=COLOR_HOME,
                              background=COLOR_BG)
        home_title.pack(anchor=tk.W, pady=(0, 10))

        # 主队实力
        home_strength_frame = ttk.Frame(home_info_frame)
        home_strength_frame.pack(fill=tk.X, pady=2)

        self.home_strength_label = ttk.Label(home_strength_frame, text="实力等级: ",
                                         foreground=COLOR_TEXT,
                                         background=COLOR_BG)
        self.home_strength_label.pack(side=tk.LEFT)

        self.home_strength_value = ttk.Label(home_strength_frame, text="",
                                         foreground=COLOR_TEXT,
                                         background=COLOR_BG)
        self.home_strength_value.pack(side=tk.LEFT)

        # 主队评分
        home_rating_frame = ttk.Frame(home_info_frame)
        home_rating_frame.pack(fill=tk.X, pady=2)

        self.home_rating_label = ttk.Label(home_rating_frame, text="评分值: ",
                                       foreground=COLOR_TEXT,
                                       background=COLOR_BG)
        self.home_rating_label.pack(side=tk.LEFT)

        self.home_rating_value = ttk.Label(home_rating_frame, text="",
                                       foreground=COLOR_TEXT,
                                       background=COLOR_BG)
        self.home_rating_value.pack(side=tk.LEFT)

        # 客队信息区域
        away_title = ttk.Label(away_info_frame, text="客队信息",
                              font=FONT_LARGE,
                              foreground=COLOR_AWAY,
                              background=COLOR_BG)
        away_title.pack(anchor=tk.W, pady=(0, 10))

        # 客队实力
        away_strength_frame = ttk.Frame(away_info_frame)
        away_strength_frame.pack(fill=tk.X, pady=2)

        self.away_strength_label = ttk.Label(away_strength_frame, text="实力等级: ",
                                         foreground=COLOR_TEXT,
                                         background=COLOR_BG)
        self.away_strength_label.pack(side=tk.LEFT)

        self.away_strength_value = ttk.Label(away_strength_frame, text="",
                                         foreground=COLOR_TEXT,
                                         background=COLOR_BG)
        self.away_strength_value.pack(side=tk.LEFT)

        # 客队评分
        away_rating_frame = ttk.Frame(away_info_frame)
        away_rating_frame.pack(fill=tk.X, pady=2)

        self.away_rating_label = ttk.Label(away_rating_frame, text="评分值: ",
                                       foreground=COLOR_TEXT,
                                       background=COLOR_BG)
        self.away_rating_label.pack(side=tk.LEFT)

        self.away_rating_value = ttk.Label(away_rating_frame, text="",
                                       foreground=COLOR_TEXT,
                                       background=COLOR_BG)
        self.away_rating_value.pack(side=tk.LEFT)

        # 添加分隔线
        separator2 = ttk.Separator(self.match_info_frame, orient="horizontal")
        separator2.pack(fill=tk.X, padx=15, pady=10)

        # 档距差 - 使用更现代的卡片式设计
        gap_frame = ttk.Frame(self.match_info_frame)
        gap_frame.pack(fill=tk.X, padx=15, pady=10)

        # 添加标题
        gap_title = ttk.Label(gap_frame, text="比赛分析",
                            font=FONT_LARGE,
                            foreground=COLOR_PRIMARY,
                            background=COLOR_BG)
        gap_title.pack(anchor=tk.W, pady=(0, 10))

        # 档距差显示
        gap_value_frame = ttk.Frame(gap_frame)
        gap_value_frame.pack(fill=tk.X, pady=5)

        self.gap_difference_label = tk.Label(gap_frame, text="档距差D: ",
                                          font=FONT_LARGE,
                                          fg=COLOR_PRIMARY, bg=COLOR_BG)
        self.gap_difference_label.pack(side=tk.LEFT, padx=5)

        self.gap_difference_value = ttk.Label(gap_value_frame, text="",
                                          font=FONT_LARGE,
                                          foreground=COLOR_ACCENT,
                                          background=COLOR_BG)
        self.gap_difference_value.pack(side=tk.LEFT, padx=5)

        # 添加多公司评分区域
        self.create_multi_company_frame()

        # 可视化区域 - 使用现代化的卡片式设计
        visualization_frame = ttk.LabelFrame(self, text="可视化分析",
                                          style="TLabelframe")
        visualization_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 暂时放置一个提示标签 - 使用更现代的样式
        self.placeholder_label = ttk.Label(visualization_frame,
                                        text="选择比赛后显示可视化分析",
                                        font=FONT_NORMAL,
                                        foreground=COLOR_TEXT_LIGHT,
                                        background=COLOR_BG)
        self.placeholder_label.pack(expand=True, pady=30)

    def create_multi_company_frame(self):
        """创建多公司评分展示区域"""
        # 多公司评分框架 - 使用现代化的卡片式设计
        self.multi_company_frame = ttk.LabelFrame(self, text="多公司评分数据",
                                               style="TLabelframe")
        self.multi_company_frame.pack(fill=tk.X, expand=False, padx=15, pady=15)

        # 创建Treeview用于显示多公司评分
        columns = ("company", "home_power", "home_rating", "away_power", "away_rating", "gap", "win_prob", "draw_prob", "loss_prob")
        self.ratings_tree = ttk.Treeview(self.multi_company_frame, columns=columns, show="headings", height=5)

        # 定义列
        self.ratings_tree.heading("company", text="博彩公司")
        self.ratings_tree.heading("home_power", text="主队实力")
        self.ratings_tree.heading("home_rating", text="主队评分")
        self.ratings_tree.heading("away_power", text="客队实力")
        self.ratings_tree.heading("away_rating", text="客队评分")
        self.ratings_tree.heading("gap", text="档距差")
        self.ratings_tree.heading("win_prob", text="主胜率")
        self.ratings_tree.heading("draw_prob", text="平局率")
        self.ratings_tree.heading("loss_prob", text="主负率")

        # 设置列宽
        self.ratings_tree.column("company", width=120)
        self.ratings_tree.column("home_power", width=80)
        self.ratings_tree.column("home_rating", width=80)
        self.ratings_tree.column("away_power", width=80)
        self.ratings_tree.column("away_rating", width=80)
        self.ratings_tree.column("gap", width=80)
        self.ratings_tree.column("win_prob", width=60)
        self.ratings_tree.column("draw_prob", width=60)
        self.ratings_tree.column("loss_prob", width=60)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.multi_company_frame, orient="vertical", command=self.ratings_tree.yview)
        self.ratings_tree.configure(yscrollcommand=scrollbar.set)

        # 放置控件
        self.ratings_tree.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

    def update_match_info(self, match_data, home_team_data, away_team_data, gap_difference):
        """
        更新比赛信息

        Args:
            match_data: 比赛数据
            home_team_data: 主队数据
            away_team_data: 客队数据
            gap_difference: 档距差
        """
        try:
            # 更新队伍名称
            self.home_team_value.config(text=match_data.get('home_team', ''))
            self.away_team_value.config(text=match_data.get('away_team', ''))

            # 更新实力信息
            home_strength = home_team_data.get('power')
            away_strength = away_team_data.get('power')
            home_rating = home_team_data.get('rating')
            away_rating = away_team_data.get('rating')

            self.home_strength_value.config(text=str(home_strength) if home_strength else "未知")
            self.away_strength_value.config(text=str(away_strength) if away_strength else "未知")
            self.home_rating_value.config(text=str(home_rating) if home_rating else "未知")
            self.away_rating_value.config(text=str(away_rating) if away_rating else "未知")

            # 更新档距差
            if gap_difference is not None:
                self.gap_difference_value.config(text=str(gap_difference))
            else:
                self.gap_difference_value.config(text="未知")

            # 更新多公司评分数据
            self.update_multi_company_ratings(home_team_data, away_team_data)

            # 隐藏提示标签
            self.placeholder_label.config(text="")

        except Exception as e:
            logging.error(f"更新比赛信息时出错: {e}")

    def update_multi_company_ratings(self, home_team_data, away_team_data):
        """
        更新多公司评分数据表格

        Args:
            home_team_data: 主队数据
            away_team_data: 客队数据
        """
        # 清空表格
        for item in self.ratings_tree.get_children():
            self.ratings_tree.delete(item)

        # 获取多公司评分数据
        home_ratings = home_team_data.get('multi_company_ratings', {})
        away_ratings = away_team_data.get('multi_company_ratings', {})

        # 找出所有公司ID
        all_company_ids = set(list(home_ratings.keys()) + list(away_ratings.keys()))

        # 打印出所有的公司ID和名称，用于调试
        for company_id in all_company_ids:
            home_data = home_ratings.get(company_id, {})
            away_data = away_ratings.get(company_id, {})
            company_name = home_data.get('company_name') or away_data.get('company_name') or ""
            logging.info(f"公司ID: {company_id}, 名称: {company_name}")

        # 定义要显示的博彩公司ID和名称映射
        target_companies = {
            "115": "威廉希尔",  # 威廉希尔
            "90": "易胜博",     # 易胜博
            "432": "香港马会",  # 香港马会
            "80": "澳门",       # 澳门
            "255": "BWIN",      # BWIN
            "81": "伟德",       # 伟德
            "474": "利记",      # 利记
            "4": "Nordicbet",   # Nordicbet
            "976": "18BET",     # 18BET
            "1132": "Coolbet",  # Coolbet
            "657": "iddaa"      # iddaa
        }

        # 检查各家公司的名称，找出澳门公司(可能有不同ID)
        macau_id = None
        for company_id in all_company_ids:
            if company_id == "80":
                macau_id = company_id
                break

            home_data = home_ratings.get(company_id, {})
            away_data = away_ratings.get(company_id, {})

            # 获取公司名称
            company_name = home_data.get('company_name') or away_data.get('company_name') or ""

            # 检查名称中是否包含"澳门"或类似字样
            if "澳门" in company_name or "澳" in company_name or "macao" in company_name.lower() or "macau" in company_name.lower():
                macau_id = company_id
                # 添加到目标公司映射
                target_companies[company_id] = "澳门"
                logging.info(f"找到澳门赔率公司: ID={company_id}, 名称={company_name}")
                break

        # 过滤只显示目标博彩公司
        filtered_company_ids = [company_id for company_id in all_company_ids if company_id in target_companies]

        # 输出调试信息
        logging.info(f"找到的公司ID: {all_company_ids}")
        logging.info(f"过滤后的公司ID: {filtered_company_ids}")

        # 为每个公司添加一行数据
        for company_id in filtered_company_ids:
            home_data = home_ratings.get(company_id, {})
            away_data = away_ratings.get(company_id, {})

            # 获取公司名称
            company_name = target_companies.get(company_id) or home_data.get('company_name') or away_data.get('company_name') or f"公司 {company_id}"

            # 获取评分数据
            home_power = home_data.get('power', '未知')
            home_rating = home_data.get('rating', '未知')
            away_power = away_data.get('power', '未知')
            away_rating = away_data.get('rating', '未知')

            # 计算档距差
            gap = "未知"
            if home_rating != '未知' and away_rating != '未知':
                try:
                    h_rating = float(home_rating)
                    a_rating = float(away_rating)
                    gap = round(h_rating - a_rating, 2)
                except (ValueError, TypeError):
                    pass

            # 查询胜平负概率
            win_prob = "未知"
            draw_prob = "未知"
            loss_prob = "未知"

            if home_power != '未知' and away_power != '未知':
                try:
                    # 直接查询数据库
                    import sqlite3
                    import os

                    # 尝试几个可能的数据库路径
                    db_paths = [
                        'data/probabilities.db',
                        'data/football_probabilities_new.db',
                        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'data', 'probabilities.db')
                    ]

                    conn = None
                    for db_path in db_paths:
                        if os.path.exists(db_path):
                            conn = sqlite3.connect(db_path)
                            break

                    if conn:
                        cursor = conn.cursor()

                        cursor.execute('''
                            SELECT win_probability, draw_probability, loss_probability
                            FROM match_probabilities
                            WHERE home_strength = ? AND away_strength = ?
                        ''', (home_power, away_power))

                        result = cursor.fetchone()
                        if result:
                            win_prob = f"{result[0]*100:.1f}%"
                            draw_prob = f"{result[1]*100:.1f}%"
                            loss_prob = f"{result[2]*100:.1f}%"
                            logging.info(f"查询到概率: {home_power} vs {away_power} = {win_prob}/{draw_prob}/{loss_prob}")
                        else:
                            logging.warning(f"未找到广义实力组合的概率: {home_power} vs {away_power}")

                        conn.close()
                except Exception as e:
                    logging.error(f"查询胜平负概率时出错: {e}")

            # 添加到表格
            self.ratings_tree.insert("", tk.END, values=(
                company_name,
                home_power,
                home_rating,
                away_power,
                away_rating,
                gap,
                win_prob,
                draw_prob,
                loss_prob
            ))

    def clear(self):
        """清除显示的数据"""
        self.home_team_value.config(text="")
        self.away_team_value.config(text="")
        self.home_strength_value.config(text="")
        self.away_strength_value.config(text="")
        self.home_rating_value.config(text="")
        self.away_rating_value.config(text="")
        self.gap_difference_value.config(text="")

        # 清空多公司评分表格
        for item in self.ratings_tree.get_children():
            self.ratings_tree.delete(item)

        self.placeholder_label.config(text="选择比赛后显示可视化分析")