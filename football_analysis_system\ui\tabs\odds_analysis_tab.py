import tkinter as tk
from tkinter import ttk
import logging
from football_analysis_system.config import COLOR_BG, COLOR_PRIMARY, COLOR_TEXT_LIGHT, FONT_SUBHEADER, FONT_NORMAL

class OddsAnalysisTab(tk.Frame):
    """赔率分析标签页"""

    def __init__(self, parent, odds_analyzer):
        """
        初始化赔率分析标签页

        Args:
            parent: 父容器
            odds_analyzer: 赔率分析器
        """
        super().__init__(parent, bg=COLOR_BG)

        self.odds_analyzer = odds_analyzer
        self.create_widgets()

    def create_widgets(self):
        """创建标签页控件"""
        # 容器框架 - 使用更现代的布局
        odds_container = ttk.Frame(self)
        odds_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 原始赔率分析 - 使用现代化的卡片式设计
        self.original_odds_frame = ttk.LabelFrame(odds_container, text="原始赔率分析",
                                               style="TLabelframe")
        self.original_odds_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=10)

        # 创建Treeview用于显示赔率
        columns = ("bookmaker", "home", "draw", "away", "payout", "home_true", "draw_true", "away_true")
        self.odds_tree = ttk.Treeview(self.original_odds_frame, columns=columns, show="headings", height=10)

        # 定义列
        self.odds_tree.heading("bookmaker", text="博彩公司")
        self.odds_tree.heading("home", text="主胜")
        self.odds_tree.heading("draw", text="平局")
        self.odds_tree.heading("away", text="客胜")
        self.odds_tree.heading("payout", text="返还率")
        self.odds_tree.heading("home_true", text="主胜满水")
        self.odds_tree.heading("draw_true", text="平局满水")
        self.odds_tree.heading("away_true", text="客胜满水")

        # 设置列宽度
        self.odds_tree.column("bookmaker", width=100)
        self.odds_tree.column("home", width=70, anchor=tk.CENTER)
        self.odds_tree.column("draw", width=70, anchor=tk.CENTER)
        self.odds_tree.column("away", width=70, anchor=tk.CENTER)
        self.odds_tree.column("payout", width=80, anchor=tk.CENTER)
        self.odds_tree.column("home_true", width=80, anchor=tk.CENTER)
        self.odds_tree.column("draw_true", width=80, anchor=tk.CENTER)
        self.odds_tree.column("away_true", width=80, anchor=tk.CENTER)

        # 滚动条
        odds_scroll_y = ttk.Scrollbar(self.original_odds_frame, orient="vertical", command=self.odds_tree.yview)
        odds_scroll_x = ttk.Scrollbar(self.original_odds_frame, orient="horizontal", command=self.odds_tree.xview)
        self.odds_tree.configure(yscrollcommand=odds_scroll_y.set, xscrollcommand=odds_scroll_x.set)

        # 布局
        self.odds_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        odds_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        odds_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # 赔率可视化和分析结果 - 使用现代化的卡片式设计
        self.odds_viz_frame = ttk.LabelFrame(odds_container, text="赔率趋势分析",
                                          style="TLabelframe")
        self.odds_viz_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=10)

        # 暂时放置一个提示标签 - 使用更现代的样式
        self.placeholder_label = ttk.Label(self.odds_viz_frame,
                                        text="选择比赛后显示赔率趋势分析",
                                        font=FONT_NORMAL,
                                        foreground=COLOR_TEXT_LIGHT,
                                        background=COLOR_BG)
        self.placeholder_label.pack(expand=True, pady=30)

    def update_odds_data(self, match_data):
        """更新赔率数据"""
        try:
            # 清空当前赔率表格
            for item in self.odds_tree.get_children():
                self.odds_tree.delete(item)

            if not match_data or 'odds' not in match_data:
                self.placeholder_label.config(text="没有此比赛的赔率数据")
                return

            odds_dict = match_data.get('odds', {})
            if not odds_dict:
                self.placeholder_label.config(text="没有此比赛的赔率数据")
                return

            # 添加调试日志
            logging.info(f"赔率分析 - 找到 {len(odds_dict)} 个博彩公司的赔率数据")
            for bookmaker in odds_dict.keys():
                logging.info(f"赔率分析 - 博彩公司: {bookmaker}")

            # 获取联赛名称
            league_name = match_data.get('league_name', '未知联赛')
            logging.info(f"赔率分析 - 当前联赛: {league_name}")

            # 判断是否为南美联赛 (巴西、阿根廷、智利)
            is_south_american = any(league in league_name for league in ["巴西甲", "巴西乙", "阿根廷甲", "阿根廷乙", "智利甲", "智利乙"])

            # 判断是否为东欧/亚洲联赛 (俄罗斯、乌克兰、波兰、奥地利等)
            is_east_european_asian = any(league in league_name for league in ["俄超", "俄甲", "乌克兰", "波兰", "奥地利", "韩K", "韩K2"])

            # 获取主客队对象，提取广义实力数据
            home_team_data = match_data.get('home_team_obj')
            away_team_data = match_data.get('away_team_obj')

            # 创建有广义实力数据的公司ID集合
            available_company_ids = set()

            if home_team_data and away_team_data:
                home_ratings = getattr(home_team_data, 'multi_company_ratings', {})
                away_ratings = getattr(away_team_data, 'multi_company_ratings', {})

                # 合并主客队的广义实力公司ID
                all_company_ids = set(list(home_ratings.keys()) + list(away_ratings.keys()))

                for company_id in all_company_ids:
                    home_data = home_ratings.get(company_id, {})
                    away_data = away_ratings.get(company_id, {})

                    home_rating = home_data.get('rating')
                    away_rating = away_data.get('rating')

                    if home_rating is not None and away_rating is not None:
                        # 这个公司有完整的广义实力数据
                        available_company_ids.add(company_id)
                        company_name = home_data.get('company_name') or away_data.get('company_name')
                        if company_name:
                            logging.info(f"找到公司 {company_name} 的广义实力数据，ID: {company_id}")

            # 公司ID映射表
            company_id_mapping = {
                "马会": "432",  # 香港马会
                "HKJC": "432",  # 香港马会
                "香港": "432",  # 香港马会
                "澳门": "80",   # 澳门
                "Macau": "80",  # 澳门
                "BetISn": "937", # BetISn
                "betisn": "937", # BetISn
                "iddaa": "657",   # iddaa
                "伟德": "81",    # 伟德
                "韦德": "81",    # 伟德（变体）
                "18BET": "976",   # 18BET
                "18bet": "976",   # 18BET（变体）
                "18*": "976",     # 18BET（变体）
                "18": "976",      # 18BET（变体）
                "威廉": "115",    # 威廉希尔
                "威廉希尔": "115",  # 威廉希尔
                "William Hill": "115", # 威廉希尔英文名
                "必赢": "255",    # bwin/必赢
                "bwin": "255",    # bwin/必赢
                "BWIN": "255",    # bwin/必赢
                "bwi": "255",     # bwin/必赢（变体）
                "Bwi": "255"      # bwin/必赢（变体）
            }

            # 查找基准赔率公司数据
            betisn_odds = None    # BetISn赔率
            coolbet_odds = None
            iddaa_odds = None
            hkjc_odds = None
            macau_odds = None
            weide_odds = None   # 伟德赔率
            bet18_odds = None   # 18BET赔率
            william_odds = None   # 威廉希尔赔率
            bwin_odds = None      # bwin赔率
            all_bookmakers = []   # 所有博彩公司列表

            # 优化查找基准公司的逻辑 - 一次遍历完成所有查找
            for bookmaker, odds_data in odds_dict.items():
                if not bookmaker:
                    continue

                bookmaker_lower = bookmaker.lower()

                # 检查这个公司是否有广义实力数据
                has_guangyi_data = False
                company_id = None

                # 查找公司ID
                for key, id_val in company_id_mapping.items():
                    if key in bookmaker:
                        company_id = id_val
                        break

                if company_id and company_id in available_company_ids:
                    has_guangyi_data = True
                    logging.info(f"赔率分析 - 公司 {bookmaker} 有广义实力数据")
                else:
                    logging.warning(f"赔率分析 - 公司 {bookmaker} 没有广义实力数据，不显示")
                    continue  # 跳过没有广义实力数据的公司

                # 添加到所有公司列表
                all_bookmakers.append(bookmaker)

                # 使用更高效的字符串匹配
                if "betisn" in bookmaker_lower or bookmaker == "BetISn":
                    betisn_odds = (bookmaker, odds_data)
                    # 为BetISn添加日志记录，帮助调试
                    logging.info(f"找到BetISn赔率数据: {bookmaker}")
                elif "coolbet" in bookmaker_lower or bookmaker == "Coolbet":
                    coolbet_odds = (bookmaker, odds_data)
                elif "iddaa" in bookmaker_lower or bookmaker == "iddaa":
                    iddaa_odds = (bookmaker, odds_data)
                elif "马会" in bookmaker or "hkjc" in bookmaker_lower or "香港" in bookmaker:
                    hkjc_odds = (bookmaker, odds_data)
                elif "澳门" in bookmaker or "macau" in bookmaker_lower:
                    macau_odds = (bookmaker, odds_data)
                elif "伟德" in bookmaker or "韦德" in bookmaker or "weide" in bookmaker_lower:
                    weide_odds = (bookmaker, odds_data)  # 记录伟德赔率
                elif "18bet" in bookmaker_lower or bookmaker == "18BET" or "18*" in bookmaker or bookmaker.startswith("18"):
                    bet18_odds = (bookmaker, odds_data)  # 记录18BET赔率
                    logging.info(f"匹配到18BET赔率数据: {bookmaker}")
                elif "威廉" in bookmaker or "William Hill" in bookmaker or "william hill" in bookmaker_lower:
                    william_odds = (bookmaker, odds_data)  # 记录威廉希尔赔率
                elif "必赢" in bookmaker or "bwin" in bookmaker_lower or "BWIN" in bookmaker or "bwi" in bookmaker_lower:
                    bwin_odds = (bookmaker, odds_data)  # 记录bwin赔率
                    logging.info(f"匹配到BWIN赔率数据: {bookmaker}")

            # 收集所有基准公司的赔率数据，包括根据联赛类型可能添加的特殊基准赔率公司
            base_company_odds = []

            # 首先添加BetISn作为基准赔率公司
            if betisn_odds:
                base_company_odds.append(betisn_odds)

            # 添加其他标准基准赔率公司
            if hkjc_odds:
                base_company_odds.append(hkjc_odds)
            if iddaa_odds:
                base_company_odds.append(iddaa_odds)

            # 根据联赛类型添加特殊基准赔率公司
            if is_south_american and weide_odds:
                logging.info(f"南美联赛 {league_name} - 添加伟德作为基准赔率公司")
                base_company_odds.append(weide_odds)

            if is_east_european_asian and bet18_odds:
                logging.info(f"东欧/亚洲联赛 {league_name} - 添加18BET作为基准赔率公司")
                base_company_odds.append(bet18_odds)

            # 显示所有基准公司的初始赔率
            for company_odds in base_company_odds:
                if company_odds:
                    bookmaker, odds_data = company_odds

                    # 获取初始赔率 - 严格只使用初始赔率数据
                    initial_home_win = odds_data.get('Initial_W') or odds_data.get('initial_home_win', 'N/A')
                    initial_draw = odds_data.get('Initial_D') or odds_data.get('initial_draw', 'N/A')
                    initial_away_win = odds_data.get('Initial_L') or odds_data.get('initial_away_win', 'N/A')

                    # 获取返还率数据 - 严格只使用初始返还率
                    initial_payout_rate = odds_data.get('Initial_Payout_Rate') or odds_data.get('initial_payout_rate', 'N/A')

                    # 添加调试日志，显示找到的返还率
                    logging.info(f"{bookmaker} - 找到初始赔率: 胜={initial_home_win}, 平={initial_draw}, 负={initial_away_win}, 返还率={initial_payout_rate}")

                    # 安全转换为浮点数
                    try:
                        if initial_home_win != 'N/A':
                            initial_home_win = float(initial_home_win)
                        if initial_draw != 'N/A':
                            initial_draw = float(initial_draw)
                        if initial_away_win != 'N/A':
                            initial_away_win = float(initial_away_win)
                        if initial_payout_rate != 'N/A':
                            initial_payout_rate = float(initial_payout_rate)
                    except (ValueError, TypeError):
                        pass

                    # 强制使用初始返还率
                    display_rate = ""
                    rate_for_calculation = None

                    if initial_payout_rate != 'N/A':
                        display_rate = f"{initial_payout_rate}% (初)"
                        rate_for_calculation = initial_payout_rate
                    else:
                        display_rate = "未知"

                    # 计算初始赔率的满水赔率
                    initial_home_win_true = 'N/A'
                    initial_draw_true = 'N/A'
                    initial_away_win_true = 'N/A'

                    if rate_for_calculation and initial_home_win != 'N/A':
                        initial_home_win_true = self.odds_analyzer.calculate_true_odds(initial_home_win, rate_for_calculation)
                    if rate_for_calculation and initial_draw != 'N/A':
                        initial_draw_true = self.odds_analyzer.calculate_true_odds(initial_draw, rate_for_calculation)
                    if rate_for_calculation and initial_away_win != 'N/A':
                        initial_away_win_true = self.odds_analyzer.calculate_true_odds(initial_away_win, rate_for_calculation)

                    # 添加初始赔率行
                    self.odds_tree.insert("", tk.END, values=(
                        f"{bookmaker} (初赔)",
                        initial_home_win,
                        initial_draw,
                        initial_away_win,
                        display_rate,
                        initial_home_win_true,
                        initial_draw_true,
                        initial_away_win_true
                    ))

            # 显示所有公司的即时赔率（包括基准赔率公司）
            for bookmaker in all_bookmakers:
                odds_data = odds_dict[bookmaker]

                # 获取即时赔率 - 简化键名检查逻辑
                home_win = odds_data.get('Instant_W') or odds_data.get('instant_home_win', 'N/A')
                draw = odds_data.get('Instant_D') or odds_data.get('instant_draw', 'N/A')
                away_win = odds_data.get('Instant_L') or odds_data.get('instant_away_win', 'N/A')
                # 获取即时返还率
                payout_rate = odds_data.get('Payout_Rate') or odds_data.get('payout_rate', 'N/A')

                # 添加调试日志
                logging.info(f"{bookmaker} - 即时赔率: 胜={home_win}, 平={draw}, 负={away_win}, 返还率={payout_rate}")

                # 安全转换为浮点数
                try:
                    if home_win != 'N/A':
                        home_win = float(home_win)
                    if draw != 'N/A':
                        draw = float(draw)
                    if away_win != 'N/A':
                        away_win = float(away_win)
                    if payout_rate != 'N/A':
                        payout_rate = float(payout_rate)
                except (ValueError, TypeError):
                    pass

                # 准备返还率显示和计算
                display_rate = f"{payout_rate}% (即)" if payout_rate != 'N/A' else "未知"

                # 计算满水赔率 - 对于即时赔率使用即时返还率
                home_win_true = 'N/A'
                draw_true = 'N/A'
                away_win_true = 'N/A'

                if payout_rate != 'N/A':
                    if home_win != 'N/A':
                        home_win_true = self.odds_analyzer.calculate_true_odds(home_win, payout_rate)
                    if draw != 'N/A':
                        draw_true = self.odds_analyzer.calculate_true_odds(draw, payout_rate)
                    if away_win != 'N/A':
                        away_win_true = self.odds_analyzer.calculate_true_odds(away_win, payout_rate)

                # 添加即时赔率行
                self.odds_tree.insert("", tk.END, values=(
                    f"{bookmaker} (即赔)",
                    home_win,
                    draw,
                    away_win,
                    display_rate,
                    home_win_true,
                    draw_true,
                    away_win_true
                ))

            # 隐藏提示标签
            self.placeholder_label.config(text="")

        except Exception as e:
            logging.error(f"更新赔率数据时出错: {e}")
            import traceback
            logging.error(traceback.format_exc())

    def clear(self):
        """清除显示的数据"""
        for item in self.odds_tree.get_children():
            self.odds_tree.delete(item)
        self.placeholder_label.config(text="选择比赛后显示赔率趋势分析")