class Odds:
    """赔率数据模型类"""
    
    def __init__(self, bookmaker=None, initial_home_win=None, initial_draw=None, initial_away_win=None,
                 instant_home_win=None, instant_draw=None, instant_away_win=None, payout_rate=None):
        """
        初始化赔率对象
        
        Args:
            bookmaker: 博彩公司名称
            initial_home_win: 初始主胜赔率
            initial_draw: 初始平局赔率
            initial_away_win: 初始客胜赔率
            instant_home_win: 即时主胜赔率
            instant_draw: 即时平局赔率
            instant_away_win: 即时客胜赔率
            payout_rate: 返还率
        """
        self.bookmaker = bookmaker
        self.initial_home_win = initial_home_win
        self.initial_draw = initial_draw
        self.initial_away_win = initial_away_win
        self.instant_home_win = instant_home_win
        self.instant_draw = instant_draw
        self.instant_away_win = instant_away_win
        self.payout_rate = payout_rate
    
    def to_dict(self):
        """
        将赔率对象转换为字典
        """
        return {
            'bookmaker': self.bookmaker,
            'initial_home_win': self.initial_home_win,
            'initial_draw': self.initial_draw,
            'initial_away_win': self.initial_away_win,
            'instant_home_win': self.instant_home_win,
            'instant_draw': self.instant_draw,
            'instant_away_win': self.instant_away_win,
            'payout_rate': self.payout_rate
        }
    
    @classmethod
    def from_dict(cls, data):
        """
        从字典创建赔率对象
        
        Args:
            data: 包含赔率数据的字典
            
        Returns:
            Odds: 赔率对象
        """
        return cls(
            bookmaker=data.get('bookmaker'),
            initial_home_win=data.get('initial_home_win'),
            initial_draw=data.get('initial_draw'),
            initial_away_win=data.get('initial_away_win'),
            instant_home_win=data.get('instant_home_win'),
            instant_draw=data.get('instant_draw'),
            instant_away_win=data.get('instant_away_win'),
            payout_rate=data.get('payout_rate')
        )
    
    def __str__(self):
        """字符串表示"""
        return f"{self.bookmaker}: 主胜 {self.instant_home_win} 平局 {self.instant_draw} 客胜 {self.instant_away_win}"