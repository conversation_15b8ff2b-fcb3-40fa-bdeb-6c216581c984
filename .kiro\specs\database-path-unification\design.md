# Design Document

## Overview

本设计文档描述了如何统一足球分析系统中的数据库路径配置，解决当前数据分散在外层`data/`和内层`football_analysis_system/data/`两个位置的问题。设计的核心目标是确保所有组件都使用内层的标准数据库路径，同时保持现有功能的正常运行。

## Architecture

### 当前架构问题

1. **双重路径配置**：
   - 内层路径：`football_analysis_system/data/matches_and_odds.db` (675KB)
   - 外层路径：`data/matches_and_odds.db` (5.79MB)

2. **硬编码路径问题**：
   - `odds_scraper.py`默认使用`"data/matches_and_odds.db"`
   - 多个测试文件直接硬编码外层路径
   - 缺乏统一的路径管理机制

3. **配置管理分散**：
   - `config.py`中的简单路径配置
   - `ConfigManager`中的高级配置管理
   - 两套系统并存但不统一

### 目标架构

```mermaid
graph TD
    A[ConfigManager] --> B[统一路径配置]
    B --> C[odds_scraper.py]
    B --> D[测试文件]
    B --> E[其他组件]
    
    F[数据迁移工具] --> G[外层数据]
    G --> H[内层数据库]
    
    I[路径解析器] --> B
    I --> J[相对路径转换]
    J --> H
```

## Components and Interfaces

### 1. 路径配置统一组件

**PathResolver类**
```python
class PathResolver:
    """统一路径解析器"""
    
    @staticmethod
    def get_database_path(db_name: str = "matches_and_odds.db") -> str:
        """获取标准化的数据库路径"""
        
    @staticmethod
    def resolve_relative_path(relative_path: str) -> str:
        """将相对路径解析为标准路径"""
```

**功能**：
- 提供统一的数据库路径获取接口
- 自动将相对路径转换为内层绝对路径
- 与现有ConfigManager集成

### 2. OddsScraper路径修复组件

**修改策略**：
- 保持构造函数接口不变，确保向后兼容
- 内部使用PathResolver解析路径
- 自动检测和转换硬编码的相对路径

**接口设计**：
```python
class OddsScraper:
    def __init__(self, url_template, headers_template, target_companies, 
                 db_path="data/matches_and_odds.db"):
        # 使用PathResolver解析路径
        self.db_path = PathResolver.resolve_relative_path(db_path)
```

### 3. 数据迁移组件

**DatabaseMigrator类**
```python
class DatabaseMigrator:
    """数据库迁移工具"""
    
    def migrate_external_data(self) -> bool:
        """迁移外层数据到内层"""
        
    def verify_migration(self) -> dict:
        """验证迁移结果"""
        
    def cleanup_external_data(self) -> bool:
        """清理外层数据"""
```

**迁移策略**：
- 检测外层数据库是否存在且包含更多数据
- 使用SQLite的ATTACH DATABASE进行数据合并
- 保留所有历史数据，避免重复
- 提供回滚机制

### 4. 测试文件修复组件

**批量修复策略**：
- 识别所有使用硬编码路径的文件
- 替换为PathResolver调用
- 保持测试逻辑不变

## Data Models

### 路径配置模型

```python
@dataclass
class DatabaseConfig:
    """数据库配置模型"""
    name: str
    relative_path: str
    absolute_path: str
    exists: bool
    size: int
```

### 迁移状态模型

```python
@dataclass
class MigrationStatus:
    """迁移状态模型"""
    source_path: str
    target_path: str
    source_records: int
    target_records: int
    migrated_records: int
    success: bool
    error_message: Optional[str]
```

## Error Handling

### 1. 路径解析错误
- **场景**：无效的相对路径或权限问题
- **处理**：回退到默认路径，记录警告日志
- **恢复**：提供手动路径配置选项

### 2. 数据迁移错误
- **场景**：数据库锁定、磁盘空间不足、数据损坏
- **处理**：事务回滚，保留原始数据
- **恢复**：提供详细错误报告和修复建议

### 3. 向后兼容性错误
- **场景**：现有代码依赖特定路径格式
- **处理**：保持接口不变，内部透明转换
- **恢复**：提供兼容模式开关

## Testing Strategy

### 1. 单元测试
- **PathResolver**：测试各种路径格式的解析
- **DatabaseMigrator**：测试数据迁移逻辑
- **OddsScraper**：测试路径解析集成

### 2. 集成测试
- **端到端路径解析**：从配置到实际文件访问
- **数据迁移完整性**：验证数据完整性和一致性
- **现有功能兼容性**：确保所有现有功能正常工作

### 3. 回归测试
- **现有测试套件**：确保所有现有测试通过
- **性能测试**：验证路径解析不影响性能
- **并发测试**：测试多进程访问数据库的安全性

## Implementation Phases

### Phase 1: 核心组件开发
1. 创建PathResolver类
2. 集成到ConfigManager
3. 单元测试覆盖

### Phase 2: OddsScraper修复
1. 修改OddsScraper构造函数
2. 更新内部路径使用
3. 保持接口兼容性

### Phase 3: 数据迁移
1. 实现DatabaseMigrator
2. 执行数据迁移
3. 验证迁移结果

### Phase 4: 测试文件修复
1. 批量更新测试文件
2. 验证测试功能
3. 清理硬编码路径

### Phase 5: 清理和验证
1. 清理外层data目录
2. 全面功能测试
3. 性能验证

## Configuration Management

### 统一配置接口
```python
# 新的统一接口
from football_analysis_system.core.path_resolver import PathResolver

# 获取数据库路径
db_path = PathResolver.get_database_path("matches_and_odds.db")

# 解析相对路径
resolved_path = PathResolver.resolve_relative_path("data/matches_and_odds.db")
```

### 配置优先级
1. 环境变量 (`FOOTBALL_DB_PATH`)
2. ConfigManager配置
3. 默认内层路径

## Migration Strategy

### 数据合并逻辑
```sql
-- 使用SQLite ATTACH进行数据合并
ATTACH DATABASE 'data/matches_and_odds.db' AS external_db;

-- 合并matches表
INSERT OR IGNORE INTO matches 
SELECT * FROM external_db.matches;

-- 合并odds表
INSERT OR IGNORE INTO odds 
SELECT * FROM external_db.odds;

-- 合并overunder_odds表
INSERT OR IGNORE INTO overunder_odds 
SELECT * FROM external_db.overunder_odds;

-- 合并history_odds表
INSERT OR IGNORE INTO history_odds 
SELECT * FROM external_db.history_odds;
```

### 数据验证
- 记录数量对比
- 关键字段完整性检查
- 数据类型一致性验证
- 外键约束验证