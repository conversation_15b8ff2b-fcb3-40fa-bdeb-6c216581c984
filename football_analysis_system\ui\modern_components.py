"""
现代化UI组件
提供各种现代化的界面组件
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import time
from typing import Callable, Optional, List, Dict, Union, Tuple
from .modern_theme import ModernTheme, ModernStyleManager
from PIL import Image, ImageTk, ImageDraw

# 创建全局主题实例
modern_theme = ModernTheme()

class ModernThemeManager:
    """主题管理器类，负责存储当前主题状态"""
    current_theme = "light"  # 默认为浅色主题
    
    @classmethod
    def toggle_theme(cls):
        """切换主题"""
        cls.current_theme = "dark" if cls.current_theme == "light" else "light"
        return cls.current_theme


class ModernButton(ttk.Button):
    """现代化按钮，支持图标和悬停效果"""
    
    def __init__(self, master=None, text="", icon=None, icon_size=(20, 20), 
                style="TButton", command=None, tooltip=None, **kwargs):
        """
        初始化现代按钮
        
        Args:
            master: 父窗口
            text: 按钮文本
            icon: 图标路径或PIL Image对象
            icon_size: 图标大小，默认 (20, 20)
            style: 按钮样式，默认 "TButton"
            command: 点击回调
            tooltip: 悬停提示
            **kwargs: 其他ttk.Button参数
        """
        self.icon_image = None
        self.tooltip_window = None
        self.tooltip_text = tooltip
        
        # 设置图标
        if icon:
            try:
                if isinstance(icon, str):
                    from PIL import Image
                    img = Image.open(icon)
                else:
                    img = icon
                
                img = img.resize(icon_size, Image.LANCZOS)
                self.icon_image = ImageTk.PhotoImage(img)
                compound = kwargs.get('compound', 'left')
                kwargs['compound'] = compound
                kwargs['image'] = self.icon_image
            except Exception as e:
                print(f"加载按钮图标出错: {e}")
        
        super().__init__(master, text=text, style=style, command=command, **kwargs)
        
        # 绑定事件
        if tooltip:
            self.bind("<Enter>", self._show_tooltip)
            self.bind("<Leave>", self._hide_tooltip)
    
    def _show_tooltip(self, event=None):
        """显示提示框"""
        if self.tooltip_text:
            x, y, _, _ = self.bbox("insert")
            x += self.winfo_rootx() + 25
            y += self.winfo_rooty() + 25
            
            # 创建提示框窗口
            self.tooltip_window = tw = tk.Toplevel(self)
            tw.wm_overrideredirect(True)
            tw.wm_geometry(f"+{x}+{y}")
            
            label = tk.Label(tw, text=self.tooltip_text, background=ModernTheme.PRIMARY_LIGHT,
                          foreground="white", relief="solid", borderwidth=1,
                          font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["sm"]),
                          padx=5, pady=2)
            label.pack()
    
    def _hide_tooltip(self, event=None):
        """隐藏提示框"""
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None


class RoundedButton(tk.Canvas):
    """自定义圆角按钮"""
    
    def __init__(self, master=None, text="", radius=10, bg_color=ModernTheme.PRIMARY, 
                fg_color="white", hover_color=ModernTheme.PRIMARY_LIGHT,
                command=None, width=100, height=30, **kwargs):
        """
        初始化圆角按钮
        
        Args:
            master: 父窗口
            text: 按钮文本
            radius: 圆角半径
            bg_color: 背景色
            fg_color: 文本色
            hover_color: 悬停背景色
            command: 点击回调
            width: 按钮宽度
            height: 按钮高度
            **kwargs: 其他tk.Canvas参数
        """
        super().__init__(master, width=width, height=height, highlightthickness=0,
                      background=master["background"] if "background" in master else ModernTheme.BACKGROUND,
                      **kwargs)
        
        self.radius = radius
        self.bg_color = bg_color
        self.hover_color = hover_color
        self.fg_color = fg_color
        self.command = command
        self.text = text
        self.font_family = kwargs.get('font_family', ModernTheme.FONT_FAMILY)
        self.font_size = kwargs.get('font_size', ModernTheme.FONT_SIZES["md"])
        
        # 绘制按钮
        self._draw_button()
        
        # 绑定事件
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        self.bind("<Button-1>", self._on_click)
    
    def _draw_button(self, color=None):
        """绘制圆角按钮"""
        if color is None:
            color = self.bg_color
            
        self.delete("all")
        
        # 绘制圆角矩形
        width, height = self.winfo_width(), self.winfo_height()
        if width == 1 and height == 1:  # 初始化时会返回1x1
            width, height = self.winfo_reqwidth(), self.winfo_reqheight()
        
        # 创建圆角矩形路径
        self.create_rounded_rect(0, 0, width, height, self.radius, fill=color)
        
        # 绘制文本
        text_color = self.fg_color
        font = (self.font_family, self.font_size)
        self.create_text(width//2, height//2, text=self.text, fill=text_color, font=font)
    
    def create_rounded_rect(self, x1, y1, x2, y2, radius, **kwargs):
        """创建圆角矩形"""
        points = [
            x1+radius, y1,
            x2-radius, y1,
            x2, y1,
            x2, y1+radius,
            x2, y2-radius,
            x2, y2,
            x2-radius, y2,
            x1+radius, y2,
            x1, y2,
            x1, y2-radius,
            x1, y1+radius,
            x1, y1
        ]
        return self.create_polygon(points, **kwargs, smooth=True)
    
    def _on_enter(self, event=None):
        """鼠标进入事件"""
        self._draw_button(self.hover_color)
    
    def _on_leave(self, event=None):
        """鼠标离开事件"""
        self._draw_button()
    
    def _on_click(self, event=None):
        """鼠标点击事件"""
        if self.command:
            self.command()
    
    def configure(self, **kwargs):
        """配置按钮属性"""
        if 'text' in kwargs:
            self.text = kwargs.pop('text')
        if 'bg_color' in kwargs:
            self.bg_color = kwargs.pop('bg_color')
        if 'fg_color' in kwargs:
            self.fg_color = kwargs.pop('fg_color')
        if 'hover_color' in kwargs:
            self.hover_color = kwargs.pop('hover_color')
        if 'command' in kwargs:
            self.command = kwargs.pop('command')
        
        super().configure(**kwargs)
        self._draw_button()
    
    def config(self, **kwargs):
        """config别名"""
        self.configure(**kwargs)


class ModernCard(ttk.Frame):
    """现代卡片视图组件"""
    
    def __init__(self, master=None, title="", icon=None, collapsible=False, **kwargs):
        """
        初始化现代卡片组件
        
        Args:
            master: 父窗口
            title: 卡片标题
            icon: 标题图标（路径或PIL Image）
            collapsible: 是否可折叠
            **kwargs: 其他ttk.Frame参数
        """
        super().__init__(master, style='Card.TFrame', **kwargs)
        
        self.collapsible = collapsible
        self.collapsed = False
        self.title = title
        self.icon_image = None
        
        # 创建卡片头部
        self.header_frame = ttk.Frame(self, style='CardHeader.TFrame')
        self.header_frame.pack(fill=tk.X, expand=False)
        
        # 水平布局标题和折叠按钮
        header_content = ttk.Frame(self.header_frame, style='CardHeader.TFrame')
        header_content.pack(fill=tk.X, padx=10, pady=5)
        
        # 图标和标题
        if icon:
            try:
                if isinstance(icon, str):
                    from PIL import Image
                    img = Image.open(icon)
                    img = img.resize((16, 16), Image.LANCZOS)
                    self.icon_image = ImageTk.PhotoImage(img)
                else:
                    self.icon_image = icon
                
                icon_label = ttk.Label(header_content, image=self.icon_image, 
                                    style='CardHeader.TLabel')
                icon_label.pack(side=tk.LEFT, padx=(0, 5))
            except Exception as e:
                print(f"加载卡片图标出错: {e}")
        
        self.title_label = ttk.Label(header_content, text=title, style='CardHeader.TLabel')
        self.title_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 折叠按钮
        if collapsible:
            self.toggle_btn = ttk.Label(header_content, text="▼", cursor="hand2",
                                     style='CardHeader.TLabel')
            self.toggle_btn.pack(side=tk.RIGHT)
            self.toggle_btn.bind("<Button-1>", self.toggle_collapse)
        
        # 创建内容区域
        self.content_frame = ttk.Frame(self, style='CardContent.TFrame')
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def toggle_collapse(self, event=None):
        """切换折叠状态"""
        if not self.collapsible:
            return
            
        self.collapsed = not self.collapsed
        
        if self.collapsed:
            self.content_frame.pack_forget()
            self.toggle_btn.configure(text="▶")
        else:
            self.content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            self.toggle_btn.configure(text="▼")
    
    def add_widget(self, widget):
        """向卡片内容区域添加小部件"""
        widget.pack(in_=self.content_frame, fill=tk.BOTH, expand=True)
        return widget


class ModernSearchBox(ttk.Frame):
    """现代搜索框组件"""
    
    def __init__(self, master=None, placeholder="搜索...", command=None, width=200, **kwargs):
        """
        初始化现代搜索框
        
        Args:
            master: 父窗口
            placeholder: 占位符文本
            command: 搜索回调函数
            width: 搜索框宽度
            **kwargs: 其他ttk.Frame参数
        """
        super().__init__(master, **kwargs)
        
        self.placeholder = placeholder
        self.command = command
        self.has_focus = False
        
        # 创建搜索框边框
        self.search_frame = ttk.Frame(self, style='Card.TFrame')
        self.search_frame.pack(fill=tk.X, expand=True)
        
        # 创建搜索图标（使用Unicode字符）
        self.search_icon = ttk.Label(self.search_frame, text="🔍", 
                                   foreground=ModernTheme.TEXT_SECONDARY)
        self.search_icon.pack(side=tk.LEFT, padx=(8, 0))
        
        # 创建输入框
        self.entry_var = tk.StringVar()
        self.entry = ttk.Entry(self.search_frame, textvariable=self.entry_var, 
                             width=width, style='TEntry')
        self.entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)
        
        # 创建清除按钮，提高其可见性
        self.clear_button = ttk.Label(self.search_frame, text="✕", cursor="hand2",
                                    foreground=ModernTheme.PRIMARY,  # 改为使用主题色
                                    font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["md"]))  # 增大字体
        self.clear_button.pack(side=tk.RIGHT, padx=(0, 8))
        self.clear_button.pack_forget()  # 初始隐藏
        
        # 绑定清除按钮点击事件
        self.clear_button.bind("<Button-1>", self.clear_search)
        
        # 设置事件绑定
        self.entry.bind("<FocusIn>", self._on_focus_in)
        self.entry.bind("<FocusOut>", self._on_focus_out)
        self.entry.bind("<Return>", self._on_search)
        self.entry.bind("<KeyRelease>", self._on_key_release)
        
        # 初始化显示占位符
        self.show_placeholder()
    
    def show_placeholder(self):
        """显示占位符"""
        if not self.has_focus and not self.entry_var.get():
            self.entry.delete(0, tk.END)
            self.entry.insert(0, self.placeholder)
            self.entry.config(foreground=ModernTheme.TEXT_SECONDARY)
    
    def hide_placeholder(self):
        """隐藏占位符"""
        if self.entry.get() == self.placeholder:
            self.entry.delete(0, tk.END)
            self.entry.config(foreground=ModernTheme.TEXT_PRIMARY)
    
    def _on_focus_in(self, event=None):
        """获取焦点时"""
        self.has_focus = True
        self.search_frame.configure(style='Card.TFrame')
        self.hide_placeholder()
    
    def _on_focus_out(self, event=None):
        """失去焦点时"""
        self.has_focus = False
        self.show_placeholder()
    
    def _on_key_release(self, event=None):
        """处理键盘释放事件"""
        # 如果有输入内容，显示清除按钮
        if self.entry_var.get():
            self.clear_button.pack(side=tk.RIGHT, padx=(0, 8))
        else:
            self.clear_button.pack_forget()
        
        # 自动搜索
        if self.command:
            self.command(self.entry_var.get())
    
    def _on_search(self, event=None):
        """处理搜索事件"""
        if self.command:
            self.command(self.entry_var.get())
    
    def clear_search(self, event=None):
        """清除搜索内容"""
        self.entry_var.set("")
        self.entry.focus_set()  # 保持焦点
        self.clear_button.pack_forget()
        
        # 触发搜索回调，通知搜索已清除
        if self.command:
            self.command("")


class ModernToast(tk.Toplevel):
    """现代Toast提示组件"""
    
    def __init__(self, master, message, duration=3000, 
               bg_color=ModernTheme.PRIMARY, fg_color='white'):
        """
        初始化Toast提示
        
        Args:
            master: 父窗口
            message: 提示信息
            duration: 显示时长(毫秒)
            bg_color: 背景颜色
            fg_color: 文本颜色
        """
        super().__init__(master)
        
        # 设置窗口属性
        self.overrideredirect(True)  # 无边框窗口
        self.attributes('-topmost', True)  # 置顶
        self.attributes('-alpha', 0.9)  # 透明度
        self.config(bg=bg_color)
        
        # 设置位置
        master_x = master.winfo_rootx()
        master_y = master.winfo_rooty()
        master_width = master.winfo_width()
        master_height = master.winfo_height()
        
        # 创建消息标签
        self.message = message
        self.label = tk.Label(self, text=message, bg=bg_color, fg=fg_color,
                           font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["md"]),
                           padx=20, pady=10)
        self.label.pack()
        
        # 调整大小并居中
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = master_x + (master_width - width) // 2
        y = master_y + master_height - height - 30
        self.geometry(f"{width}x{height}+{x}+{y}")
        
        # 设置定时消失
        self.after(duration, self.fade_away)
    
    def fade_away(self):
        """渐隐动画"""
        alpha = 0.9
        fade_steps = 10
        fade_ms = 50
        
        def fade_step():
            nonlocal alpha
            if alpha <= 0:
                self.destroy()
                return
            
            alpha -= 0.9 / fade_steps
            self.attributes('-alpha', max(0, alpha))
            self.after(fade_ms, fade_step)
        
        fade_step()


class ModernProgressBar(ttk.Frame):
    """现代进度条组件，带有动画效果"""
    
    def __init__(self, master=None, value=0, maximum=100, height=10, 
               bg_color="#e0e0e0", fg_color=ModernTheme.PRIMARY, 
               animated=True, **kwargs):
        """
        初始化现代进度条
        
        Args:
            master: 父窗口
            value: 初始值
            maximum: 最大值
            height: 高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            animated: 是否使用动画效果
            **kwargs: 其他ttk.Frame参数
        """
        super().__init__(master, **kwargs)
        
        self.value = value
        self.maximum = maximum
        self.height = height
        self.bg_color = bg_color
        self.fg_color = fg_color
        self.animated = animated
        self.target_value = value
        
        # 创建画布
        self.canvas = tk.Canvas(self, height=height, bg=bg_color,
                             highlightthickness=0)
        self.canvas.pack(fill=tk.X, expand=True)
        
        # 创建进度条
        self.bar = self.canvas.create_rectangle(0, 0, 0, height, fill=fg_color, width=0)
        
        # 绑定大小调整事件
        self.bind("<Configure>", self._on_resize)
        
        # 初始设置进度
        self.set(value)
    
    def _on_resize(self, event=None):
        """窗口大小调整事件"""
        self.update_bar()
    
    def update_bar(self):
        """更新进度条"""
        width = self.canvas.winfo_width()
        progress = min(1, max(0, self.value / self.maximum))
        bar_width = int(width * progress)
        self.canvas.coords(self.bar, 0, 0, bar_width, self.height)
    
    def set(self, value):
        """设置进度值"""
        old_value = self.value
        self.target_value = min(self.maximum, max(0, value))
        
        if self.animated and old_value != self.target_value:
            self._animate_to_target()
        else:
            self.value = self.target_value
            self.update_bar()
    
    def _animate_to_target(self):
        """动画过渡到目标值"""
        if abs(self.value - self.target_value) < 0.1:
            self.value = self.target_value
            self.update_bar()
            return
        
        # 平滑过渡
        step = (self.target_value - self.value) / 10
        self.value += step
        self.update_bar()
        self.after(16, self._animate_to_target)  # 约60fps


class ModernTabs(ttk.Notebook):
    """现代标签页组件，带有动画效果和自定义外观"""
    
    def __init__(self, master=None, **kwargs):
        """
        初始化现代标签页
        
        Args:
            master: 父窗口
            **kwargs: 其他ttk.Notebook参数
        """
        super().__init__(master, style='TNotebook', **kwargs)
        
        # 存储标签页信息
        self.tabs_info = {}
        
        # 绑定标签选择事件
        self.bind("<<NotebookTabChanged>>", self._on_tab_changed)
    
    def add_tab(self, child, text="", icon=None, **kwargs):
        """
        添加标签页
        
        Args:
            child: 标签页内容框架
            text: 标签文本
            icon: 标签图标
            **kwargs: 其他add参数
        """
        # 使用add方法添加标签页
        self.add(child, text=text, **kwargs)
        
        # 存储图标信息
        if icon:
            tab_id = self.tabs()[-1]
            self.tabs_info[tab_id] = {'icon': icon}
    
    def _on_tab_changed(self, event=None):
        """标签切换事件"""
        current_tab = self.select()
        
        # 这里可以添加标签切换的动画效果


class ModernTable(ttk.Frame):
    """现代表格组件，对Treeview的封装，支持排序、过滤和分页"""
    
    def __init__(self, master=None, columns=None, headings=None, data=None, 
               sortable=True, filterable=True, page_size=10, **kwargs):
        """
        初始化现代表格
        
        Args:
            master: 父窗口
            columns: 列标识符列表，例如 ["id", "name", "age"]
            headings: 列标题列表，例如 ["ID", "姓名", "年龄"]
            data: 初始数据行列表
            sortable: 是否可排序
            filterable: 是否可过滤
            page_size: 每页显示行数
            **kwargs: 其他ttk.Frame参数
        """
        super().__init__(master, **kwargs)
        
        self.columns = columns or []
        self.headings = headings or []
        self.sortable = sortable
        self.filterable = filterable
        self.page_size = page_size
        
        # 用于排序和过滤
        self.full_data = []
        self.filtered_data = []
        self.current_data = []
        self.sort_column = ""
        self.sort_ascending = True
        self.current_page = 0
        
        # 创建筛选框架
        if filterable:
            self.filter_frame = ttk.Frame(self)
            self.filter_frame.pack(fill=tk.X, padx=5, pady=5)
            
            self.search_box = ModernSearchBox(self.filter_frame, command=self.apply_filter)
            self.search_box.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 创建表格
        self.tree_frame = ttk.Frame(self)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建Treeview和滚动条
        self.tree_scroll_y = ttk.Scrollbar(self.tree_frame, orient=tk.VERTICAL)
        self.tree_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.tree = ttk.Treeview(self.tree_frame, style='Treeview',
                               columns=columns, show='headings',
                               yscrollcommand=self.tree_scroll_y.set)
        self.tree.pack(fill=tk.BOTH, expand=True)
        self.tree_scroll_y.config(command=self.tree.yview)
        
        # 设置列标题
        for col, heading in zip(columns, headings):
            self.tree.heading(col, text=heading, 
                           command=lambda c=col: self.sort_by_column(c) if sortable else None)
            self.tree.column(col, anchor="center", width=100)
        
        # 创建分页控制框架
        self.page_control_frame = ttk.Frame(self)
        self.page_control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 分页按钮
        self.prev_page_btn = ttk.Button(self.page_control_frame, text="上一页",
                                     command=self.prev_page, state=tk.DISABLED)
        self.prev_page_btn.pack(side=tk.LEFT, padx=5)
        
        self.page_info = ttk.Label(self.page_control_frame, text="第 1 页，共 1 页")
        self.page_info.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.next_page_btn = ttk.Button(self.page_control_frame, text="下一页",
                                     command=self.next_page, state=tk.DISABLED)
        self.next_page_btn.pack(side=tk.RIGHT, padx=5)
        
        # 加载数据
        if data:
            self.set_data(data)
    
    def set_data(self, data):
        """设置表格数据"""
        self.full_data = data
        self.filtered_data = data
        self.current_page = 0
        self.refresh_table()
    
    def refresh_table(self):
        """刷新表格显示"""
        # 清空现有数据
        for row in self.tree.get_children():
            self.tree.delete(row)
        
        # 计算当前页数据
        start_idx = self.current_page * self.page_size
        end_idx = start_idx + self.page_size
        self.current_data = self.filtered_data[start_idx:end_idx]
        
        # 插入当前页数据
        for row in self.current_data:
            self.tree.insert('', tk.END, values=row)
        
        # 更新分页信息
        total_pages = max(1, (len(self.filtered_data) + self.page_size - 1) // self.page_size)
        self.page_info.config(text=f"第 {self.current_page + 1} 页，共 {total_pages} 页")
        
        # 更新分页按钮状态
        self.prev_page_btn.config(state=tk.NORMAL if self.current_page > 0 else tk.DISABLED)
        self.next_page_btn.config(state=tk.NORMAL if self.current_page < total_pages - 1 else tk.DISABLED)
    
    def sort_by_column(self, column):
        """按列排序"""
        if self.sort_column == column:
            # 如果点击相同的列，则切换排序方向
            self.sort_ascending = not self.sort_ascending
        else:
            # 如果点击不同的列，则设置为升序
            self.sort_column = column
            self.sort_ascending = True
        
        # 获取列索引
        col_idx = self.columns.index(column)
        
        # 排序
        self.filtered_data.sort(key=lambda x: x[col_idx] if x[col_idx] is not None else "",
                              reverse=not self.sort_ascending)
        
        # 刷新表格
        self.current_page = 0
        self.refresh_table()
        
        # 更新标题显示排序方向
        for col in self.columns:
            if col == column:
                direction = "▲" if self.sort_ascending else "▼"
                self.tree.heading(col, text=f"{self.headings[self.columns.index(col)]} {direction}")
            else:
                self.tree.heading(col, text=self.headings[self.columns.index(col)])
    
    def apply_filter(self, filter_text):
        """应用过滤器"""
        if not filter_text:
            self.filtered_data = self.full_data
        else:
            filter_text = filter_text.lower()
            self.filtered_data = [row for row in self.full_data if any(
                str(cell).lower().find(filter_text) >= 0 for cell in row)]
        
        self.current_page = 0
        self.refresh_table()
    
    def prev_page(self):
        """前一页"""
        if self.current_page > 0:
            self.current_page -= 1
            self.refresh_table()
    
    def next_page(self):
        """后一页"""
        total_pages = (len(self.filtered_data) + self.page_size - 1) // self.page_size
        if self.current_page < total_pages - 1:
            self.current_page += 1
            self.refresh_table()


# 为其他常见UI组件添加类似的现代封装...
class ModernSlider(ttk.Scale):
    """现代滑块组件"""
    pass


class ModernRadioButton(ttk.Radiobutton):
    """现代单选按钮组件"""
    pass


class ModernCheckbox(ttk.Checkbutton):
    """现代复选框组件"""
    pass


class ModernCombobox(ttk.Combobox):
    """现代下拉框组件"""
    pass


class ModernIconButton(tk.Label):
    """现代化图标按钮"""
    
    def __init__(self, parent, icon: str, style: str = "primary", 
                 command: Callable = None, **kwargs):
        # 默认配置
        default_config = {
            "text": icon,
            "font": ("Segoe UI", 14),
            "cursor": "hand2",
            "padx": 12,
            "pady": 8
        }
        
        # 样式配置
        if style == "primary":
            default_config.update({
                "bg": ModernTheme.PRIMARY,
                "fg": ModernTheme.TEXT_ON_PRIMARY
            })
        elif style == "secondary":
            default_config.update({
                "bg": ModernTheme.SURFACE,
                "fg": ModernTheme.TEXT_PRIMARY
            })
        
        # 合并配置
        default_config.update(kwargs)
        super().__init__(parent, **default_config)
        
        # 绑定点击事件
        if command:
            self.bind("<Button-1>", lambda e: command())
        
        # 悬停效果
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        
        self.original_bg = self['bg']
        self.style = style
    
    def _on_enter(self, event):
        """鼠标进入"""
        if self.style == "primary":
            self.configure(bg=ModernTheme.PRIMARY_DARK)
        elif self.style == "secondary":
            self.configure(bg="#f0f0f0")  # 浅灰色
    
    def _on_leave(self, event):
        """鼠标离开"""
        self.configure(bg=self.original_bg)


class ModernTitleBar(tk.Frame):
    """现代化标题栏"""
    
    def __init__(self, parent, title: str, 
                 on_scrape: Callable = None, on_cancel: Callable = None, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.title = title
        self.on_scrape = on_scrape
        self.on_cancel = on_cancel
        self.scraping_active = False
        
        self.configure(
            bg=ModernTheme.BACKGROUND,
            height=60,
            padx=20,
            pady=10
        )
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建标题栏组件"""
        # 左侧：标题和版本
        left_frame = tk.Frame(self, bg=ModernTheme.BACKGROUND)
        left_frame.pack(side=tk.LEFT, fill=tk.Y)
        
        # 标题
        title_label = tk.Label(
            left_frame,
            text=self.title,
            font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["2xl"], "bold"),
            bg=ModernTheme.BACKGROUND,
            fg=ModernTheme.TEXT_PRIMARY
        )
        title_label.pack(side=tk.LEFT, padx=(0, 8))
        
        # 右侧：主题切换和操作按钮
        right_frame = tk.Frame(self, bg=ModernTheme.BACKGROUND)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 主题切换按钮
        self.theme_button = ModernIconButton(
            right_frame,
            icon="🌙",
            style="secondary",
            command=self._toggle_theme
        )
        self.theme_button.pack(side=tk.RIGHT, padx=8)
        
        # 数据更新按钮
        self.scrape_button = ModernButton(
            right_frame,
            text="📊 更新数据",
            style="Primary.TButton",
            command=self._on_scrape_click
        )
        self.scrape_button.pack(side=tk.RIGHT, padx=(0, 8))
        
        # 取消按钮（初始隐藏）
        self.cancel_button = ModernButton(
            right_frame,
            text="⏹ 停止",
            style="Error.TButton",
            command=self._on_cancel_click
        )
        # 不显示，需要时再显示
    
    def _toggle_theme(self):
        """切换主题"""
        new_theme = ModernThemeManager.toggle_theme()
        
        # 更新主题按钮图标
        new_icon = "🌙" if new_theme == "light" else "☀️"
        self.theme_button.configure(text=new_icon)
        
        # 设置全局主题
        modern_theme.set_theme(new_theme)
        
        # TODO: 这里需要通知主应用重新应用样式
        # 可以通过回调函数或事件系统实现
    
    def _on_scrape_click(self):
        """处理数据更新按钮点击"""
        if self.on_scrape:
            self.on_scrape()
    
    def _on_cancel_click(self):
        """处理取消按钮点击"""
        if self.on_cancel:
            self.on_cancel()
    
    def set_scraping_state(self, active: bool):
        """设置爬取状态"""
        self.scraping_active = active
        if active:
            self.scrape_button.pack_forget()
            self.cancel_button.pack(side=tk.RIGHT, padx=(0, 8))
            self.scrape_button.configure(text="⏳ 更新中...")
        else:
            self.cancel_button.pack_forget()
            self.scrape_button.pack(side=tk.RIGHT, padx=(0, 8))
            self.scrape_button.configure(text="📊 更新数据")


class ModernStatusBar(tk.Frame):
    """现代化状态栏"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.configure(
            bg=ModernTheme.CARD_BG,
            height=40
        )
        self._create_widgets()
    
    def _create_widgets(self):
        """创建状态栏组件"""
        # 状态信息
        self.status_label = tk.Label(
            self,
            text="就绪",
            font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["md"]),
            bg=ModernTheme.CARD_BG,
            fg=ModernTheme.TEXT_PRIMARY
        )
        self.status_label.pack(side=tk.LEFT, padx=16, pady=8)
        
        # 分隔符
        separator = tk.Frame(
            self,
            bg=ModernTheme.BORDER,
            width=1,
            height=20
        )
        separator.pack(side=tk.LEFT, padx=8, pady=10)
        
        # 加载状态
        self.load_label = tk.Label(
            self,
            text="",
            font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["md"]),
            bg=ModernTheme.CARD_BG,
            fg=ModernTheme.TEXT_SECONDARY
        )
        self.load_label.pack(side=tk.LEFT, padx=8, pady=8)
        
        # 右侧：时间显示
        self.time_label = tk.Label(
            self,
            text="",
            font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["md"]),
            bg=ModernTheme.CARD_BG,
            fg=ModernTheme.TEXT_SECONDARY
        )
        self.time_label.pack(side=tk.RIGHT, padx=16, pady=8)
        
        # 更新时间
        self._update_time()
    
    def _update_time(self):
        """更新时间显示"""
        current_time = time.strftime("%H:%M:%S")
        self.time_label.configure(text=current_time)
        self.after(1000, self._update_time)
    
    def set_status(self, status: str):
        """设置状态文本"""
        self.status_label.configure(text=status)
    
    def set_load_status(self, matches: int, leagues: int):
        """设置加载状态"""
        text = f"已加载 {matches} 场比赛，{leagues} 个联赛"
        self.load_label.configure(text=text)


class ModernLeagueSelector(tk.Frame):
    """现代化联赛选择器"""
    
    def __init__(self, parent, leagues: List[str] = None,
                 on_select: Callable = None, **kwargs):
        super().__init__(parent, **kwargs)
        self.leagues = leagues or []
        self.on_select = on_select
        self.selected_league = tk.StringVar()
        
        self.configure(bg=ModernTheme.BACKGROUND)
        self._create_widgets()
    
    def _create_widgets(self):
        """创建联赛选择器组件"""
        # 标题
        title_label = tk.Label(
            self,
            text="联赛选择",
            font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["lg"], "bold"),
            bg=ModernTheme.BACKGROUND,
            fg=ModernTheme.TEXT_PRIMARY
        )
        title_label.pack(anchor="w", padx=16, pady=(16, 8))
        
        # 选择器容器
        selector_frame = tk.Frame(self, bg=ModernTheme.BACKGROUND)
        selector_frame.pack(fill=tk.X, padx=16, pady=(0, 8))
        
        # 联赛下拉框
        self.league_combo = ttk.Combobox(
            selector_frame,
            textvariable=self.selected_league,
            values=self.leagues,
            state="readonly",
            font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["md"]),
            width=30
        )
        self.league_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 筛选按钮
        self.filter_button = ModernButton(
            selector_frame,
            text="筛选",
            style="Primary.TButton",
            command=self._on_filter_click
        )
        self.filter_button.pack(side=tk.RIGHT, padx=(8, 0))
        
        # 绑定事件
        self.league_combo.bind("<<ComboboxSelected>>", self._on_select)
        
        # 筛选状态
        self.status_label = tk.Label(
            self,
            text="",
            font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["sm"]),
            bg=ModernTheme.BACKGROUND,
            fg=ModernTheme.TEXT_SECONDARY
        )
        self.status_label.pack(anchor="w", padx=16, pady=(0, 16))
        
        # 默认选择
        if self.leagues:
            self.league_combo.current(0)
    
    def _on_select(self, event=None):
        """联赛选择事件"""
        if self.on_select:
            selected = self.selected_league.get()
            self.on_select(selected)
    
    def _on_filter_click(self):
        """筛选按钮点击"""
        self._on_select()
    
    def update_leagues(self, leagues: List[str]):
        """更新联赛列表"""
        self.leagues = leagues
        self.league_combo['values'] = leagues
        if leagues:
            self.league_combo.current(0)
    
    def set_status(self, status: str):
        """设置状态文本"""
        self.status_label.configure(text=status)


class ModernMatchList(tk.Frame):
    """现代化比赛列表"""
    
    def __init__(self, parent, on_select: Callable = None, **kwargs):
        super().__init__(parent, **kwargs)
        self.on_select = on_select
        
        self.configure(bg=ModernTheme.BACKGROUND)
        self._create_widgets()
    
    def _create_widgets(self):
        """创建比赛列表组件"""
        # 列表容器 - 移除标题，直接使用框架
        list_frame = tk.Frame(self, bg=ModernTheme.BACKGROUND)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建树视图 - 增加高度
        columns = ("league", "match", "time", "status")
        self.tree = ttk.Treeview(
            list_frame,
            columns=columns,
            show="headings",
            height=20  # 增加高度
        )
        
        # 定义列
        self.tree.heading("league", text="联赛")
        self.tree.heading("match", text="比赛")
        self.tree.heading("time", text="时间")
        self.tree.heading("status", text="状态")
        
        # 调整列宽以更好地显示比赛信息
        self.tree.column("league", width=80, anchor="center")
        self.tree.column("match", width=220, anchor="w")  # 左对齐，增加宽度
        self.tree.column("time", width=60, anchor="center")
        self.tree.column("status", width=40, anchor="center")
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定事件
        self.tree.bind("<Double-1>", self._on_select)
        self.tree.bind("<Return>", self._on_select)
        self.tree.bind("<ButtonRelease-1>", self._on_click)  # 单击也可以选择
        
        # 配置样式标签
        self._configure_tags()
    
    def _configure_tags(self):
        """配置样式标签 - 增强视觉效果"""
        # 有马会数据：绿色
        self.tree.tag_configure('has_hkjc', 
                               background='#dcfce7', 
                               foreground='#166534',
                               font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["md"]))
        
        # 有数据无马会：黄色
        self.tree.tag_configure('has_odds_no_hkjc', 
                               background='#fef3c7', 
                               foreground='#92400e',
                               font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["md"]))
        
        # 无数据：红色
        self.tree.tag_configure('no_odds', 
                               background='#fee2e2', 
                               foreground='#991b1b',
                               font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZES["md"]))
        
        # 选中项样式
        self.tree.tag_configure('selected', 
                               background=ModernTheme.PRIMARY_LIGHT,
                               foreground=ModernTheme.TEXT_PRIMARY)
    
    def _on_click(self, event):
        """单击选择事件"""
        self._on_select(event)
    
    def _on_select(self, event):
        """选择事件"""
        selection = self.tree.selection()
        if selection and self.on_select:
            item = self.tree.item(selection[0])
            # 从标签中获取索引
            tags = item['tags']
            if tags:
                try:
                    index = int(tags[0])
                    self.on_select(index)
                except (ValueError, IndexError):
                    pass
    
    def update_matches(self, matches: List[Dict]):
        """更新比赛列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加新项目
        for i, match in enumerate(matches):
            # 确定状态和标签
            has_odds = match.get('has_odds', False)
            has_hkjc = match.get('has_hkjc', False)
            
            if not has_odds:
                status = "❌"
                tag = 'no_odds'
            elif has_hkjc:
                status = "📊"
                tag = 'has_hkjc'
            else:
                status = "⚠️"
                tag = 'has_odds_no_hkjc'
            
            # 插入项目
            self.tree.insert("", tk.END, values=(
                match.get('league', ''),
                match.get('match_name', ''),
                match.get('time', ''),
                status
            ), tags=(str(i), tag)) 