"""
历史赔率区间分析图表可视化组件

该模块负责创建matplotlib折线图，展示历史赔率在不同区间的变化趋势。
"""

import logging
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any
import tkinter as tk
from tkinter import ttk

from .interval_visualizer import VisualizationData


class HistoricalChartVisualizer:
    """历史赔率区间分析图表可视化器"""
    
    def __init__(self, parent_frame: tk.Widget = None):
        """
        初始化图表可视化器
        
        Args:
            parent_frame: 父级tkinter组件
        """
        self.parent_frame = parent_frame
        self.logger = logging.getLogger(__name__)
        
        # 图表配置
        self.figure_size = (12, 8)
        self.dpi = 100
        
        # 创建matplotlib图形
        self.figure = Figure(figsize=self.figure_size, dpi=self.dpi)
        self.ax = self.figure.add_subplot(111)
        
        # 图表组件
        self.canvas = None
        self.toolbar = None
        
        # 数据存储
        self.current_data = None
        self.y_labels_mapping = {}
        
        # 样式配置
        self.setup_chart_style()
        
    def setup_chart_style(self):
        """设置图表样式"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 图表背景色
        self.figure.patch.set_facecolor('white')
        self.ax.set_facecolor('#f8f9fa')
        
        # 网格样式
        self.ax.grid(True, linestyle='--', alpha=0.7, color='#cccccc')
        
        # 边框样式
        for spine in self.ax.spines.values():
            spine.set_color('#dddddd')
            spine.set_linewidth(1)
    
    def create_canvas(self, parent: tk.Widget) -> FigureCanvasTkAgg:
        """
        创建matplotlib画布
        
        Args:
            parent: 父级tkinter组件
            
        Returns:
            FigureCanvasTkAgg: matplotlib画布对象
        """
        if self.canvas:
            self.canvas.get_tk_widget().destroy()
            
        self.canvas = FigureCanvasTkAgg(self.figure, parent)
        self.canvas.draw()
        
        return self.canvas
    
    def plot_historical_intervals(self, visualization_data: VisualizationData, 
                                 company_name: str = "未知公司") -> bool:
        """
        绘制历史赔率区间折线图（合并显示胜平负区间）
        
        Args:
            visualization_data: 可视化数据对象
            company_name: 博彩公司名称
            
        Returns:
            bool: 绘制是否成功
        """
        try:
            if not visualization_data or not visualization_data.timestamps:
                self.logger.warning("没有可视化数据")
                return False
            
            # 清除之前的图表
            self.figure.clear()
            
            # 创建单个图表显示所有区间
            self.ax = self.figure.add_subplot(1, 1, 1)
            self.setup_chart_style()
            
            # 存储数据和标签映射
            self.current_data = visualization_data
            self.y_labels_mapping = visualization_data.interval_labels
            
            # 转换时间戳为datetime对象
            timestamps = self._parse_timestamps(visualization_data.timestamps)
            
            if not timestamps:
                self.logger.error("时间戳解析失败")
                return False
            
            # 存储时间戳用于交互
            self.timestamps = timestamps
            
            # 调试信息
            self.logger.info(f"解析了 {len(timestamps)} 个时间戳")
            self.logger.info(f"时间范围: {timestamps[0]} 到 {timestamps[-1]}")
            self.logger.info(f"数据点数量: 主胜={len(visualization_data.home_y_coords)}, 平局={len(visualization_data.draw_y_coords)}, 客胜={len(visualization_data.away_y_coords)}")
            
            # 绘制合并的区间图
            self._plot_combined_intervals(timestamps, visualization_data, company_name)
            
            # 添加交互功能
            self._setup_interactive_features()
            
            # 调整布局，增加边距避免注释被遮挡
            self.figure.tight_layout(pad=3.0)
            
            # 刷新画布
            if self.canvas:
                self.canvas.draw()
            
            self.logger.info(f"成功绘制 {company_name} 的历史区间图表")
            return True
            
        except Exception as e:
            self.logger.error(f"绘制历史区间图表时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False
    
    def _parse_timestamps(self, timestamps: List[str]) -> List[datetime]:
        """
        解析时间戳字符串为datetime对象
        
        Args:
            timestamps: 时间戳字符串列表
            
        Returns:
            List[datetime]: datetime对象列表
        """
        parsed_timestamps = []
        
        # 常见的时间格式，包括新的格式
        time_formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%Y-%m-%d',
            '%m/%d/%Y %H:%M:%S',
            '%m/%d/%Y %H:%M',
            '%m/%d/%Y',
            '%m-%d %H:%M',  # 新增：支持 "08-04 00:42" 格式
            '%m-%d %H:%M:%S',  # 新增：支持 "08-04 00:42:30" 格式
        ]
        
        for i, timestamp_str in enumerate(timestamps):
            parsed = None
            
            # 记录原始时间戳用于调试
            self.logger.debug(f"解析时间戳 {i}: '{timestamp_str}'")
            
            for fmt in time_formats:
                try:
                    parsed = datetime.strptime(timestamp_str, fmt)
                    # 如果是月-日格式，需要添加年份
                    if fmt in ['%m-%d %H:%M', '%m-%d %H:%M:%S']:
                        # 使用当前年份
                        current_year = datetime.now().year
                        parsed = parsed.replace(year=current_year)
                    self.logger.debug(f"成功解析为: {parsed} (格式: {fmt})")
                    break
                except ValueError:
                    continue
            
            if parsed is None:
                # 如果所有格式都失败，尝试使用pandas解析
                try:
                    parsed = pd.to_datetime(timestamp_str)
                    if hasattr(parsed, 'to_pydatetime'):
                        parsed = parsed.to_pydatetime()
                    self.logger.debug(f"pandas解析成功: {parsed}")
                except Exception:
                    self.logger.warning(f"无法解析时间戳: {timestamp_str}")
                    # 使用基准时间加上索引作为默认值，确保时间序列
                    base_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                    parsed = base_time + pd.Timedelta(hours=i)
                    self.logger.debug(f"使用默认时间: {parsed}")
            
            parsed_timestamps.append(parsed)
        
        # 调试信息：显示解析结果
        if parsed_timestamps:
            self.logger.info(f"时间戳解析完成: {len(parsed_timestamps)} 个时间点")
            self.logger.info(f"时间范围: {parsed_timestamps[0]} 到 {parsed_timestamps[-1]}")
            
            # 检查是否所有时间戳都相同（这会导致垂直线问题）
            unique_times = set(parsed_timestamps)
            if len(unique_times) == 1:
                self.logger.warning("警告：所有时间戳都相同，将生成时间序列")
                # 生成时间序列
                base_time = parsed_timestamps[0]
                parsed_timestamps = [base_time + pd.Timedelta(minutes=i*30) for i in range(len(timestamps))]
                self.logger.info(f"生成时间序列: {parsed_timestamps[0]} 到 {parsed_timestamps[-1]}")
        
        return parsed_timestamps
    
    def _plot_line(self, x_data: List[datetime], y_data: List[int], 
                   label: str, color: str, marker: str):
        """
        绘制单条折线
        
        Args:
            x_data: X轴数据（时间）
            y_data: Y轴数据（区间坐标）
            label: 线条标签
            color: 线条颜色
            marker: 标记样式
        """
        self.ax.plot(x_data, y_data, 
                    label=label, 
                    color=color, 
                    marker=marker, 
                    markersize=6,
                    linewidth=2,
                    markerfacecolor=color,
                    markeredgecolor='white',
                    markeredgewidth=1,
                    alpha=0.8)
    
    def _setup_chart_labels(self, company_name: str):
        """
        设置图表标题和轴标签
        
        Args:
            company_name: 博彩公司名称
        """
        self.ax.set_title(f'{company_name} - 历史赔率区间变化趋势', 
                         fontsize=16, fontweight='bold', pad=20)
        self.ax.set_xlabel('时间', fontsize=12, fontweight='bold')
        self.ax.set_ylabel('区间类型', fontsize=12, fontweight='bold')
    
    def _setup_y_axis(self):
        """设置Y轴标签和范围"""
        if not self.y_labels_mapping:
            return
        
        # 获取所有Y坐标值并排序
        y_coords = sorted(self.y_labels_mapping.keys())
        
        # 设置Y轴刻度
        self.ax.set_yticks(y_coords)
        
        # 设置Y轴标签，只显示主要区间以避免过于拥挤
        y_labels = []
        for y_coord in y_coords:
            interval_name = self.y_labels_mapping.get(y_coord, '')
            # 简化标签显示
            if len(interval_name) > 8:
                # 对于长标签，只显示关键部分
                if '盘' in interval_name:
                    simplified = interval_name.split('盘')[0] + '盘'
                elif '平' in interval_name:
                    simplified = interval_name.replace('平', '')[:6] + '平'
                else:
                    simplified = interval_name[:8]
                y_labels.append(simplified)
            else:
                y_labels.append(interval_name)
        
        self.ax.set_yticklabels(y_labels, fontsize=8, rotation=0)
        
        # 设置Y轴范围，留出一些边距
        if y_coords:
            y_min, y_max = min(y_coords), max(y_coords)
            margin = (y_max - y_min) * 0.05
            self.ax.set_ylim(y_min - margin, y_max + margin)
    
    def _setup_x_axis(self, timestamps: List[datetime]):
        """
        设置X轴时间标签
        
        Args:
            timestamps: 时间戳列表
        """
        if not timestamps:
            return
        
        try:
            # 使用AutoDateLocator，限制最大刻度数量
            self.ax.xaxis.set_major_locator(mdates.AutoDateLocator(maxticks=8))
            
            # 根据时间跨度选择合适的格式
            time_span = timestamps[-1] - timestamps[0]
            
            if time_span.days > 30:
                # 超过一个月，显示月-日
                date_format = mdates.DateFormatter('%m-%d')
            elif time_span.days > 7:
                # 超过一周，显示月-日
                date_format = mdates.DateFormatter('%m-%d')
            elif time_span.days > 1:
                # 超过一天，显示月-日 时:分
                date_format = mdates.DateFormatter('%m-%d %H:%M')
            else:
                # 一天内，显示时:分
                date_format = mdates.DateFormatter('%H:%M')
            
            self.ax.xaxis.set_major_formatter(date_format)
            
            # 旋转X轴标签以避免重叠
            plt.setp(self.ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
            
        except Exception as e:
            self.logger.error(f"设置X轴时出错: {e}")
            # 使用最简单的自动定位器作为备选
            self.ax.xaxis.set_major_locator(mdates.AutoDateLocator(maxticks=6))
            self.ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    
    def _setup_legend(self):
        """设置图例"""
        legend = self.ax.legend(loc='upper right', 
                               frameon=True, 
                               fancybox=True, 
                               shadow=True,
                               fontsize=10)
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.9)
    
    def clear_chart(self):
        """清除图表内容"""
        self.ax.clear()
        self.setup_chart_style()
        if self.canvas:
            self.canvas.draw()
    
    def save_chart(self, filepath: str, dpi: int = 300) -> bool:
        """
        保存图表到文件
        
        Args:
            filepath: 保存路径
            dpi: 图片分辨率
            
        Returns:
            bool: 保存是否成功
        """
        try:
            self.figure.savefig(filepath, dpi=dpi, bbox_inches='tight', 
                              facecolor='white', edgecolor='none')
            self.logger.info(f"图表已保存到: {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"保存图表时出错: {e}")
            return False
    
    def add_interactive_features(self):
        """添加交互功能"""
        if not self.canvas:
            return
        
        # 添加鼠标悬停显示数据点信息的功能
        def on_hover(event):
            if event.inaxes != self.ax:
                return
            
            # 这里可以添加更多的交互功能
            # 比如显示具体的区间信息、赔率值等
            pass
        
        # 连接事件
        self.canvas.mpl_connect('motion_notify_event', on_hover)
    
    def update_chart_data(self, visualization_data: VisualizationData, 
                         company_name: str = "未知公司"):
        """
        更新图表数据
        
        Args:
            visualization_data: 新的可视化数据
            company_name: 博彩公司名称
        """
        self.plot_historical_intervals(visualization_data, company_name)
    
    def get_chart_statistics(self) -> Dict[str, Any]:
        """
        获取图表统计信息
        
        Returns:
            dict: 统计信息字典
        """
        if not self.current_data:
            return {}
        
        stats = {
            'data_points': len(self.current_data.timestamps),
            'time_range': {
                'start': self.current_data.timestamps[0] if self.current_data.timestamps else None,
                'end': self.current_data.timestamps[-1] if self.current_data.timestamps else None
            },
            'interval_distribution': {
                'home': self._get_interval_distribution(self.current_data.home_y_coords),
                'draw': self._get_interval_distribution(self.current_data.draw_y_coords),
                'away': self._get_interval_distribution(self.current_data.away_y_coords)
            }
        }
        
        return stats
    
    def _get_interval_distribution(self, y_coords: List[int]) -> Dict[str, int]:
        """
        获取区间分布统计
        
        Args:
            y_coords: Y坐标列表
            
        Returns:
            dict: 区间分布字典
        """
        distribution = {}
        
        for y_coord in y_coords:
            interval_name = self.y_labels_mapping.get(y_coord, '未知')
            distribution[interval_name] = distribution.get(interval_name, 0) + 1
        
        return distribution
    
    def _plot_combined_intervals(self, timestamps: List[datetime], visualization_data: VisualizationData, company_name: str):
        """
        绘制合并的胜平负区间图
        
        Args:
            timestamps: 时间戳列表
            visualization_data: 可视化数据
            company_name: 博彩公司名称
        """
        # 绘制三条折线，使用红黄蓝颜色
        self.ax.plot(timestamps, visualization_data.home_y_coords, 
                    label='主胜区间', color='#FF0000', marker='o', 
                    markersize=5, linewidth=2.5, markerfacecolor='#FF0000',
                    markeredgecolor='white', markeredgewidth=1, alpha=0.9)
        
        self.ax.plot(timestamps, visualization_data.draw_y_coords, 
                    label='平局区间', color='#FFD700', marker='s', 
                    markersize=5, linewidth=2.5, markerfacecolor='#FFD700',
                    markeredgecolor='white', markeredgewidth=1, alpha=0.9)
        
        self.ax.plot(timestamps, visualization_data.away_y_coords, 
                    label='客胜区间', color='#0066FF', marker='^', 
                    markersize=5, linewidth=2.5, markerfacecolor='#0066FF',
                    markeredgecolor='white', markeredgewidth=1, alpha=0.9)
        
        # 设置标题和标签
        self.ax.set_title(f'{company_name} - 历史赔率区间变化趋势', 
                         fontsize=16, fontweight='bold', pad=20)
        self.ax.set_xlabel('时间', fontsize=12, fontweight='bold')
        self.ax.set_ylabel('区间类型', fontsize=12, fontweight='bold')
        
        # 设置简化的Y轴标签
        self._setup_simplified_y_axis()
        
        # 设置X轴时间格式
        self._setup_x_axis(timestamps)
        
        # 添加图例
        self._setup_legend()
        
        # 添加时间点标记
        self._add_time_point_markers()
    
    def _setup_simplified_y_axis(self):
        """设置简化的Y轴标签，只显示关键区间避免重叠"""
        if not self.y_labels_mapping:
            return
        
        # 获取所有Y坐标值并排序
        all_y_coords = sorted(self.y_labels_mapping.keys())
        
        if not all_y_coords:
            return
        
        # 设置Y轴范围，留出边距
        y_min, y_max = min(all_y_coords), max(all_y_coords)
        margin = (y_max - y_min) * 0.08
        self.ax.set_ylim(y_min - margin, y_max + margin)
        
        # 智能选择要显示的Y轴标签，避免重叠
        max_labels = 15  # 最多显示15个标签
        
        if len(all_y_coords) <= max_labels:
            # 如果区间数量不多，显示所有区间
            selected_y_coords = all_y_coords
        else:
            # 如果区间太多，智能选择关键区间
            selected_y_coords = self._select_key_intervals(all_y_coords, max_labels)
        
        # 创建简化的区间标签
        simplified_labels = []
        for y_coord in selected_y_coords:
            original_label = self.y_labels_mapping.get(y_coord, '')
            
            # 极简化标签逻辑，只显示最关键信息
            if '主' in original_label:
                if '超低' in original_label or '低' in original_label:
                    simplified_labels.append('主低')
                elif '中' in original_label:
                    simplified_labels.append('主中')
                elif '高' in original_label:
                    simplified_labels.append('主高')
                else:
                    simplified_labels.append('主胜')
            elif '平' in original_label:
                if '低' in original_label:
                    simplified_labels.append('平低')
                elif '中' in original_label:
                    simplified_labels.append('平中')
                elif '高' in original_label:
                    simplified_labels.append('平高')
                else:
                    simplified_labels.append('平局')
            elif '客' in original_label:
                if '超低' in original_label or '低' in original_label:
                    simplified_labels.append('客低')
                elif '中' in original_label:
                    simplified_labels.append('客中')
                elif '高' in original_label:
                    simplified_labels.append('客高')
                else:
                    simplified_labels.append('客胜')
            else:
                # 对于其他标签，取前3个字符
                simplified_labels.append(original_label[:3] if len(original_label) > 3 else original_label)
        
        # 设置Y轴刻度和标签
        self.ax.set_yticks(selected_y_coords)
        self.ax.set_yticklabels(simplified_labels, fontsize=8, rotation=0)
        
        # 添加网格线帮助阅读
        self.ax.grid(True, linestyle='--', alpha=0.3, color='#cccccc')
    
    def _select_key_intervals(self, all_y_coords: List[int], max_labels: int) -> List[int]:
        """
        智能选择关键区间，避免标签重叠
        
        Args:
            all_y_coords: 所有Y坐标
            max_labels: 最大标签数量
            
        Returns:
            List[int]: 选择的关键Y坐标
        """
        if len(all_y_coords) <= max_labels:
            return all_y_coords
        
        # 策略1：均匀分布选择
        step = len(all_y_coords) // max_labels
        selected = []
        
        for i in range(0, len(all_y_coords), step):
            if len(selected) < max_labels:
                selected.append(all_y_coords[i])
        
        # 确保包含最小值和最大值
        if all_y_coords[0] not in selected:
            selected[0] = all_y_coords[0]
        if all_y_coords[-1] not in selected:
            selected[-1] = all_y_coords[-1]
        
        return sorted(selected)
    
    def _add_time_point_markers(self):
        """在X轴上添加时间点标记"""
        if not hasattr(self, 'timestamps') or not self.current_data:
            return
        
        try:
            # 在图表上添加时间点标记
            for i, timestamp in enumerate(self.timestamps):
                x_pos = mdates.date2num(timestamp)
                
                # 在X轴底部添加小的垂直线标记
                self.ax.axvline(x=x_pos, color='#E0E0E0', linestyle=':', alpha=0.6, linewidth=0.8)
            
            # 高亮显示关键时间点
            self._highlight_key_timepoints()
            
        except Exception as e:
            self.logger.error(f"添加时间点标记时出错: {e}")
    
    def _highlight_key_timepoints(self):
        """高亮显示关键时间点"""
        if not hasattr(self, 'timestamps') or len(self.timestamps) < 2:
            return
        
        try:
            # 高亮第一个时间点（开盘）
            first_time = mdates.date2num(self.timestamps[0])
            self.ax.axvline(x=first_time, color='#28A745', linestyle='-', alpha=0.8, linewidth=2)
            
            # 高亮最后一个时间点（收盘）
            last_time = mdates.date2num(self.timestamps[-1])
            self.ax.axvline(x=last_time, color='#DC3545', linestyle='-', alpha=0.8, linewidth=2)
            
        except Exception as e:
            self.logger.error(f"高亮关键时间点时出错: {e}")
    
    def _setup_interactive_features(self):
        """设置交互功能"""
        if not self.canvas:
            return
        
        # 创建注释框用于显示点击信息，调整位置避免被遮挡
        self.annotation = self.ax.annotate('', xy=(0,0), xytext=(0,0), 
                                          textcoords="offset points",
                                          bbox=dict(boxstyle="round,pad=0.5", fc="white", 
                                                   ec="gray", alpha=0.95),
                                          arrowprops=dict(arrowstyle="->", 
                                                         connectionstyle="arc3,rad=0.1",
                                                         color="gray"))
        self.annotation.set_visible(False)
        
        # 添加点击事件处理
        def on_click(event):
            if event.inaxes != self.ax:
                return
            
            # 找到最近的数据点
            if hasattr(self, 'timestamps') and self.current_data:
                closest_index = self._find_closest_point(event.xdata, event.ydata)
                if closest_index is not None:
                    self._show_detailed_point_info(closest_index, event.xdata, event.ydata)
        
        # 添加悬停事件处理
        def on_hover(event):
            if event.inaxes != self.ax:
                self.annotation.set_visible(False)
                if self.canvas:
                    self.canvas.draw_idle()
                return
            
            # 找到最近的数据点并显示悬停信息
            if hasattr(self, 'timestamps') and self.current_data:
                closest_index = self._find_closest_point(event.xdata, event.ydata)
                if closest_index is not None:
                    self._show_hover_info(closest_index, event.xdata, event.ydata)
                else:
                    self.annotation.set_visible(False)
                    if self.canvas:
                        self.canvas.draw_idle()
        
        # 连接事件
        self.canvas.mpl_connect('button_press_event', on_click)
        self.canvas.mpl_connect('motion_notify_event', on_hover)
    
    def _find_closest_point(self, x_click, y_click):
        """
        找到最接近点击位置的数据点
        
        Args:
            x_click: 点击的X坐标（时间）
            y_click: 点击的Y坐标
            
        Returns:
            int: 最接近的数据点索引，如果没找到返回None
        """
        if not hasattr(self, 'timestamps') or not self.current_data:
            return None
        
        try:
            # 转换点击的X坐标为时间戳
            click_time = mdates.num2date(x_click)
            
            # 确保click_time是naive datetime（移除时区信息）
            if click_time.tzinfo is not None:
                click_time = click_time.replace(tzinfo=None)
            
            # 找到最接近的时间点
            min_distance = float('inf')
            closest_index = None
            
            for i, timestamp in enumerate(self.timestamps):
                # 确保timestamp也是naive datetime
                if hasattr(timestamp, 'tzinfo') and timestamp.tzinfo is not None:
                    timestamp = timestamp.replace(tzinfo=None)
                
                time_distance = abs((timestamp - click_time).total_seconds())
                
                # 检查三条线的Y坐标距离
                home_y_distance = abs(self.current_data.home_y_coords[i] - y_click)
                draw_y_distance = abs(self.current_data.draw_y_coords[i] - y_click)
                away_y_distance = abs(self.current_data.away_y_coords[i] - y_click)
                
                # 选择最近的线
                y_distance = min(home_y_distance, draw_y_distance, away_y_distance)
                
                # 综合时间和Y坐标距离
                total_distance = time_distance / 3600 + y_distance  # 时间距离转换为小时
                
                if total_distance < min_distance:
                    min_distance = total_distance
                    closest_index = i
            
            # 只有距离足够近才返回索引
            if min_distance < 8:  # 调整阈值
                return closest_index
            
        except Exception as e:
            self.logger.error(f"查找最近数据点时出错: {e}")
        
        return None
    
    def _show_detailed_point_info(self, index, x_mouse, y_mouse):
        """
        显示详细的数据点信息（点击时）
        
        Args:
            index: 数据点索引
            x_mouse: 鼠标X坐标
            y_mouse: 鼠标Y坐标
        """
        if not self.current_data or index >= len(self.current_data.timestamps):
            return
        
        try:
            # 获取数据点信息
            timestamp = self.current_data.timestamps[index]
            home_y = self.current_data.home_y_coords[index]
            draw_y = self.current_data.draw_y_coords[index]
            away_y = self.current_data.away_y_coords[index]
            
            # 获取详细区间名称
            home_interval = self.y_labels_mapping.get(home_y, '未知')
            draw_interval = self.y_labels_mapping.get(draw_y, '未知')
            away_interval = self.y_labels_mapping.get(away_y, '未知')
            
            # 格式化时间显示
            formatted_time = self._format_timestamp_for_display(timestamp)
            
            # 判断点击最接近哪条线
            home_distance = abs(home_y - y_mouse)
            draw_distance = abs(draw_y - y_mouse)
            away_distance = abs(away_y - y_mouse)
            
            min_distance = min(home_distance, draw_distance, away_distance)
            
            if min_distance == home_distance:
                # 最接近主胜线
                info_text = f"时间: {formatted_time}\n主胜详细区间: {home_interval}"
                target_y = home_y
                color = '#FF0000'
            elif min_distance == draw_distance:
                # 最接近平局线
                info_text = f"时间: {formatted_time}\n平局详细区间: {draw_interval}"
                target_y = draw_y
                color = '#FFD700'
            else:
                # 最接近客胜线
                info_text = f"时间: {formatted_time}\n客胜详细区间: {away_interval}"
                target_y = away_y
                color = '#0066FF'
            
            # 智能计算注释框位置，避免被遮挡
            offset_x, offset_y = self._calculate_smart_annotation_offset(
                mdates.date2num(self.timestamps[index]), target_y, x_mouse, y_mouse
            )
            
            # 更新注释位置和内容
            self.annotation.xy = (mdates.date2num(self.timestamps[index]), target_y)
            self.annotation.xytext = (offset_x, offset_y)
            self.annotation.set_text(info_text)
            self.annotation.get_bbox_patch().set_facecolor(color)
            self.annotation.get_bbox_patch().set_alpha(0.9)
            self.annotation.set_visible(True)
            
            # 刷新画布
            if self.canvas:
                self.canvas.draw()
                
        except Exception as e:
            self.logger.error(f"显示详细数据点信息时出错: {e}")
    
    def _show_hover_info(self, index, x_mouse, y_mouse):
        """
        显示悬停信息（显示具体区间信息）
        
        Args:
            index: 数据点索引
            x_mouse: 鼠标X坐标
            y_mouse: 鼠标Y坐标
        """
        if not self.current_data or index >= len(self.current_data.timestamps):
            return
        
        try:
            # 获取数据点信息
            timestamp = self.current_data.timestamps[index]
            home_y = self.current_data.home_y_coords[index]
            draw_y = self.current_data.draw_y_coords[index]
            away_y = self.current_data.away_y_coords[index]
            
            # 获取具体的区间名称
            home_interval = self.y_labels_mapping.get(home_y, '未知')
            draw_interval = self.y_labels_mapping.get(draw_y, '未知')
            away_interval = self.y_labels_mapping.get(away_y, '未知')
            
            # 格式化时间显示
            formatted_time = self._format_timestamp_for_display(timestamp)
            
            # 判断悬停最接近哪条线
            home_distance = abs(home_y - y_mouse)
            draw_distance = abs(draw_y - y_mouse)
            away_distance = abs(away_y - y_mouse)
            
            min_distance = min(home_distance, draw_distance, away_distance)
            
            if min_distance == home_distance:
                # 显示具体的主胜区间
                info_text = f"{formatted_time}\n{home_interval}"
                target_y = home_y
                color = '#FF0000'
            elif min_distance == draw_distance:
                # 显示具体的平局区间
                info_text = f"{formatted_time}\n{draw_interval}"
                target_y = draw_y
                color = '#FFD700'
            else:
                # 显示具体的客胜区间
                info_text = f"{formatted_time}\n{away_interval}"
                target_y = away_y
                color = '#0066FF'
            
            # 更新注释
            self.annotation.xy = (mdates.date2num(self.timestamps[index]), target_y)
            self.annotation.xytext = (20, 20)  # 固定偏移，避免复杂计算
            self.annotation.set_text(info_text)
            self.annotation.get_bbox_patch().set_facecolor(color)
            self.annotation.get_bbox_patch().set_alpha(0.8)
            self.annotation.set_visible(True)
            
            # 刷新画布
            if self.canvas:
                self.canvas.draw_idle()
                
        except Exception as e:
            self.logger.error(f"显示悬停信息时出错: {e}")
    
    def _calculate_smart_annotation_offset(self, x_pos, y_pos, x_mouse, y_mouse):
        """
        智能计算注释框偏移位置，避免被边框遮挡
        
        Args:
            x_pos: 数据点X坐标
            y_pos: 数据点Y坐标
            x_mouse: 鼠标X坐标
            y_mouse: 鼠标Y坐标
            
        Returns:
            tuple: (offset_x, offset_y) 偏移量
        """
        try:
            # 获取图表的边界
            xlim = self.ax.get_xlim()
            ylim = self.ax.get_ylim()
            
            # 计算数据点在图表中的相对位置（0-1）
            x_rel = (x_pos - xlim[0]) / (xlim[1] - xlim[0])
            y_rel = (y_pos - ylim[0]) / (ylim[1] - ylim[0])
            
            # 基础偏移量
            base_offset = 30
            
            # 根据位置智能调整偏移方向
            if x_rel > 0.7:  # 靠近右边
                offset_x = -base_offset - 20  # 向左偏移更多
            elif x_rel < 0.3:  # 靠近左边
                offset_x = base_offset + 20   # 向右偏移更多
            else:  # 中间位置
                offset_x = base_offset
            
            if y_rel > 0.7:  # 靠近顶部
                offset_y = -base_offset - 10  # 向下偏移更多
            elif y_rel < 0.3:  # 靠近底部
                offset_y = base_offset + 10   # 向上偏移更多
            else:  # 中间位置
                offset_y = base_offset
            
            return offset_x, offset_y
            
        except Exception as e:
            self.logger.error(f"计算注释框偏移时出错: {e}")
            # 返回默认偏移
            return 30, 30
    
    def _format_timestamp_for_display(self, timestamp_str: str) -> str:
        """
        格式化时间戳用于显示
        
        Args:
            timestamp_str: 时间戳字符串
            
        Returns:
            str: 格式化后的时间字符串
        """
        try:
            # 尝试解析时间戳
            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%d']:
                try:
                    dt = datetime.strptime(timestamp_str, fmt)
                    # 根据时间精度返回不同格式
                    if '%H:%M:%S' in fmt:
                        return dt.strftime('%m-%d %H:%M:%S')
                    elif '%H:%M' in fmt:
                        return dt.strftime('%m-%d %H:%M')
                    else:
                        return dt.strftime('%m-%d')
                except ValueError:
                    continue
            
            # 如果解析失败，返回原始字符串的简化版本
            if len(timestamp_str) > 16:
                return timestamp_str[:16] + '...'
            return timestamp_str
            
        except Exception:
            return timestamp_str