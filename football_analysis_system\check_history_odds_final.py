#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查历史赔率表的最终结果
"""

import sqlite3

def check_history_odds_table():
    """检查历史赔率表"""
    try:
        conn = sqlite3.connect("data/matches_and_odds.db")
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='history_odds';")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ history_odds 表存在")
            
            # 获取表结构
            cursor.execute("PRAGMA table_info(history_odds);")
            columns = cursor.fetchall()
            
            print("\n=== 表结构 ===")
            for col_info in columns:
                cid, name, data_type, notnull, default_value, pk = col_info
                pk_str = " (主键)" if pk else ""
                notnull_str = " NOT NULL" if notnull else ""
                default_str = f" DEFAULT {default_value}" if default_value else ""
                print(f"  {name}: {data_type}{notnull_str}{default_str}{pk_str}")
            
            # 获取记录数
            cursor.execute("SELECT COUNT(*) FROM history_odds;")
            count = cursor.fetchone()[0]
            print(f"\n记录数: {count}")
            
            if count > 0:
                # 按比赛和公司分组显示
                cursor.execute("""
                    SELECT match_id, target_name, COUNT(*) as record_count
                    FROM history_odds 
                    GROUP BY match_id, target_name 
                    ORDER BY match_id, target_name
                """)
                match_stats = cursor.fetchall()
                
                print(f"\n=== 按比赛和公司分组统计 ===")
                for match_id, target_name, record_count in match_stats:
                    print(f"比赛 {match_id} - {target_name}: {record_count} 条历史记录")
                
                # 显示最新几条记录
                cursor.execute("""
                    SELECT match_id, target_name, company_name, home_odds, draw_odds, away_odds, 
                           payout_rate, update_time, scrape_time
                    FROM history_odds 
                    ORDER BY scrape_time DESC 
                    LIMIT 10
                """)
                
                recent_records = cursor.fetchall()
                print(f"\n=== 最新10条历史记录 ===")
                for record in recent_records:
                    match_id, target_name, company_name, home_odds, draw_odds, away_odds, payout_rate, update_time, scrape_time = record
                    payout_str = f" 返还率:{payout_rate:.2f}%" if payout_rate else ""
                    print(f"{scrape_time} - 比赛{match_id} {target_name}: {home_odds:.2f}/{draw_odds:.2f}/{away_odds:.2f}{payout_str} ({update_time})")
                
                # 显示数据示例
                cursor.execute("""
                    SELECT target_name, COUNT(*) as total_records, 
                           MIN(update_time) as earliest_time, 
                           MAX(update_time) as latest_time
                    FROM history_odds 
                    GROUP BY target_name
                    ORDER BY total_records DESC
                """)
                
                company_stats = cursor.fetchall()
                print(f"\n=== 各公司历史数据统计 ===")
                for target_name, total_records, earliest_time, latest_time in company_stats:
                    print(f"{target_name}: {total_records} 条记录 ({earliest_time} -> {latest_time})")
        else:
            print("❌ history_odds 表不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🔍 检查历史赔率表最终结果")
    print("=" * 60)
    check_history_odds_table()
    print("\n✅ 检查完成！") 