#!/usr/bin/env python3
"""
测试应用程序启动
验证修复后的路径配置是否能让应用正常启动
"""

import sys
import os
from pathlib import Path

def test_imports():
    """测试关键模块导入"""
    print("测试模块导入...")
    
    try:
        # 添加路径
        sys.path.insert(0, str(Path("football_analysis_system")))
        
        # 测试配置管理器
        from core.config_manager import config_manager
        print("✓ 配置管理器导入成功")
        print(f"  项目根目录: {config_manager.project_root}")
        
        # 测试日志管理器
        from core.logger_manager import logger_manager
        print("✓ 日志管理器导入成功")
        
        # 测试获取日志记录器
        logger = logger_manager.get_logger('test')
        logger.info("测试日志记录")
        print("✓ 日志记录器工作正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_paths():
    """测试配置路径"""
    print("\n测试配置路径...")
    
    try:
        sys.path.insert(0, str(Path("football_analysis_system")))
        from core.config_manager import config_manager
        
        # 测试各种路径
        paths_to_test = {
            'logs_dir': config_manager.get('paths.logs_dir'),
            'data_dir': config_manager.get('paths.data_dir'),
            'temp_dir': config_manager.get('paths.temp_dir'),
            'backup_dir': config_manager.get('paths.backup_dir')
        }
        
        for name, path in paths_to_test.items():
            resolved_path = config_manager.resolve_path(path)
            print(f"  {name}: {path} -> {resolved_path}")
            
            # 确保目录存在
            resolved_path.mkdir(parents=True, exist_ok=True)
            if resolved_path.exists():
                print(f"    ✓ 目录存在")
            else:
                print(f"    ✗ 目录不存在")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置路径测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_paths():
    """测试数据库路径"""
    print("\n测试数据库路径...")
    
    try:
        sys.path.insert(0, str(Path("football_analysis_system")))
        from core.config_manager import config_manager
        
        # 测试数据库路径
        db_names = ['matches', 'teams', 'odds_intervals']
        
        for db_name in db_names:
            db_path = config_manager.get_database_path(db_name)
            print(f"  {db_name}: {db_path}")
            
            # 检查数据库目录是否存在
            db_dir = Path(db_path).parent
            if db_dir.exists():
                print(f"    ✓ 数据库目录存在")
            else:
                print(f"    ✗ 数据库目录不存在")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库路径测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("测试应用程序启动和路径配置")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_config_paths,
        test_database_paths
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"测试执行失败: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    if all(results):
        print("🎉 所有测试通过！应用程序应该能正常启动。")
        print("\n✅ 路径问题已修复:")
        print("  - 配置文件中的硬编码路径已改为相对路径")
        print("  - ConfigManager 能正确解析相对路径为绝对路径")
        print("  - 所有必要的目录都能正确创建")
        print("  - 日志系统工作正常")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
