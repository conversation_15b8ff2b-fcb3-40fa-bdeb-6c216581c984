import sqlite3
import os
import sys

# 使用相对路径，当前文件在analysis目录下，上一级是项目根目录
DATA_DIR = os.path.join('..', 'data')
# 确保data目录存在
os.makedirs(DATA_DIR, exist_ok=True)
# 数据库文件路径
DB_FILE = os.path.join(DATA_DIR, 'football.db')

def print_separator():
    print("\n" + "="*80 + "\n")

def verify_database():
    """
    全面验证数据库内容
    """
    if not os.path.exists(DB_FILE):
        print(f"错误: 数据库文件 {DB_FILE} 不存在!")
        return False
    
    print(f"检查数据库: {os.path.abspath(DB_FILE)}")
    print(f"文件大小: {os.path.getsize(DB_FILE) / (1024*1024):.2f} MB")
    
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # 列出所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if not tables:
            print("错误: 数据库中没有表!")
            return False
        
        print(f"数据库中的表 ({len(tables)}):")
        for i, table in enumerate(tables, 1):
            print(f"  {i}. {table[0]}")
        
        print_separator()
        
        # 检查每个表的记录数
        print("各表记录数:")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  {table_name}: {count} 条记录")
        
        print_separator()
        
        # 检查特定表的联赛数量
        if 'matches' in [t[0] for t in tables]:
            print("matches表中各联赛的比赛数量:")
            cursor.execute("""
            SELECT league_name, COUNT(*) as match_count
            FROM matches
            GROUP BY league_name
            ORDER BY match_count DESC
            """)
            league_stats = cursor.fetchall()
            
            if not league_stats:
                print("  未找到任何联赛数据")
            else:
                for i, (league, count) in enumerate(league_stats, 1):
                    print(f"  {i}. {league}: {count}场比赛")
            
            # 专门检查澳超数据
            cursor.execute("SELECT COUNT(*) FROM matches WHERE league_name = '澳超'")
            aus_count = cursor.fetchone()[0]
            print(f"\n澳超联赛比赛数量: {aus_count}")
            
            if aus_count > 0:
                print("澳超联赛比赛示例:")
                cursor.execute("""
                SELECT match_id, round_number, match_time, home_team_name, home_score, away_score, away_team_name
                FROM matches 
                WHERE league_name = '澳超'
                LIMIT 5
                """)
                
                for row in cursor.fetchall():
                    match_id, round_num, match_time, home, h_score, a_score, away = row
                    print(f"  ID: {match_id}, 轮次: {round_num}, 时间: {match_time}")
                    print(f"  {home} {h_score}-{a_score} {away}\n")
            else:
                # 检查是否有使用其他名称的澳超数据
                cursor.execute("SELECT DISTINCT league_name FROM matches")
                all_leagues = [row[0] for row in cursor.fetchall()]
                print("查找可能的澳超相关联赛:")
                for league in all_leagues:
                    if '澳' in league or 'aus' in league.lower() or 'australia' in league.lower():
                        print(f"  可能相关: {league}")
                
                # 检查使用league_id
                cursor.execute("SELECT COUNT(*) FROM matches WHERE league_id = 273")
                aus_id_count = cursor.fetchone()[0]
                print(f"league_id = 273 的比赛数量: {aus_id_count}")
                
                if aus_id_count > 0:
                    print("league_id = 273 的比赛示例:")
                    cursor.execute("""
                    SELECT match_id, league_name, round_number, match_time, home_team_name, home_score, away_score, away_team_name
                    FROM matches 
                    WHERE league_id = 273
                    LIMIT 5
                    """)
                    
                    for row in cursor.fetchall():
                        match_id, league_name, round_num, match_time, home, h_score, a_score, away = row
                        print(f"  ID: {match_id}, 联赛: {league_name}, 轮次: {round_num}, 时间: {match_time}")
                        print(f"  {home} {h_score}-{a_score} {away}\n")
        
        print_separator()
        
        # 检查数据库完整性
        cursor.execute("PRAGMA integrity_check")
        integrity = cursor.fetchone()[0]
        print(f"数据库完整性检查: {integrity}")
        
        conn.close()
        return True
    
    except sqlite3.Error as e:
        print(f"数据库访问错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"验证过程出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 如果提供了命令行参数，使用它作为数据库路径
    if len(sys.argv) > 1:
        DB_FILE = sys.argv[1]
    
    print(f"开始验证数据库: {DB_FILE}")
    success = verify_database()
    
    if success:
        print("\n数据库验证完成")
    else:
        print("\n数据库验证失败")