import time
import logging
import requests
import pandas as pd
import os
import sqlite3
import threading
import re  # 添加re模块
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from tqdm import tqdm
from football_analysis_system.config import DATA_DIR, DB_GUANGYISHILI  # 导入配置的数据目录和广义实力数据库路径
from .crawler import CrawlerUtils, ErrorType
from .league_mapping import (
    LEAGUE_STANDARD_NAMES,
    LEAGUE_HIERARCHY,
    LEAGUE_KEYWORDS,
    LEAGUE_FUZZY_PATTERNS,
    LEAGUE_ALIASES,
    LEAGUE_REGIONS
)

class MatchScraper:
    """增强的比赛数据爬虫类"""

    def __init__(self, url_template, headers):
        """
        初始化比赛爬虫

        Args:
            url_template: 请求URL模板
            headers: 请求头信息
        """
        self.url_template = url_template
        self.headers = headers

        # 创建增强的HTTP会话
        self.session = CrawlerUtils.create_session(retry_times=3)

        # 缓存广义实力联赛列表
        self._guangyi_leagues = None
        # 添加线程锁，保证并发安全
        self._lock = threading.Lock()

        # 统计信息
        self._stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'parsed_matches': 0,
            'cache_hits': 0
        }

    def strip_html_tags(self, text):
        """
        清除文本中的HTML标签和场地标记如(中)

        Args:
            text: 包含HTML标签的文本

        Returns:
            str: 清除HTML标签和场地标记后的文本
        """
        if not text or not isinstance(text, str):
            return text
        # 先清除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 再清除(中)、(中立)等场地标记
        text = re.sub(r'\(中\)|\(中立\)', '', text)
        return text

    def get_timestamp_param(self):
        """
        生成增强的时间戳参数

        Returns:
            str: 格式化的时间戳参数
        """
        timestamp_part = str(int(time.time() * 1000))
        if len(timestamp_part) > 13:
            timestamp_part = timestamp_part[-13:]
        else:
            timestamp_part = timestamp_part.ljust(13, '0')
        return "007" + timestamp_part

    def fetch_data_with_retry(self, url, headers=None, max_retries=3, backoff_factor=1.2):
        """
        增强的带重试机制的数据获取函数

        Args:
            url: 请求的URL
            headers: 请求头信息，如果为None则使用self.headers
            max_retries: 最大重试次数
            backoff_factor: 退避因子，控制重试等待时间

        Returns:
            str: 响应内容，失败则返回None
        """
        if headers is None:
            headers = self.headers.copy()

        # 增强请求头，但保持兼容
        enhanced_headers = headers.copy()
        try:
            # 仅在CrawlerUtils可用时增强请求头
            from .crawler import CrawlerUtils
            crawler_headers = CrawlerUtils.get_headers()
            enhanced_headers.update(crawler_headers)
            enhanced_headers.update(headers)  # 确保原始headers覆盖
        except Exception as e:
            logging.warning(f"无法使用CrawlerUtils增强请求头: {e}")
            enhanced_headers = headers.copy()

        # 更新统计
        with self._lock:
            self._stats['total_requests'] += 1

        # 多次重试逻辑
        for attempt in range(max_retries):
            try:
                logging.info(f"尝试获取数据 (第 {attempt + 1}/{max_retries} 次): {url}")

                # 优化请求超时时间
                response = self.session.get(
                    url,
                    headers=enhanced_headers,
                    timeout=20,  # 从25秒减少到20秒
                    allow_redirects=True
                )

                if response.status_code == 200:
                    content = response.text
                    if content and len(content) > 100:  # 基本的内容验证
                        with self._lock:
                            self._stats['successful_requests'] += 1
                        logging.info(f"成功获取数据，内容长度: {len(content)}")
                        return content
                    else:
                        logging.warning(f"响应内容过短或为空: {len(content) if content else 0}")
                else:
                    logging.warning(f"HTTP响应状态码: {response.status_code}")

            except requests.exceptions.Timeout as e:
                logging.warning(f"第 {attempt + 1} 次请求超时: {e}")
            except requests.exceptions.ConnectionError as e:
                logging.warning(f"第 {attempt + 1} 次连接错误: {e}")
            except requests.exceptions.RequestException as e:
                logging.warning(f"第 {attempt + 1} 次请求异常: {e}")
            except Exception as e:
                logging.error(f"第 {attempt + 1} 次未知错误: {e}")

            # 如果不是最后一次尝试，等待一段时间
            if attempt < max_retries - 1:
                wait_time = backoff_factor ** attempt * 0.5  # 减少基础等待时间
                logging.info(f"等待 {wait_time:.1f} 秒后重试...")
                time.sleep(wait_time)

        # 所有尝试都失败了
        with self._lock:
            self._stats['failed_requests'] += 1

        logging.error(f"在 {max_retries} 次尝试后仍无法获取数据: {url}")
        return None

    def fetch_data(self):
        """
        获取比赛数据

        Returns:
            str: 响应内容
        """
        timestamp = self.get_timestamp_param()
        url = self.url_template.format(timestamp=timestamp)

        return self.fetch_data_with_retry(url)

    def get_stats(self):
        """获取爬虫统计信息"""
        with self._lock:
            stats = dict(self._stats)

        # 合并CrawlerUtils的统计信息
        crawler_stats = CrawlerUtils.get_stats()
        stats.update(crawler_stats)

        # 计算成功率
        if stats['total_requests'] > 0:
            stats['success_rate'] = stats['successful_requests'] / stats['total_requests']
        else:
            stats['success_rate'] = 0

        return stats

    def reset_stats(self):
        """重置统计信息"""
        with self._lock:
            self._stats = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'parsed_matches': 0,
                'cache_hits': 0
            }
        CrawlerUtils.reset_stats()
        logging.info("比赛爬虫统计信息已重置")

    def parse_javascript_data(self, js_content):
        """
        解析JavaScript内容，提取比赛数据

        Args:
            js_content: JavaScript内容

        Returns:
            list: 解析后的比赛数据列表
        """
        if not js_content:
            logging.warning("没有JavaScript内容可解析")
            return None

        all_match_data = []
        lines = js_content.splitlines()
        logging.info(f"处理JS文件的 {len(lines)} 行内容")

        extracted_count = 0
        found_potential_line = False

        for line in lines:
            if "A[" in line and ".split('^');" in line:
                found_potential_line = True

                try:
                    first_quote_index = line.find('"')
                    if first_quote_index == -1:
                        continue

                    second_quote_index = line.find('"', first_quote_index + 1)
                    if second_quote_index == -1:
                        continue

                    split_marker_index = line.find(".split('^');", second_quote_index)
                    if split_marker_index == -1:
                        continue

                    match_str = line[first_quote_index + 1 : second_quote_index]
                    fields = match_str.split('^')
                    all_match_data.append(fields)
                    extracted_count += 1
                except Exception as e:
                    logging.error(f"处理潜在比赛数据行出错: {e}")
                    continue

        if not found_potential_line:
            logging.warning("未找到包含'A['和'.split('^');'的行")

        if not all_match_data:
            logging.warning("未能解析出任何比赛数据行")
            return None

        logging.info(f"成功解析了 {extracted_count} 条比赛记录")
        return all_match_data

    def standardize_league_name(self, league_name):
        """
        将联赛名称标准化为广义实力数据库中使用的格式

        Args:
            league_name: 原始联赛名称

        Returns:
            str: 标准化后的联赛名称
        """
        if not league_name:
            return league_name

        # 转换为小写以进行不区分大小写的比较
        league_name_lower = league_name.lower()

        # 预处理：去除特殊情况
        # 明确排除青年队比赛，避免将"英乙U21"匹配为"英乙"等
        if "u21" in league_name_lower or "u23" in league_name_lower or "青年" in league_name:
            return league_name  # 保持原样，不进行映射

        # 先移除升级/降级/附加赛等关键词，再进行标准化
        # 例如 "德乙升" -> "德乙"
        cleaned_league_name = league_name
        cleaned_league_name_lower = league_name_lower
        promotion_relegation_keywords = ["升", "降", "playoff", "play-off", "附加赛"]
        for keyword in promotion_relegation_keywords:
            if keyword in cleaned_league_name_lower:
                # 使用 re.sub 来移除关键词，忽略大小写，并且只移除独立的词（如果适用）
                # 但简单移除子串对于中文可能更直接有效
                cleaned_league_name = re.sub(re.escape(keyword), '', cleaned_league_name, flags=re.IGNORECASE).strip()
                cleaned_league_name_lower = cleaned_league_name.lower() # 更新小写版本

        # 如果清理后的名称与原名称不同，记录一下
        if league_name != cleaned_league_name:
            logging.info(f"移除附加赛关键词: '{league_name}' -> '{cleaned_league_name}'")
            league_name = cleaned_league_name # 使用清理后的名称进行后续匹配
            league_name_lower = cleaned_league_name_lower

        # 排除杯赛，避免与联赛混淆
        if "杯" in league_name or "cup" in league_name_lower:
            return league_name  # 保持原样，不进行映射

        # 简称到标准名称的直接映射，解决常见简称匹配问题
        common_short_names = {
            "土超": "土耳其超",
            "葡超": "葡超",  # 保持一致
            "葡甲": "葡甲",  # 保持一致
            "荷甲": "荷甲",  # 保持一致
            "荷乙": "荷乙",  # 保持一致
            "阿甲": "阿根廷甲",
            "阿乙": "阿根廷乙",
            "西甲": "西甲",  # 保持一致
            "西乙": "西乙",  # 保持一致
            "英超": "英超",  # 保持一致
            "英冠": "英冠",  # 保持一致
            "英甲": "英甲",  # 保持一致
            "英乙": "英乙",  # 保持一致
            "德甲": "德甲",  # 保持一致
            "德乙": "德乙",  # 保持一致
            "法甲": "法甲",  # 保持一致
            "法乙": "法乙",  # 保持一致
            "意甲": "意甲",  # 保持一致
            "意乙": "意乙",  # 保持一致
            "苏超": "苏超",  # 保持一致
            "苏冠": "苏冠",  # 保持一致
            "苏甲": "苏甲",  # 保持一致
            "苏乙": "苏乙",  # 保持一致
            "瑞典甲": "瑞典甲", # 使用标准化的名称
            "瑞甲": "瑞典甲", # 将简称映射到标准化的名称
            "日职联": "日职联", # 直接匹配
            "日职乙": "日职乙", # 直接匹配
            "韩K": "韩K",  # 使用标准化的大写K形式
            "韩k": "韩K",  # 将小写k映射到标准化的大写K形式
            "韩K联": "韩K",  # 添加韩K联的映射
            "韩k联": "韩K",  # 添加韩k联的映射
            "韩K2": "韩K2",  # 保持一致
            "韩k2": "韩K2",  # 添加小写k2的映射
            "韩K2联": "韩K2",  # 添加韩K2联的映射
            "韩k2联": "韩K2",  # 添加韩k2联的映射
            "爱超": "爱尔兰超", # 添加爱尔兰超的简称映射
            "爱甲": "爱尔兰甲",  # 添加爱尔兰甲的简称映射
            "墨超": "墨西哥甲", # 添加墨西哥甲的简称映射
            "墨西哥超": "墨西哥甲", # 添加墨西哥超的映射
            "沙特联": "沙特联", # 沙特联赛
            "沙特超": "沙特联", # 沙特联赛别名
            "南非超": "南非超", # 南非超级联赛
            "南非甲": "南非甲", # 南非甲级联赛
            "摩洛超": "摩洛超", # 摩洛哥超级联赛
            "摩洛哥超": "摩洛超", # 摩洛哥超级联赛别名
            "泰超": "泰超", # 泰国超级联赛
            "泰甲": "泰甲", # 泰国甲级联赛
            "巴林超": "巴林超", # 巴林超级联赛
            "巴林甲": "巴林甲", # 巴林甲级联赛
            "巴拉甲": "巴拉甲", # 巴拉圭甲级联赛
            "巴拉乙": "巴拉乙", # 巴拉圭乙级联赛
            "巴拉圭甲": "巴拉甲", # 巴拉圭甲级联赛别名
            "巴拉圭乙": "巴拉乙", # 巴拉圭乙级联赛别名
            "秘鲁甲": "秘鲁甲", # 秘鲁甲级联赛
            "冰岛超": "冰岛超", # 冰岛超级联赛
            "冰岛甲": "冰岛甲", # 冰岛甲级联赛
            "拉脱超": "拉脱超", # 拉脱维亚超级联赛
            "拉脱甲": "拉脱甲", # 拉脱维亚甲级联赛
            "拉脱维亚超": "拉脱超", # 拉脱维亚超级联赛别名
            "拉脱维亚甲": "拉脱甲", # 拉脱维亚甲级联赛别名
            "立陶甲": "立陶甲", # 立陶宛甲级联赛
            "立陶乙": "立陶乙", # 立陶宛乙级联赛
            "立陶宛甲": "立陶甲", # 立陶宛甲级联赛别名
            "立陶宛乙": "立陶乙", # 立陶宛乙级联赛别名
            "土甲": "土甲", # 土耳其甲级联赛
            "土耳其甲": "土甲", # 土耳其甲级联赛别名
            "匈甲": "匈甲", # 匈牙利甲级联赛
            "匈牙利甲": "匈甲", # 匈牙利甲级联赛别名
            "克亚甲": "克亚甲", # 克罗地亚甲级联赛
            "克罗地亚甲": "克亚甲", # 克罗地亚甲级联赛别名
            "斯亚甲": "斯亚甲", # 斯洛文尼亚甲级联赛
            "斯洛文尼亚甲": "斯亚甲", # 斯洛文尼亚甲级联赛别名
            "哈萨克超": "哈萨克超", # 哈萨克斯坦超级联赛
            "哈萨克甲": "哈萨克甲", # 哈萨克斯坦甲级联赛
            "哈萨克斯坦超": "哈萨克超", # 哈萨克斯坦超级联赛别名
            "哈萨克斯坦甲": "哈萨克甲", # 哈萨克斯坦甲级联赛别名
            "波斯甲": "波斯甲", # 波斯尼亚甲级联赛
            "波黑甲": "波斯甲", # 波斯尼亚甲级联赛别名
            "波斯尼亚甲": "波斯甲", # 波斯尼亚甲级联赛别名
            "爱沙甲": "爱沙甲", # 爱沙尼亚甲级联赛
            "爱沙尼亚甲": "爱沙甲", # 爱沙尼亚甲级联赛别名
            "格鲁甲": "格鲁甲", # 格鲁吉亚甲级联赛
            "格鲁吉亚甲": "格鲁甲", # 格鲁吉亚甲级联赛别名
            # 新增联赛映射
            "澳超": "澳超", # 澳大利亚超级联赛
            "澳大利亚超": "澳超", # 澳大利亚超级联赛别名
            "瑞典超": "瑞典超", # 瑞典超级联赛
            "瑞超": "瑞典超", # 将简称映射到标准化的名称
            "挪超": "挪超", # 挪威超级联赛
            "挪甲": "挪甲", # 挪威甲级联赛
            "瑞士超": "瑞士超", # 瑞士超级联赛
            "瑞士甲": "瑞士甲", # 瑞士甲级联赛
            "俄超": "俄超", # 俄罗斯超级联赛
            "俄甲": "俄甲", # 俄罗斯甲级联赛
            "俄罗斯超": "俄超", # 俄罗斯超级联赛别名
            "俄罗斯甲": "俄甲", # 俄罗斯甲级联赛别名
            "巴西甲": "巴西甲", # 巴西甲级联赛
            "巴西乙": "巴西乙", # 巴西乙级联赛
            "中超": "中超", # 中国超级联赛
            "智利甲": "智利甲", # 智利甲级联赛
            "智利乙": "智利乙", # 智利乙级联赛
            "阿联酋超": "阿联酋超", # 阿联酋超级联赛
            "阿联酋甲": "阿联酋甲", # 阿联酋甲级联赛
            "乌兹超": "乌兹超", # 乌兹别克斯坦超级联赛
            "乌兹甲": "乌兹甲", # 乌兹别克斯坦甲级联赛
            "乌兹别克斯坦超": "乌兹超", # 乌兹别克斯坦超级联赛别名
            "乌兹别克斯坦甲": "乌兹甲", # 乌兹别克斯坦甲级联赛别名
            "白俄超": "白俄超", # 白俄罗斯超级联赛
            "白俄甲": "白俄甲", # 白俄罗斯甲级联赛
            "白俄罗斯超": "白俄超", # 白俄罗斯超级联赛别名
            "白俄罗斯甲": "白俄甲",  # 白俄罗斯甲级联赛别名
            # 新增澳大利亚和奥地利联赛映射
            "澳南超": "澳南超", # 澳洲南部超级联赛
            "澳洲南部超级联赛": "澳南超", # 澳洲南部超级联赛全称
            "南澳超级联赛": "澳南超", # 南澳超级联赛别名
            "澳维甲": "澳维甲", # 澳洲维多利亚甲级联赛
            "澳洲维多利亚甲级联赛": "澳维甲", # 澳洲维多利亚甲级联赛全称
            "维多利亚甲级联赛": "澳维甲", # 维多利亚甲级联赛别名
            "澳维超": "澳维超", # 澳洲维多利亚超级联赛
            "澳洲维多利亚超级联赛": "澳维超", # 澳洲维多利亚超级联赛全称
            "维多利亚超级联赛": "澳维超", # 维多利亚超级联赛别名
            "奥威超": "奥威超", # 奥地利威尔士超级联赛
            "奥地利威尔士超级联赛": "奥威超", # 奥地利威尔士超级联赛全称
            "奥地利威尔士联赛": "奥威超" # 奥地利威尔士联赛别名
        }

        # 先尝试常见简称匹配
        if league_name in common_short_names:
            standard_name = common_short_names[league_name]
            logging.info(f"简称直接匹配: '{league_name}' -> '{standard_name}'")
            return standard_name

        # 1. 直接匹配标准名称映射
        for standard_name, variants in LEAGUE_STANDARD_NAMES.items():
            if league_name in variants:
                logging.info(f"标准名称直接匹配: '{league_name}' -> '{standard_name}'")
                return standard_name
            # 检查精确小写形式匹配
            if league_name_lower in [variant.lower() for variant in variants]:
                logging.info(f"标准名称小写匹配: '{league_name}' -> '{standard_name}'")
                return standard_name

        # 2. 检查联赛别名（精确匹配）
        for standard_name, aliases in LEAGUE_ALIASES.items():
            for lang_aliases in aliases.values():
                if any(alias.lower() == league_name_lower for alias in lang_aliases):
                    logging.info(f"别名匹配: '{league_name}' -> '{standard_name}'")
                    return standard_name

        # 3. 模糊匹配（使用严格的正则表达式匹配）
        for standard_name, pattern in LEAGUE_FUZZY_PATTERNS.items():
            if re.search(pattern, league_name, re.IGNORECASE):
                # 记录模糊匹配成功的情况
                logging.info(f"使用模糊匹配成功: '{league_name}' -> '{standard_name}'")
                return standard_name

        # 4. 层级关系匹配（仅在有明确国家标识的情况下）
        for country, hierarchy in LEAGUE_HIERARCHY.items():
            # 检查联赛名称中是否包含国家名称
            if country in league_name:
                # 检查是否包含级别关键字，且只有在明确的情况下才匹配
                if ("超级" in league_name or "super" in league_name_lower) and "level_1" in hierarchy:
                    logging.info(f"层级匹配: '{league_name}' -> '{hierarchy['level_1']}'")
                    return hierarchy["level_1"]
                elif ("甲级" in league_name or "first" in league_name_lower) and "level_2" in hierarchy:
                    logging.info(f"层级匹配: '{league_name}' -> '{hierarchy['level_2']}'")
                    return hierarchy["level_2"]

        # 5. 尝试一些特殊情况处理
        # 检查是否包含特定关键字
        if "日本" in league_name and ("j1" in league_name_lower or "职业" in league_name):
            logging.info(f"特殊关键字匹配: '{league_name}' -> '日职联'")
            return "日职联"
        elif "日本" in league_name and "j2" in league_name_lower:
            logging.info(f"特殊关键字匹配: '{league_name}' -> '日职乙'")
            return "日职乙"

        # 记录未匹配的联赛名称
        logging.warning(f"未能匹配的联赛名称: {league_name}")

        # 返回原始名称
        return league_name

    def is_matching_league(self, league_name, target_leagues):
        """
        检查联赛名称是否匹配目标联赛列表中的任何一个

        Args:
            league_name: 要检查的联赛名称
            target_leagues: 目标联赛名称列表

        Returns:
            bool: 是否匹配
        """
        if not league_name:
            return False

        league_name_lower = league_name.lower()

        # 简称匹配
        common_short_names = {
            "土超": "土耳其超",
            "葡超": "葡超",
            "葡甲": "葡甲",
            "荷甲": "荷甲",
            "荷乙": "荷乙",
            "阿甲": "阿根廷甲",
            "阿乙": "阿根廷乙",
            "瑞典甲": "瑞典甲", # 使用标准化的名称
            "瑞甲": "瑞典甲", # 将简称映射到标准化的名称
            "日职联": "日职联",
            "日职乙": "日职乙",
            "韩K": "韩K",  # 使用标准化的大写K形式
            "韩k": "韩K",  # 将小写k映射到标准化的大写K形式
            "韩K联": "韩K",  # 添加韩K联的映射
            "韩k联": "韩K",  # 添加韩k联的映射
            "韩K2": "韩K2",  # 保持一致
            "韩k2": "韩K2",  # 添加小写k2的映射
            "韩K2联": "韩K2",  # 添加韩K2联的映射
            "韩k2联": "韩K2",  # 添加韩k2联的映射
            "爱超": "爱尔兰超", # 添加爱尔兰超的简称映射
            "爱甲": "爱尔兰甲",  # 添加爱尔兰甲的简称映射
            "墨超": "墨西哥甲", # 添加墨西哥甲的简称映射
            "墨西哥超": "墨西哥甲", # 添加墨西哥超的映射
            "沙特联": "沙特联", # 沙特联赛
            "沙特超": "沙特联", # 沙特联赛别名
            "南非超": "南非超", # 南非超级联赛
            "南非甲": "南非甲", # 南非甲级联赛
            "摩洛超": "摩洛超", # 摩洛哥超级联赛
            "摩洛哥超": "摩洛超", # 摩洛哥超级联赛别名
            "泰超": "泰超", # 泰国超级联赛
            "泰甲": "泰甲", # 泰国甲级联赛
            "巴林超": "巴林超", # 巴林超级联赛
            "巴林甲": "巴林甲", # 巴林甲级联赛
            "巴拉甲": "巴拉甲", # 巴拉圭甲级联赛
            "巴拉乙": "巴拉乙", # 巴拉圭乙级联赛
            "巴拉圭甲": "巴拉甲", # 巴拉圭甲级联赛别名
            "巴拉圭乙": "巴拉乙", # 巴拉圭乙级联赛别名
            "秘鲁甲": "秘鲁甲", # 秘鲁甲级联赛
            "冰岛超": "冰岛超", # 冰岛超级联赛
            "冰岛甲": "冰岛甲", # 冰岛甲级联赛
            "拉脱超": "拉脱超", # 拉脱维亚超级联赛
            "拉脱甲": "拉脱甲", # 拉脱维亚甲级联赛
            "拉脱维亚超": "拉脱超", # 拉脱维亚超级联赛别名
            "拉脱维亚甲": "拉脱甲", # 拉脱维亚甲级联赛别名
            "立陶甲": "立陶甲", # 立陶宛甲级联赛
            "立陶乙": "立陶乙", # 立陶宛乙级联赛
            "立陶宛甲": "立陶甲", # 立陶宛甲级联赛别名
            "立陶宛乙": "立陶乙", # 立陶宛乙级联赛别名
            "土甲": "土甲", # 土耳其甲级联赛
            "土耳其甲": "土甲", # 土耳其甲级联赛别名
            "匈甲": "匈甲", # 匈牙利甲级联赛
            "匈牙利甲": "匈甲", # 匈牙利甲级联赛别名
            "克亚甲": "克亚甲", # 克罗地亚甲级联赛
            "克罗地亚甲": "克亚甲", # 克罗地亚甲级联赛别名
            "斯亚甲": "斯亚甲", # 斯洛文尼亚甲级联赛
            "斯洛文尼亚甲": "斯亚甲", # 斯洛文尼亚甲级联赛别名
            "哈萨克超": "哈萨克超", # 哈萨克斯坦超级联赛
            "哈萨克甲": "哈萨克甲", # 哈萨克斯坦甲级联赛
            "哈萨克斯坦超": "哈萨克超", # 哈萨克斯坦超级联赛别名
            "哈萨克斯坦甲": "哈萨克甲", # 哈萨克斯坦甲级联赛别名
            "波斯甲": "波斯甲", # 波斯尼亚甲级联赛
            "波黑甲": "波斯甲", # 波斯尼亚甲级联赛别名
            "波斯尼亚甲": "波斯甲", # 波斯尼亚甲级联赛别名
            "爱沙甲": "爱沙甲", # 爱沙尼亚甲级联赛
            "爱沙尼亚甲": "爱沙甲", # 爱沙尼亚甲级联赛别名
            "格鲁甲": "格鲁甲", # 格鲁吉亚甲级联赛
            "格鲁吉亚甲": "格鲁甲", # 格鲁吉亚甲级联赛别名
            # 新增联赛映射
            "澳超": "澳超", # 澳大利亚超级联赛
            "澳大利亚超": "澳超", # 澳大利亚超级联赛别名
            "瑞典超": "瑞典超", # 瑞典超级联赛
            "瑞超": "瑞典超", # 将简称映射到标准化的名称
            "挪超": "挪超", # 挪威超级联赛
            "挪甲": "挪甲", # 挪威甲级联赛
            "瑞士超": "瑞士超", # 瑞士超级联赛
            "瑞士甲": "瑞士甲", # 瑞士甲级联赛
            "俄超": "俄超", # 俄罗斯超级联赛
            "俄甲": "俄甲", # 俄罗斯甲级联赛
            "俄罗斯超": "俄超", # 俄罗斯超级联赛别名
            "俄罗斯甲": "俄甲", # 俄罗斯甲级联赛别名
            "巴西甲": "巴西甲", # 巴西甲级联赛
            "巴西乙": "巴西乙", # 巴西乙级联赛
            "中超": "中超", # 中国超级联赛
            "智利甲": "智利甲", # 智利甲级联赛
            "智利乙": "智利乙", # 智利乙级联赛
            "阿联酋超": "阿联酋超", # 阿联酋超级联赛
            "阿联酋甲": "阿联酋甲", # 阿联酋甲级联赛
            "乌兹超": "乌兹超", # 乌兹别克斯坦超级联赛
            "乌兹甲": "乌兹甲", # 乌兹别克斯坦甲级联赛
            "乌兹别克斯坦超": "乌兹超", # 乌兹别克斯坦超级联赛别名
            "乌兹别克斯坦甲": "乌兹甲", # 乌兹别克斯坦甲级联赛别名
            "白俄超": "白俄超", # 白俄罗斯超级联赛
            "白俄甲": "白俄甲", # 白俄罗斯甲级联赛
            "白俄罗斯超": "白俄超", # 白俄罗斯超级联赛别名
            "白俄罗斯甲": "白俄甲",  # 白俄罗斯甲级联赛别名
            # 新增澳大利亚和奥地利联赛映射
            "澳南超": "澳南超", # 澳洲南部超级联赛
            "澳洲南部超级联赛": "澳南超", # 澳洲南部超级联赛全称
            "南澳超级联赛": "澳南超", # 南澳超级联赛别名
            "澳维甲": "澳维甲", # 澳洲维多利亚甲级联赛
            "澳洲维多利亚甲级联赛": "澳维甲", # 澳洲维多利亚甲级联赛全称
            "维多利亚甲级联赛": "澳维甲", # 维多利亚甲级联赛别名
            "澳维超": "澳维超", # 澳洲维多利亚超级联赛
            "澳洲维多利亚超级联赛": "澳维超", # 澳洲维多利亚超级联赛全称
            "维多利亚超级联赛": "澳维超", # 维多利亚超级联赛别名
            "奥威超": "奥威超", # 奥地利威尔士超级联赛
            "奥地利威尔士超级联赛": "奥威超", # 奥地利威尔士超级联赛全称
            "奥地利威尔士联赛": "奥威超" # 奥地利威尔士联赛别名
        }

        # 检查是否有简称映射
        if league_name in common_short_names:
            standard_name = common_short_names[league_name]
            if standard_name in target_leagues:
                logging.info(f"匹配成功(简称): {league_name} -> {standard_name}")
                return True

        # 1. 直接匹配
        if league_name in target_leagues:
            return True

        # 检查小写匹配
        for target in target_leagues:
            if target.lower() == league_name_lower:
                logging.info(f"匹配成功(小写): {league_name} -> {target}")
                return True

        # 2. 标准化后匹配
        standardized_name = self.standardize_league_name(league_name)
        if standardized_name in target_leagues:
            logging.info(f"匹配成功(标准化): {league_name} -> {standardized_name}")
            return True

        # 3. 检查所有可能的变体
        for standard_name in target_leagues:
            # 检查标准名称映射
            if standard_name in LEAGUE_STANDARD_NAMES:
                variants = LEAGUE_STANDARD_NAMES[standard_name]
                if any(variant.lower() == league_name_lower for variant in variants):
                    logging.info(f"匹配成功(变体): {league_name} -> {standard_name}")
                    return True

            # 检查别名
            if standard_name in LEAGUE_ALIASES:
                aliases = LEAGUE_ALIASES[standard_name]
                for lang_aliases in aliases.values():
                    if any(alias.lower() == league_name_lower for alias in lang_aliases):
                        logging.info(f"匹配成功(别名): {league_name} -> {standard_name}")
                        return True

        return False

    def get_guangyi_leagues(self):
        """
        从广义实力数据库中获取有数据的联赛列表

        Returns:
            list: 有广义实力数据的联赛名称列表
        """
        # 使用线程锁确保并发安全
        with self._lock:
            # 使用缓存，避免重复查询数据库
            if self._guangyi_leagues is not None:
                return self._guangyi_leagues

        try:
            if not os.path.exists(DB_GUANGYISHILI):
                logging.warning(f"广义实力数据库不存在: {DB_GUANGYISHILI}")
                with self._lock:
                    self._guangyi_leagues = []
                return []

            conn = sqlite3.connect(DB_GUANGYISHILI)
            cursor = conn.cursor()

            # 查询有数据的联赛名称 - 使用DISTINCT确保不重复
            cursor.execute("""
            SELECT DISTINCT league_name
            FROM team_power_ratings
            WHERE league_name IS NOT NULL AND league_name != ''
            """)

            leagues = [row[0] for row in cursor.fetchall() if row[0]]

            # 如果team_power_ratings表不存在，可能需要尝试其他表名
            if not leagues:
                # 获取所有表名
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]

                if 'team_power_ratings' not in tables and len(tables) > 0:
                    # 尝试第一个表
                    first_table = tables[0]
                    logging.info(f"team_power_ratings表不存在，尝试从{first_table}表获取联赛数据")

                    # 查看表结构
                    cursor.execute(f"PRAGMA table_info({first_table})")
                    columns = [col[1] for col in cursor.fetchall()]

                    # 寻找可能包含联赛名称的列
                    league_columns = [col for col in columns if 'league' in col.lower()]

                    if league_columns:
                        league_col = league_columns[0]
                        cursor.execute(f"""
                        SELECT DISTINCT {league_col}
                        FROM {first_table}
                        WHERE {league_col} IS NOT NULL AND {league_col} != ''
                        """)
                        leagues = [row[0] for row in cursor.fetchall() if row[0]]

            conn.close()

            if leagues:
                logging.info(f"从广义实力数据库获取到 {len(leagues)} 个联赛: {', '.join(leagues)}")
            else:
                logging.warning("未从广义实力数据库获取到任何联赛数据")

            # 缓存结果，使用线程锁确保线程安全
            with self._lock:
                self._guangyi_leagues = leagues
            return leagues

        except Exception as e:
            logging.error(f"获取广义实力联赛列表时出错: {e}")
            import traceback
            traceback.print_exc()
            with self._lock:
                self._guangyi_leagues = []
            return []

    def process_data(self, match_data_list):
        """
        处理提取的比赛数据并过滤

        Args:
            match_data_list: 比赛数据列表

        Returns:
            DataFrame: 处理后的比赛数据
        """
        if not match_data_list:
            logging.warning("没有比赛数据可处理")
            return None

        try:
            # 确保所有行的列数一致
            max_cols = 0
            if match_data_list and isinstance(match_data_list, list):
                valid_rows = [row for row in match_data_list if isinstance(row, list)]
                if valid_rows:
                    max_cols = max(len(row) for row in valid_rows)
                else:
                    logging.warning("match_data_list不包含有效行")
                    return None
            else:
                logging.warning("match_data_list为空或不是列表")
                return None

            # 初始列名
            columns = [
                'MatchID', 'LeagueColor', 'LeagueNameSimp', 'LeagueNameTrad', 'Unknown1',
                'HomeTeamSimp', 'HomeTeamTrad', 'Unknown2', 'AwayTeamSimp', 'AwayTeamTrad',
                'Unknown3', 'StartTimeShort', 'StartDateTimeFull', 'MatchStatusCode', 'HomeScore',
                'AwayScore', 'HomeHalfScore', 'AwayHalfScore', 'HomeRedCards', 'AwayRedCards',
                'HomeYellowCards', 'AwayYellowCards', 'HomeRank', 'AwayRank', 'HasLineups',
                'IsLive', 'HandicapLine', 'DetailsURLPart', 'SubLeagueURLPart', 'Unknown4',
                'Unknown5', 'DateMMDD', 'HomeTeamID', 'AwayTeamID', 'Unknown6', 'CountryID',
                'IsNeutral', 'SeasonYear', 'Unknown7', 'LeagueID', 'GoalHandicapLine',
                'Unknown8', 'HomeCorners', 'AwayCorners', 'HasAnimation', 'HasAnalysis', 'LeagueShortNameID',
                'Unknown9', 'OddsProviderCount', 'Unknown10', 'Unknown11', 'Unknown12', 'TVInfo',
                'IsVerified', 'Unknown13', 'Unknown14'
            ]

            # 调整列数
            padded_data = []
            for row in match_data_list:
                if isinstance(row, list):
                    str_row = [str(item) for item in row]
                    padded_row = str_row + [''] * (max_cols - len(row))
                    padded_data.append(padded_row)

            # 确保列名与数据匹配
            if max_cols > len(columns):
                columns.extend([f'Unknown_{i+len(columns)}' for i in range(max_cols - len(columns))])
            elif max_cols < len(columns):
                columns = columns[:max_cols]

            # 创建DataFrame
            df = pd.DataFrame(padded_data, columns=columns)
            logging.info(f"成功创建DataFrame，包含 {len(df)} 场比赛和 {len(df.columns)} 列")

            # 清除球队名称中的HTML标签
            team_columns = ['HomeTeamSimp', 'HomeTeamTrad', 'AwayTeamSimp', 'AwayTeamTrad']
            for col in team_columns:
                if col in df.columns:
                    df[col] = df[col].apply(self.strip_html_tags)
                    logging.info(f"已清除 {col} 列中的HTML标签")

            # 调整过滤顺序，先进行联赛过滤
            # 第1步：按广义实力联赛过滤
            league_name_col = 'LeagueNameSimp'
            status_code_col_index = 13  # MatchStatusCode的索引

            if league_name_col not in df.columns:
                logging.warning(f"数据中不存在联赛名称列 '{league_name_col}'，无法进行联赛过滤")
                return None

            original_count = len(df)
            logging.info(f"第1步：过滤只保留有广义实力数据的联赛比赛")

            # 添加标准化联赛名称列
            df['StandardizedLeague'] = df[league_name_col].apply(self.standardize_league_name)

            # 获取广义实力联赛列表
            guangyi_leagues = self.get_guangyi_leagues()
            if not guangyi_leagues:
                logging.warning("未获取到广义实力联赛列表，跳过联赛过滤")
                return None

            # 打印联赛名称映射情况，帮助调试
            for _, row in df.iterrows():
                original = row[league_name_col]
                standardized = row['StandardizedLeague']
                if original != standardized and standardized != original: # 只有在标准化名称确实改变时才记录
                    logging.info(f"联赛名称映射: '{original}' -> '{standardized}'")

                # 特别记录韩K联和韩K2联赛的匹配情况
                if "韩" in original or "韩" in standardized:
                    logging.info(f"韩国联赛匹配: 原始='{original}', 标准化='{standardized}'")

            # 记录广义实力数据库中的韩国联赛
            korean_leagues = [league for league in guangyi_leagues if "韩" in league]
            if korean_leagues:
                logging.info(f"广义实力数据库中的韩国联赛: {', '.join(korean_leagues)}")
            else:
                logging.warning("广义实力数据库中未找到韩国联赛")

            # 使用标准化的联赛名称直接进行过滤
            # 比较时统一转为小写，确保不区分大小写
            guangyi_leagues_lower = set(league.lower() for league in guangyi_leagues)

            # 记录转换为小写后的联赛列表（仅韩国联赛）
            korean_leagues_lower = [league.lower() for league in guangyi_leagues if "韩" in league]
            if korean_leagues_lower:
                logging.info(f"转换为小写后的韩国联赛: {', '.join(korean_leagues_lower)}")

            df_league_filtered = df[df['StandardizedLeague'].str.lower().isin(guangyi_leagues_lower)].copy()

            league_filter_count = len(df_league_filtered)
            logging.info(f"第1步结果：从 {original_count} 场比赛中保留 {league_filter_count} 场有广义实力数据的联赛比赛")

            # 如果筛选后没有比赛，记录所有联赛名称帮助调试
            if df_league_filtered.empty:
                unique_leagues = df[league_name_col].unique()
                standardized_leagues = [self.standardize_league_name(league) for league in unique_leagues]
                logging.warning(f"未找到符合有广义实力数据联赛的比赛。数据中的联赛: {', '.join(unique_leagues)}")
                logging.warning(f"标准化后的联赛: {', '.join(standardized_leagues)}")
                logging.warning(f"广义实力数据库中的联赛: {', '.join(guangyi_leagues)}")

                # 特别检查韩国联赛的匹配情况
                korean_leagues_in_data = [league for league in unique_leagues if "韩" in league]
                if korean_leagues_in_data:
                    logging.warning(f"数据中的韩国联赛: {', '.join(korean_leagues_in_data)}")
                    for league in korean_leagues_in_data:
                        std_league = self.standardize_league_name(league)
                        std_league_lower = std_league.lower()
                        logging.warning(f"韩国联赛匹配检查: '{league}' -> '{std_league}' (小写: '{std_league_lower}')")
                        logging.warning(f"是否在广义实力联赛列表中: {std_league in guangyi_leagues}")
                        logging.warning(f"是否在小写广义实力联赛列表中: {std_league_lower in guangyi_leagues_lower}")

                return None

            # 第2步：不再筛选北单比赛，直接使用第一步结果
            logging.info(f"跳过北单筛选步骤，保留所有第一步筛选出的 {league_filter_count} 场比赛")

            # 第3步：只保留未开始的比赛
            if status_code_col_index < len(df.columns):
                status_code_col_name = df.columns[status_code_col_index]

                logging.info(f"第3步：过滤出未开始的比赛（'{status_code_col_name}' == '0'）")

                df_league_filtered[status_code_col_name] = df_league_filtered[status_code_col_name].astype(str)
                not_started_status_code = '0'
                is_not_started = df_league_filtered[status_code_col_name] == not_started_status_code

                df_final_filtered = df_league_filtered[is_not_started].copy()
                final_count = len(df_final_filtered)

                logging.info(f"第3步结果：从 {league_filter_count} 场比赛中保留 {final_count} 场未开始的比赛")

                if df_final_filtered.empty:
                    logging.warning(f"未找到未开始的比赛（状态码 '{not_started_status_code}'）")
                    return None
            else:
                logging.error(f"比赛状态码列索引 ({status_code_col_index}) 超出范围")
                return None

            # 选择特定列
            desired_columns = [
                'MatchID',
                'LeagueNameSimp',
                'HomeTeamSimp',
                'AwayTeamSimp',
                'StartTimeShort',
                'StandardizedLeague'  # 添加标准化后的联赛名称
            ]

            available_columns = [col for col in desired_columns if col in df_final_filtered.columns]

            if not available_columns:
                logging.error("未找到任何所需列")
                return df_final_filtered

            df_output = df_final_filtered[available_columns].copy()
            logging.info(f"已选择特定列：{available_columns}")
            logging.info(f"返回包含 {len(df_output)} 场未开始的比赛的DataFrame")
            return df_output

        except Exception as e:
            logging.error(f"处理提取的数据时出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    def delete_temp_files(self, directory=None):
        """
        删除临时文件

        Args:
            directory: 文件目录，如果为None则使用配置的DATA_DIR
        """
        try:
            # 使用配置的数据目录或传入的目录
            target_dir = directory if directory else DATA_DIR
            logging.info(f"正在清理目录中的临时文件: {target_dir}")

            # 检查目录是否存在
            if not os.path.exists(target_dir):
                logging.warning(f"目录不存在: {target_dir}")
                return

            txt_files = [f for f in os.listdir(target_dir) if f.endswith('.txt')]

            if not txt_files:
                logging.info("没有找到需要删除的临时文件")
                return

            for txt_file in txt_files:
                file_path = os.path.join(target_dir, txt_file)
                try:
                    os.remove(file_path)
                    logging.info(f"已删除: {file_path}")
                except Exception as e:
                    logging.error(f"删除文件 {file_path} 时出错: {e}")

            logging.info(f"成功删除了 {len(txt_files)} 个临时文件")
        except Exception as e:
            logging.error(f"删除文本文件时出错: {e}")
            import traceback
            logging.error(traceback.format_exc())

    def run(self, retry_count=3):
        """
        执行完整的爬取流程，增加重试机制

        Args:
            retry_count: 最大重试次数

        Returns:
            DataFrame: 处理后的比赛数据
        """
        for attempt in range(retry_count):
            try:
                # 获取比赛数据
                js_content = self.fetch_data()
                if not js_content:
                    logging.warning(f"第 {attempt+1} 次尝试获取数据失败，继续重试...")
                    continue

                # 解析比赛数据
                match_data_list = self.parse_javascript_data(js_content)
                if not match_data_list:
                    logging.warning(f"第 {attempt+1} 次尝试解析数据失败，继续重试...")
                    continue

                # 处理和过滤数据
                result = self.process_data(match_data_list)
                if result is not None:
                    logging.info(f"第 {attempt+1} 次尝试成功获取并处理数据")
                    return result

                logging.warning(f"第 {attempt+1} 次尝试处理数据失败，继续重试...")

            except Exception as e:
                logging.error(f"第 {attempt+1} 次尝试时发生错误: {e}")
                import traceback
                logging.error(traceback.format_exc())

            # 如果不是最后一次尝试，等待一会再重试
            if attempt < retry_count - 1:
                wait_time = 2 ** attempt  # 指数退避
                logging.info(f"等待 {wait_time} 秒后进行下一次尝试...")
                time.sleep(wait_time)

        logging.error(f"在 {retry_count} 次尝试后仍然失败")
        return None

    def run_parallel(self, urls, max_workers=15, max_retries=3, progress_callback=None):
        """
        增强的并行执行多个URL的爬取任务

        Args:
            urls: 要爬取的URL列表
            max_workers: 最大线程数
            max_retries: 每个URL的最大重试次数
            progress_callback: 进度回调函数，接收 (processed, total, success_count) 参数

        Returns:
            list: 成功爬取的数据列表
        """
        results = []
        success_count = 0
        total_urls = len(urls)

        logging.info(f"开始并行爬取 {total_urls} 个URL，使用 {max_workers} 个线程")

        # 重置统计信息
        self.reset_stats()

        # 使用进度条
        with tqdm(total=total_urls, desc="爬取比赛数据", unit="个URL") as pbar:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_url = {
                    executor.submit(self.fetch_data_with_retry, url, None, max_retries): url
                    for url in urls
                }

                for future in as_completed(future_to_url):
                    url = future_to_url[future]
                    try:
                        js_content = future.result()
                        if not js_content:
                            logging.warning(f"获取 {url} 的数据失败")
                            pbar.update(1)
                            continue

                        match_data_list = self.parse_javascript_data(js_content)
                        if not match_data_list:
                            logging.warning(f"解析 {url} 的数据失败")
                            pbar.update(1)
                            continue

                        # 更新解析统计
                        with self._lock:
                            self._stats['parsed_matches'] += len(match_data_list)

                        result = self.process_data(match_data_list)
                        if result is not None:
                            success_count += 1
                            logging.info(f"成功处理 {url} 的数据，获得 {len(result)} 场比赛")
                            results.append(result)
                        else:
                            logging.warning(f"处理 {url} 的数据失败")

                    except Exception as e:
                        logging.error(f"处理 {url} 时发生错误: {e}")

                    # 更新进度条
                    pbar.update(1)

                    # 调用进度回调
                    if progress_callback:
                        processed = len([f for f in future_to_url if f.done()])
                        progress_callback(processed, total_urls, success_count)

        # 最终统计
        success_rate = success_count / total_urls if total_urls > 0 else 0
        logging.info(f"并行爬取完成: 总计 {total_urls} 个URL, 成功 {success_count} 个, 成功率 {success_rate:.1%}")

        # 显示详细统计
        stats = self.get_stats()
        logging.info(f"详细统计: 请求成功率 {stats.get('success_rate', 0):.1%}, "
                    f"解析比赛数 {stats.get('parsed_matches', 0)}, "
                    f"总请求数 {stats.get('total_requests', 0)}")

        return results

    def close(self):
        """关闭HTTP会话，释放资源"""
        if hasattr(self, 'session') and self.session:
            self.session.close()
            logging.info("比赛爬虫会话已关闭")