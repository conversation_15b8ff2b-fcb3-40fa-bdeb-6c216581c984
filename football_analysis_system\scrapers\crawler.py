"""
爬虫模块 - 包含数据爬取、解析和网络请求相关功能
"""
import re
import time
import json
import random
import requests
import threading
from datetime import datetime
from requests.adapters import HTTPAdapter
from urllib3.util import Retry
import logging
from collections import deque, defaultdict
import math
from enum import Enum
from urllib.parse import urlparse

from .config import logger, USER_AGENTS


class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    HTTP_ERROR = "http_error"
    PARSE_ERROR = "parse_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    UNKNOWN_ERROR = "unknown_error"


class ThreadSafeDict:
    """线程安全的字典，用于跨线程共享状态"""
    def __init__(self):
        self.data = {}
        self.lock = threading.RLock()

    def get(self, key, default=None):
        with self.lock:
            return self.data.get(key, default)

    def set(self, key, value):
        with self.lock:
            self.data[key] = value

    def delete(self, key):
        with self.lock:
            if key in self.data:
                del self.data[key]

    def items(self):
        with self.lock:
            return list(self.data.items())

    def clear(self):
        with self.lock:
            self.data.clear()


class UserAgentRotator:
    """User-Agent轮换器"""

    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        ]
        # 如果配置中有USER_AGENTS，合并使用
        if USER_AGENTS:
            self.user_agents.extend(USER_AGENTS)
        self.index = 0
        self.lock = threading.Lock()

    def get_random_ua(self) -> str:
        """获取随机User-Agent"""
        return random.choice(self.user_agents)

    def get_next_ua(self) -> str:
        """获取下一个User-Agent"""
        with self.lock:
            ua = self.user_agents[self.index]
            self.index = (self.index + 1) % len(self.user_agents)
            return ua


class CircuitBreaker:
    """断路器模式实现"""

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self.lock = threading.Lock()

    def call(self, func, *args, **kwargs):
        """通过断路器调用函数"""
        with self.lock:
            if self.state == "OPEN":
                if time.time() - self.last_failure_time > self.recovery_timeout:
                    self.state = "HALF_OPEN"
                else:
                    raise Exception("Circuit breaker is OPEN")

            try:
                result = func(*args, **kwargs)
                if self.state == "HALF_OPEN":
                    self.state = "CLOSED"
                    self.failure_count = 0
                return result
            except Exception as e:
                self.failure_count += 1
                self.last_failure_time = time.time()

                if self.failure_count >= self.failure_threshold:
                    self.state = "OPEN"
                raise e


class RateLimiter:
    """增强的请求速率限制器，支持令牌桶算法和智能退避"""

    def __init__(self, requests_per_second=2.0, burst_size=10, circuit_breaker_threshold=25):
        """
        初始化速率限制器

        Args:
            requests_per_second: 每秒允许的请求数
            burst_size: 突发请求容量
            circuit_breaker_threshold: 触发熔断的连续失败请求数
        """
        # 令牌桶参数
        self.requests_per_second = requests_per_second
        self.burst_size = burst_size
        self.tokens = burst_size
        self.last_update = time.time()
        self.lock = threading.RLock()

        # 旧的速率限制逻辑（保持向后兼容）
        self.requests_per_minute = int(requests_per_second * 60)
        self.interval = 1.0 / requests_per_second
        self.last_request_times = deque(maxlen=self.requests_per_minute)

        # 熔断器状态
        self.failure_count = 0
        self.circuit_open = False
        self.circuit_breaker_threshold = circuit_breaker_threshold
        self.circuit_reset_time = None
        self.base_cooldown_period = 20

        # 域名请求计数和状态
        self.domain_stats = {}

        # 统计信息
        self.stats = defaultdict(int)

    def acquire_token(self, tokens: int = 1) -> bool:
        """获取令牌"""
        with self.lock:
            now = time.time()
            # 添加令牌
            elapsed = now - self.last_update
            self.tokens = min(
                self.burst_size,
                self.tokens + elapsed * self.requests_per_second
            )
            self.last_update = now

            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False

    def wait_for_token(self, tokens: int = 1, max_wait: float = 300.0):
        """等待获取令牌"""
        start_time = time.time()
        while not self.acquire_token(tokens):
            if time.time() - start_time > max_wait:
                logger.warning(f"等待令牌超时 {max_wait}秒，跳过请求")
                return False
            time.sleep(0.1)
        return True

    def wait_if_needed(self, domain=None):
        """
        根据之前的请求频率决定是否需要等待（保持向后兼容）

        Args:
            domain: 请求的域名，用于分开统计不同域名的请求

        Returns:
            等待的时间(秒)，如果超过最大等待时间则返回-1表示跳过
        """
        # 使用新的令牌桶算法
        if not self.wait_for_token():
            return -1

        # 最大等待时间限制（5分钟）
        MAX_WAIT_TIME = 300

        with self.lock:
            # 检查熔断器状态
            if self.circuit_open:
                current_time = time.time()
                if self.circuit_reset_time and current_time >= self.circuit_reset_time:
                    # 重置熔断器
                    logger.info("熔断器重置，恢复请求")
                    self.circuit_open = False
                    self.failure_count = 0
                    self.circuit_reset_time = None
                else:
                    # 熔断器开启，检查是否超过最大等待时间
                    remaining_time = 0
                    if self.circuit_reset_time:
                        remaining_time = max(0, self.circuit_reset_time - current_time)

                    if remaining_time > MAX_WAIT_TIME:
                        logger.warning(f"熔断器等待时间 {remaining_time:.1f}秒 超过最大限制 {MAX_WAIT_TIME}秒，跳过此请求")
                        return -1  # 返回-1表示不等待，直接跳过

                    logger.warning(f"熔断器开启中，请求被拒绝，剩余冷却时间: {remaining_time:.1f}秒")
                    return remaining_time

            # 特定域名的限制
            if domain:
                if domain not in self.domain_stats:
                    self.domain_stats[domain] = {
                        'last_request': 0,
                        'failure_count': 0,
                        'backoff_factor': 1.0
                    }

                domain_stat = self.domain_stats[domain]
                current_time = time.time()

                # 计算基于退避因子的等待时间
                backoff_time = self.interval * domain_stat['backoff_factor']
                time_since_last = current_time - domain_stat['last_request']

                if time_since_last < backoff_time:
                    wait_time = backoff_time - time_since_last

                    # 检查是否超过最大等待时间
                    if wait_time > MAX_WAIT_TIME:
                        logger.warning(f"域名 {domain} 需要等待 {wait_time:.2f}秒，超过最大限制 {MAX_WAIT_TIME}秒，跳过此请求")
                        return -1  # 返回-1表示不等待，直接跳过

                    # 添加随机抖动，避免请求同步
                    jitter = random.uniform(0, 0.5 * wait_time)
                    total_wait = min(wait_time + jitter, MAX_WAIT_TIME)
                    logger.debug(f"域名 {domain} 需要等待 {total_wait:.2f}秒 (基础: {wait_time:.2f}秒, 抖动: {jitter:.2f}秒)")
                    return total_wait

                # 更新最后请求时间
                domain_stat['last_request'] = current_time

            # 记录本次请求时间
            current_time = time.time()
            self.last_request_times.append(current_time)
            return 0

    def record_success(self, domain=None):
        """记录成功的请求，减少退避因子"""
        with self.lock:
            self.stats['success_count'] += 1

            if domain and domain in self.domain_stats:
                # 成功后逐渐减小退避因子，但不低于1.0
                self.domain_stats[domain]['backoff_factor'] = max(
                    1.0,
                    self.domain_stats[domain]['backoff_factor'] * 0.9
                )
                # 减少失败计数
                self.domain_stats[domain]['failure_count'] = max(
                    0,
                    self.domain_stats[domain]['failure_count'] - 1
                )

            # 全局熔断器状态更新
            self.failure_count = max(0, self.failure_count - 1)

    def record_failure(self, domain=None, status_code=None, error_type=None):
        """
        记录失败的请求，增加退避因子

        Args:
            domain: 请求的域名
            status_code: HTTP状态码，用于区分不同类型的错误
            error_type: 错误类型
        """
        with self.lock:
            self.stats['error_count'] += 1
            if error_type:
                self.stats[f'error_{error_type.value}'] += 1

            # 全局熔断器逻辑
            self.failure_count += 1

            # 如果连续失败次数达到阈值，开启熔断器
            if self.failure_count >= self.circuit_breaker_threshold:
                # 计算冷却时间，随着失败次数增加而增加
                cooldown_multiplier = math.floor(self.failure_count / self.circuit_breaker_threshold)
                cooldown_time = min(self.base_cooldown_period * (2 ** cooldown_multiplier), 600)  # 最大10分钟

                self.circuit_open = True
                self.circuit_reset_time = time.time() + cooldown_time

                logger.warning(f"熔断器开启，冷却时间: {cooldown_time:.1f}秒 (连续失败 {self.failure_count} 次)")

            # 域名特定的退避逻辑
            if domain:
                if domain not in self.domain_stats:
                    self.domain_stats[domain] = {
                        'last_request': 0,
                        'failure_count': 0,
                        'backoff_factor': 1.0
                    }

                domain_stat = self.domain_stats[domain]
                domain_stat['failure_count'] += 1

                # 根据状态码调整退避策略
                if status_code == 429:  # 速率限制
                    domain_stat['backoff_factor'] *= 3.0  # 大幅增加退避
                elif status_code in [503, 502, 504]:  # 服务器错误
                    domain_stat['backoff_factor'] *= 2.0
                elif status_code in [403, 404]:  # 客户端错误
                    domain_stat['backoff_factor'] *= 1.5
                else:
                    domain_stat['backoff_factor'] *= 1.2

                # 限制最大退避因子
                domain_stat['backoff_factor'] = min(domain_stat['backoff_factor'], 10.0)

                logger.debug(f"域名 {domain} 失败计数: {domain_stat['failure_count']}, "
                           f"退避因子: {domain_stat['backoff_factor']:.2f}, "
                           f"状态码: {status_code}")

    def get_stats(self):
        """获取统计信息"""
        with self.lock:
            stats = dict(self.stats)
            total_requests = stats.get('success_count', 0) + stats.get('error_count', 0)
            if total_requests > 0:
                stats['success_rate'] = stats.get('success_count', 0) / total_requests
            else:
                stats['success_rate'] = 0
            return stats

    def get_domain_from_url(self, url):
        """从URL中提取域名"""
        try:
            parsed = urlparse(url)
            return parsed.netloc
        except Exception:
            return None


class CrawlerUtils:
    """增强的爬虫工具类，提供爬取和解析数据的方法"""

    # 创建全局速率限制器实例
    _rate_limiter = RateLimiter(requests_per_second=2.0, circuit_breaker_threshold=25)
    _ua_rotator = UserAgentRotator()
    _circuit_breaker = CircuitBreaker()

    @staticmethod
    def create_session(retry_times=3, pool_connections=20, pool_maxsize=20):
        """
        创建配置好的HTTP会话，增强连接池和重试策略

        Args:
            retry_times: 重试次数
            pool_connections: 连接池大小
            pool_maxsize: 连接池最大大小

        Returns:
            配置好的requests.Session对象
        """
        session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=retry_times,
            status_forcelist=[429, 500, 502, 503, 504],
            backoff_factor=1.5,
            raise_on_status=False
        )

        # 配置适配器，增强连接池
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=pool_connections,
            pool_maxsize=pool_maxsize
        )

        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session

    @staticmethod
    def get_rate_limiter():
        """获取全局速率限制器实例"""
        return CrawlerUtils._rate_limiter

    @staticmethod
    def get_random_user_agent():
        """获取随机User-Agent"""
        return CrawlerUtils._ua_rotator.get_random_ua()

    @staticmethod
    def get_headers(referer=None, use_random_ua=True):
        """
        获取增强的HTTP请求头

        Args:
            referer: 可选的Referer头
            use_random_ua: 是否使用随机User-Agent

        Returns:
            HTTP请求头字典
        """
        headers = {
            'User-Agent': CrawlerUtils.get_random_user_agent() if use_random_ua else USER_AGENTS[0],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
        }

        if referer:
            headers['Referer'] = referer

        return headers

    @staticmethod
    def classify_error(error: Exception) -> ErrorType:
        """分类错误类型"""
        if isinstance(error, requests.exceptions.Timeout):
            return ErrorType.TIMEOUT_ERROR
        elif isinstance(error, requests.exceptions.ConnectionError):
            return ErrorType.NETWORK_ERROR
        elif isinstance(error, requests.exceptions.HTTPError):
            if hasattr(error.response, 'status_code') and error.response.status_code == 429:
                return ErrorType.RATE_LIMIT_ERROR
            return ErrorType.HTTP_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR

    @staticmethod
    def generate_r_param():
        """生成随机参数"""
        return str(random.randint(10000000, 99999999))

    @staticmethod
    def fetch_data(session, url, min_delay=0.5, max_delay=1.5, timeout=30, retry_times=3):
        """增强的数据获取方法，带智能重试和错误处理"""
        # 获取速率限制器
        rate_limiter = CrawlerUtils.get_rate_limiter()

        # 从URL中提取域名
        domain = rate_limiter.get_domain_from_url(url)

        for retry in range(retry_times):
            try:
                # 检查是否需要等待
                wait_time = rate_limiter.wait_if_needed(domain)
                if wait_time == -1:
                    # 等待时间超过限制，跳过此请求
                    logger.warning(f"跳过请求 {url}，等待时间过长")
                    return None
                elif wait_time > 0:
                    logger.info(f"速率限制: 等待 {wait_time:.2f} 秒后再请求 {domain}")
                    time.sleep(wait_time)

                # 生成毫秒级时间戳
                timestamp = int(time.time() * 1000)

                # 添加时间戳参数到URL
                if '?' in url:
                    request_url = f"{url}&t={timestamp}"
                else:
                    request_url = f"{url}?t={timestamp}"

                logger.debug(f"请求URL: {request_url}" + (f" (重试 {retry+1}/{retry_times})" if retry > 0 else ""))

                response = session.get(
                    request_url,
                    headers=CrawlerUtils.get_headers(),
                    timeout=timeout
                )

                # 检查响应状态
                if response.status_code == 429:
                    logger.warning(f"遇到速率限制 (429)，URL: {url}")
                    rate_limiter.record_failure(domain, 429, ErrorType.RATE_LIMIT_ERROR)
                    # 特殊处理速率限制：从响应头获取等待时间
                    retry_after = response.headers.get('Retry-After')
                    if retry_after:
                        wait_time = int(retry_after)
                    else:
                        wait_time = random.uniform(30, 60)

                    if retry < retry_times - 1:
                        logger.info(f"速率限制，等待 {wait_time} 秒后重试")
                        time.sleep(wait_time)
                        continue
                    else:
                        logger.error(f"达到最大重试次数，放弃请求: {url}")
                        return None

                response.raise_for_status()

                # 请求成功
                rate_limiter.record_success(domain)

                # 添加随机延迟，模拟人类行为
                delay = random.uniform(min_delay, max_delay)
                if delay > 0:
                    time.sleep(delay)

                logger.debug(f"成功获取数据，响应长度: {len(response.text)} 字符")
                return response.text

            except requests.exceptions.Timeout as e:
                error_type = ErrorType.TIMEOUT_ERROR
                rate_limiter.record_failure(domain, None, error_type)
                logger.warning(f"请求超时: {url} (尝试 {retry+1}/{retry_times})")

                if retry < retry_times - 1:
                    # 指数退避
                    wait_time = (1.5 ** retry) + random.uniform(0.1, 0.5)
                    logger.info(f"超时重试，等待 {wait_time:.2f} 秒")
                    time.sleep(wait_time)
                else:
                    logger.error(f"请求超时，达到最大重试次数: {url}")

            except requests.exceptions.ConnectionError as e:
                error_type = ErrorType.NETWORK_ERROR
                rate_limiter.record_failure(domain, None, error_type)
                logger.warning(f"连接错误: {url} - {str(e)} (尝试 {retry+1}/{retry_times})")

                if retry < retry_times - 1:
                    # 网络错误使用更长的等待时间
                    wait_time = (2.0 ** retry) + random.uniform(1.0, 3.0)
                    logger.info(f"连接错误重试，等待 {wait_time:.2f} 秒")
                    time.sleep(wait_time)
                else:
                    logger.error(f"连接错误，达到最大重试次数: {url}")

            except requests.exceptions.HTTPError as e:
                status_code = e.response.status_code if e.response else None
                error_type = ErrorType.HTTP_ERROR
                rate_limiter.record_failure(domain, status_code, error_type)

                logger.warning(f"HTTP错误 {status_code}: {url} (尝试 {retry+1}/{retry_times})")

                # 某些HTTP错误不值得重试
                if status_code in [400, 401, 403, 404, 410]:
                    logger.error(f"HTTP {status_code} 错误不重试: {url}")
                    break

                if retry < retry_times - 1:
                    # HTTP错误的退避策略
                    if status_code in [500, 502, 503, 504]:
                        wait_time = (1.5 ** retry) + random.uniform(0.5, 2.0)
                    else:
                        wait_time = (1.2 ** retry) + random.uniform(0.1, 0.5)

                    logger.info(f"HTTP错误重试，等待 {wait_time:.2f} 秒")
                    time.sleep(wait_time)
                else:
                    logger.error(f"HTTP错误，达到最大重试次数: {url}")

            except Exception as e:
                error_type = ErrorType.UNKNOWN_ERROR
                rate_limiter.record_failure(domain, None, error_type)
                logger.error(f"未知错误: {url} - {str(e)} (尝试 {retry+1}/{retry_times})")

                if retry < retry_times - 1:
                    wait_time = (1.5 ** retry) + random.uniform(0.5, 1.5)
                    logger.info(f"未知错误重试，等待 {wait_time:.2f} 秒")
                    time.sleep(wait_time)
                else:
                    logger.error(f"未知错误，达到最大重试次数: {url}")

        logger.error(f"所有重试尝试失败: {url}")
        return None

    @staticmethod
    def fetch_odds_data(session, matchid, min_delay=0.5, max_delay=1.5, timeout=30, retry_times=3):
        """增强的赔率数据获取方法，带智能重试和错误处理"""
        # 获取速率限制器
        rate_limiter = CrawlerUtils.get_rate_limiter()

        # 目标域名
        domain = "1x2d.titan007.com"

        for retry in range(retry_times):
            try:
                # 检查是否需要等待
                wait_time = rate_limiter.wait_if_needed(domain)
                if wait_time == -1:
                    # 等待时间超过限制，跳过此请求
                    logger.warning(f"跳过请求赔率数据 (matchid={matchid})，等待时间过长")
                    return None
                elif wait_time > 0:
                    logger.info(f"速率限制: 等待 {wait_time:.2f} 秒后再请求赔率数据 (matchid={matchid})")
                    time.sleep(wait_time)

                r_param = CrawlerUtils.generate_r_param()
                url = f"https://{domain}/{matchid}.js?r={r_param}"

                referer = f"https://1x2.titan007.com/oddslist/{matchid}.htm"
                headers = CrawlerUtils.get_headers(referer)

                # 添加赔率专用请求头
                headers.update({
                    'Accept': 'text/javascript, application/javascript, */*; q=0.01',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Cache-Control': 'no-cache'
                })

                logger.debug(f"请求赔率URL: {url}" + (f" (重试 {retry+1}/{retry_times})" if retry > 0 else ""))

                response = session.get(
                    url,
                    headers=headers,
                    timeout=timeout
                )

                # 检查响应状态
                if response.status_code == 429:
                    logger.warning(f"遇到速率限制 (429)，matchid={matchid}")
                    rate_limiter.record_failure(domain, 429, ErrorType.RATE_LIMIT_ERROR)
                    # 特殊处理速率限制
                    retry_after = response.headers.get('Retry-After')
                    if retry_after:
                        wait_time = int(retry_after)
                    else:
                        wait_time = random.uniform(30, 60)

                    if retry < retry_times - 1:
                        logger.info(f"速率限制，等待 {wait_time} 秒后重试")
                        time.sleep(wait_time)
                        continue
                    else:
                        logger.error(f"达到最大重试次数，放弃请求赔率数据: matchid={matchid}")
                        return None

                # 对于404错误，不重试直接返回None
                if response.status_code == 404:
                    logger.error(f"赔率数据不存在 (matchid={matchid}): 404 Not Found")
                    rate_limiter.record_failure(domain, 404, ErrorType.HTTP_ERROR)
                    return None

                response.raise_for_status()

                # 记录成功请求
                rate_limiter.record_success(domain)

                # 添加随机延时，模拟人类行为
                delay = random.uniform(min_delay, max_delay)
                if delay > 0:
                    time.sleep(delay)

                # 保存原始响应内容，仅在DEBUG级别时
                if logger.level <= logging.DEBUG:
                    response_text = response.text
                    try:
                        with open(f"raw_odds_{matchid}.js", "w", encoding="utf-8") as f:
                            f.write(response_text)
                    except IOError:
                        pass  # 忽略文件写入错误

                logger.debug(f"赔率数据获取成功，响应长度: {len(response.text)} 字符")
                return response.text

            except requests.exceptions.Timeout as e:
                error_type = ErrorType.TIMEOUT_ERROR
                rate_limiter.record_failure(domain, None, error_type)
                logger.warning(f"赔率请求超时 (matchid={matchid}) (尝试 {retry+1}/{retry_times})")

                if retry < retry_times - 1:
                    wait_time = (1.5 ** retry) + random.uniform(0.1, 0.5)
                    logger.info(f"超时重试，等待 {wait_time:.2f} 秒")
                    time.sleep(wait_time)
                else:
                    logger.error(f"赔率请求超时，达到最大重试次数: matchid={matchid}")

            except requests.exceptions.ConnectionError as e:
                error_type = ErrorType.NETWORK_ERROR
                rate_limiter.record_failure(domain, None, error_type)
                logger.warning(f"赔率请求连接错误 (matchid={matchid}) - {str(e)} (尝试 {retry+1}/{retry_times})")

                if retry < retry_times - 1:
                    wait_time = (2.0 ** retry) + random.uniform(1.0, 3.0)
                    logger.info(f"连接错误重试，等待 {wait_time:.2f} 秒")
                    time.sleep(wait_time)
                else:
                    logger.error(f"赔率请求连接错误，达到最大重试次数: matchid={matchid}")

            except requests.exceptions.HTTPError as e:
                status_code = e.response.status_code if e.response else None
                error_type = ErrorType.HTTP_ERROR
                rate_limiter.record_failure(domain, status_code, error_type)

                logger.warning(f"赔率请求HTTP错误 {status_code} (matchid={matchid}) (尝试 {retry+1}/{retry_times})")

                # 某些HTTP错误不值得重试
                if status_code in [400, 401, 403, 404, 410]:
                    logger.error(f"HTTP {status_code} 错误不重试: matchid={matchid}")
                    break

                if retry < retry_times - 1:
                    if status_code in [500, 502, 503, 504]:
                        wait_time = (1.5 ** retry) + random.uniform(0.5, 2.0)
                    else:
                        wait_time = (1.2 ** retry) + random.uniform(0.1, 0.5)

                    logger.info(f"HTTP错误重试，等待 {wait_time:.2f} 秒")
                    time.sleep(wait_time)
                else:
                    logger.error(f"赔率请求HTTP错误，达到最大重试次数: matchid={matchid}")

            except Exception as e:
                error_type = ErrorType.UNKNOWN_ERROR
                rate_limiter.record_failure(domain, None, error_type)
                logger.error(f"赔率请求未知错误 (matchid={matchid}) - {str(e)} (尝试 {retry+1}/{retry_times})")

                if retry < retry_times - 1:
                    wait_time = (1.5 ** retry) + random.uniform(0.5, 1.5)
                    logger.info(f"未知错误重试，等待 {wait_time:.2f} 秒")
                    time.sleep(wait_time)
                else:
                    logger.error(f"赔率请求未知错误，达到最大重试次数: matchid={matchid}")

        logger.error(f"所有重试尝试失败，无法获取赔率数据: matchid={matchid}")
        return None

    @staticmethod
    def parse_js_data(js_content):
        """解析JavaScript格式数据为Python字典"""
        data = {}

        # 解析联赛信息
        league_match = re.search(r'var arrLeague = \[(.*?)\];', js_content, re.DOTALL)
        if league_match:
            # 特殊处理，转为有效的JSON格式
            league_str = '[' + league_match.group(1) + ']'
            # 处理JS中的单引号和末尾逗号
            league_str = league_str.replace("'", '"').replace(',]', ']')
            try:
                data['league'] = json.loads(league_str)
                logger.info("联赛数据解析成功")
            except json.JSONDecodeError as e:
                logger.error(f"联赛数据解析失败: {e}")
                # 保存出错的数据到文件，方便调试
                with open("league_error.txt", "w", encoding="utf-8") as f:
                    f.write(league_str)

        # 解析球队信息
        teams_match = re.search(r'var arrTeam = \[(.*?)\];', js_content, re.DOTALL)
        if teams_match:
            # 提取球队数组字符串
            teams_str = '[' + teams_match.group(1) + ']'
            # 替换非标准JSON格式
            teams_str = teams_str.replace("'", '"').replace(',]', ']')
            try:
                data['teams'] = json.loads(teams_str)
                logger.info(f"球队数据解析成功，共{len(data['teams'])}支球队")
            except json.JSONDecodeError as e:
                logger.error(f"球队数据解析失败: {e}")
                # 保存出错的数据到文件，方便调试
                with open("teams_error.txt", "w", encoding="utf-8") as f:
                    f.write(teams_str)

        # 解析比赛数据
        data['matches'] = {}

        # 尝试多种正则表达式匹配模式
        match_patterns = [
            r'jh\["R_(\d+)"\]\s*=\s*\[(.*?)\];',  # jh["R_1"] = [...]
            r'jh\[\'R_(\d+)\'\]\s*=\s*\[(.*?)\];',  # jh['R_1'] = [...]
            r'jh\["?R?_?(\d+)"?\]\s*=\s*\[(.*?)\];',  # 更宽松的匹配
            r'jh\[(\d+)\]\s*=\s*\[(.*?)\];'  # jh[1] = [...]
        ]

        match_rounds = []
        for pattern in match_patterns:
            match_rounds = re.findall(pattern, js_content, re.DOTALL)
            if match_rounds:
                logger.debug(f"使用模式 '{pattern}' 成功匹配到 {len(match_rounds)} 轮比赛数据")
                break

        if not match_rounds:
            logger.error(f"未能匹配到任何轮次的比赛数据，请检查数据格式。")
            return data  # 提前返回

        for round_num, round_data in match_rounds:
            # 检查是否为空数据
            if not round_data.strip():
                continue

            try:
                # 清理数据中的异常字符
                cleaned_data = round_data
                # 处理未定义值和空值
                cleaned_data = re.sub(r',,', ',null,', cleaned_data)
                cleaned_data = re.sub(r',\s*,', ',null,', cleaned_data)
                # 处理末尾逗号
                cleaned_data = re.sub(r',\s*\]', ']', cleaned_data)
                # 确保Javascript undefined转为null
                cleaned_data = re.sub(r'undefined', 'null', cleaned_data)

                # 构建JSON字符串
                round_str = '[' + cleaned_data + ']'
                # 尝试直接替换所有单引号为双引号
                try:
                    # 先尝试简单替换
                    simple_str = round_str.replace("'", '"')
                    data['matches'][int(round_num)] = json.loads(simple_str)
                    logger.debug(f"轮次{round_num}数据解析成功")
                except json.JSONDecodeError:
                    # 如果简单替换失败，使用更复杂的正则替换
                    # 替换单引号为双引号，但跳过嵌套引号情况
                    complex_str = re.sub(r"(?<!\w)'(.*?)'(?!\w)", r'"\1"', round_str)
                    complex_str = complex_str.replace(",]", "]")
                    complex_str = complex_str.replace(",,", ",null,")

                    data['matches'][int(round_num)] = json.loads(complex_str)
                    logger.debug(f"轮次{round_num}数据通过复杂解析成功")

            except json.JSONDecodeError as e:
                logger.error(f"轮次{round_num}数据解析失败: {e}")
                # 保存出错的数据到文件，方便调试
                with open(f"round_{round_num}_error.txt", "w", encoding="utf-8") as f:
                    f.write(round_data)
            except Exception as e:
                logger.error(f"轮次{round_num}处理异常: {e}")

        # 解析积分榜数据
        score_patterns = {
            'totalScore': r'var totalScore = \[(.*?)\];',
            'homeScore': r'var homeScore = \[(.*?)\];',
            'guestScore': r'var guestScore = \[(.*?)\];',
            'halfScore': r'var halfScore = \[(.*?)\];',
            'homeHalfScore': r'var homeHalfScore = \[(.*?)\];',
            'guestHalfScore': r'var guestHalfScore = \[(.*?)\];'
        }

        data['standings'] = {}
        for score_type, pattern in score_patterns.items():
            score_match = re.search(pattern, js_content, re.DOTALL)
            if score_match:
                score_str = '[' + score_match.group(1) + ']'
                score_str = score_str.replace("'", '"').replace(',,', ',null,').replace(',]', ']')
                try:
                    data['standings'][score_type] = json.loads(score_str)
                    logger.debug(f"{score_type}数据解析成功，共{len(data['standings'][score_type])}条记录")
                except json.JSONDecodeError as e:
                    logger.error(f"{score_type}数据解析失败: {e}")
                    # 保存出错的数据到文件，方便调试
                    with open(f"{score_type}_error.txt", "w", encoding="utf-8") as f:
                        f.write(score_str)

        # 解析积分区域颜色设置
        zone_match = re.search(r'var scoreColor = \[(.*?)\];', js_content, re.DOTALL)
        if zone_match:
            zone_str = '[' + zone_match.group(1) + ']'
            zone_str = zone_str.replace("'", '"').replace(',]', ']')
            try:
                data['zones'] = json.loads(zone_str)
                logger.debug(f"积分区域设置解析成功，共{len(data['zones'])}个区域")
            except json.JSONDecodeError as e:
                logger.error(f"积分区域设置解析失败: {e}")
                # 保存出错的数据到文件，方便调试
                with open("zones_error.txt", "w", encoding="utf-8") as f:
                    f.write(zone_str)

        # 获取最后更新时间
        update_time_match = re.search(r"var lastUpdateTime = '(.*?)';", js_content)
        if update_time_match:
            data['lastUpdateTime'] = update_time_match.group(1)
            logger.debug(f"最后更新时间: {data['lastUpdateTime']}")

        return data

    @staticmethod
    def extract_value(text, pattern):
        """提取值"""
        match = re.search(pattern, text)
        return match.group(1) if match else ""

    @staticmethod
    def parse_odds_data(odds_js, matchid):
        """解析赔率数据JS"""
        logger.debug(f"解析比赛 {matchid} 的赔率数据")

        try:
            if not odds_js:
                logger.error("赔率数据为空")
                return None

            # 保存原始数据仅在DEBUG级别
            if logger.level <= logging.DEBUG:
                with open(f"odds_data_{matchid}.txt", "w", encoding="utf-8") as f:
                    f.write(odds_js)

            # 尝试多种正则表达式匹配模式
            patterns = [
                r'var\s+match_oddsData\s*=\s*(\{.*?\});',  # 标准格式
                r'var\s+game\s*=\s*Array\((.*?)\);',       # 旧格式
                r'var\s+oddsList\s*=\s*(\{.*?\});',        # 可能的另一种格式
                r'var\s+odds\s*=\s*(\{.*?\});',            # 可能的简化格式
                r'var\s+[a-zA-Z0-9_]+\s*=\s*(\{.*?"odds".*?\});', # 更通用的格式，查找包含odds键的对象
                r'(\{"odds".*?\});'                         # 直接查找odds对象
            ]

            match = None
            matched_pattern = None

            for pattern in patterns:
                match = re.search(pattern, odds_js, re.DOTALL)
                if match:
                    matched_pattern = pattern
                    # 减少日志输出，移除匹配成功信息
                    break

            if not match:
                logger.error(f"未能匹配到赔率数据格式 (matchid={matchid})")

                # 仅在DEBUG级别查找变量名
                if logger.level <= logging.DEBUG:
                    var_names = re.findall(r'var\s+([a-zA-Z0-9_]+)\s*=', odds_js)
                    if var_names:
                        logger.debug(f"在JS中找到的变量名: {var_names}")

                return None

            # 提取匹配到的数据
            if matched_pattern == patterns[1]:  # 旧格式 - Array格式
                # 处理旧格式的game数组
                game_str = match.group(1)
                # 构建兼容的旧格式数据解析
                return CrawlerUtils.parse_old_format_odds(game_str, matchid)

            # 默认处理JSON格式
            odds_json = match.group(1)

            # 尝试解析JSON
            try:
                odds_data = json.loads(odds_json)
            except json.JSONDecodeError:
                logger.warning(f"解析赔率JSON失败，尝试修复后再解析: {matchid}")

                # 修复可能的JSON格式问题
                odds_json = odds_json.replace("'", '"')
                odds_json = re.sub(r'([{,]\s*)(\w+)(\s*:)', r'\1"\2"\3', odds_json)
                odds_json = re.sub(r',\s*}', '}', odds_json)

                try:
                    odds_data = json.loads(odds_json)
                except json.JSONDecodeError as e:
                    logger.error(f"修复后仍无法解析赔率数据: {e}")
                    # 保存修复后的JSON仅在DEBUG级别
                    if logger.level <= logging.DEBUG:
                        with open(f"odds_json_{matchid}.txt", "w", encoding="utf-8") as f:
                            f.write(odds_json)
                    return None

            # 检查是否获取到公司赔率数据
            if 'odds' not in odds_data:
                # 尝试查找替代结构
                if isinstance(odds_data, dict):
                    for key, value in odds_data.items():
                        if isinstance(value, dict) and len(value) > 5:
                            # 移除详细日志
                            odds_data = {'odds': value}
                            break

                if 'odds' not in odds_data:
                    logger.warning(f"赔率数据中缺少odds字段: {matchid}")
                    # 仅在DEBUG级别保存解析后的数据
                    if logger.level <= logging.DEBUG:
                        with open(f"parsed_odds_{matchid}.json", "w", encoding="utf-8") as f:
                            json.dump(odds_data, f, ensure_ascii=False, indent=2)
                    return None

            # 获取目标公司列表
            from scrapers.config import TARGET_COMPANIES
            target_company_ids = set(TARGET_COMPANIES.keys())

            # 仅在DEBUG级别记录公司ID列表
            if logger.level <= logging.DEBUG:
                actual_company_ids = list(odds_data['odds'].keys())
                logger.debug(f"比赛 {matchid} 获取到的原始公司ID: {actual_company_ids}")
                logger.debug(f"配置的目标公司ID: {list(target_company_ids)}")

            # 过滤只保留目标公司的赔率
            filtered_odds = []
            for company_id, company_odds in odds_data['odds'].items():
                # 同时尝试字符串和数字格式匹配
                if (company_id in target_company_ids or
                    (company_id.isdigit() and str(int(company_id)) in target_company_ids) or
                    (str(company_id) in target_company_ids)):
                    # 提取并保存该公司的赔率数据
                    try:
                        # 初赔
                        init_odds = company_odds.get('initData', None)
                        # 临场即时赔率
                        real_odds = company_odds.get('realTimeData', None) or company_odds.get('newData', None)

                        if init_odds and real_odds:
                            odds_record = {
                                'company_id': company_id,
                                'company_odds_id': company_odds.get('id', ''),
                                'init_home_win': init_odds[0],
                                'init_draw': init_odds[1],
                                'init_away_win': init_odds[2],
                                'init_home_win_rate': init_odds[3],
                                'init_draw_rate': init_odds[4],
                                'init_away_win_rate': init_odds[5],
                                'init_return_rate': init_odds[6],
                                'real_home_win': real_odds[0],
                                'real_draw': real_odds[1],
                                'real_away_win': real_odds[2],
                                'real_home_win_rate': real_odds[3],
                                'real_draw_rate': real_odds[4],
                                'real_away_win_rate': real_odds[5],
                                'real_return_rate': real_odds[6],
                                'k_home': real_odds[7],
                                'k_draw': real_odds[8],
                                'k_away': real_odds[9],
                                'update_time': real_odds[10]
                            }
                            filtered_odds.append(odds_record)
                            # 移除成功匹配的详细日志
                    except Exception as e:
                        logger.error(f"处理公司 {company_id} 的赔率数据出错: {e}")

            if not filtered_odds:
                logger.warning(f"比赛 {matchid} 没有找到任何匹配的公司赔率数据")
                # 仅在DEBUG级别保存数据
                if logger.level <= logging.DEBUG:
                    with open(f"odds_data_{matchid}_full.json", "w", encoding="utf-8") as f:
                        json.dump(odds_data, f, ensure_ascii=False, indent=2)
            else:
                # 添加一条简洁的日志，记录匹配到的公司数量
                logger.info(f"比赛 {matchid} 匹配到 {len(filtered_odds)} 家公司的赔率数据")

            return {'matchid': matchid, 'odds_list': filtered_odds}
        except Exception as e:
            logger.error(f"解析赔率数据失败 (matchid={matchid}): {e}")
            return None

    @staticmethod
    def parse_old_format_odds(game_str, matchid):
        """解析旧格式的赔率数据"""
        try:
            # 移除旧格式解析的详细日志

            # 清理和分割数据
            items = game_str.split('","')
            items = [item.replace('"', '') for item in items]

            # 获取目标公司列表
            from scrapers.config import TARGET_COMPANIES
            target_company_ids = set(TARGET_COMPANIES.keys())

            # 过滤只保留目标公司的赔率
            filtered_odds = []
            for item in items:
                parts = item.split('|')
                if len(parts) < 20:
                    continue

                company_id = parts[0]

                # 匹配目标公司
                if company_id in target_company_ids:
                    try:
                        odds_record = {
                            'company_id': company_id,
                            'company_odds_id': parts[1],
                            'init_home_win': float(parts[3] or 0),
                            'init_draw': float(parts[4] or 0),
                            'init_away_win': float(parts[5] or 0),
                            'init_home_win_rate': float(parts[6] or 0),
                            'init_draw_rate': float(parts[7] or 0),
                            'init_away_win_rate': float(parts[8] or 0),
                            'init_return_rate': float(parts[9] or 0),
                            'real_home_win': float(parts[10] or 0),
                            'real_draw': float(parts[11] or 0),
                            'real_away_win': float(parts[12] or 0),
                            'real_home_win_rate': float(parts[13] or 0),
                            'real_draw_rate': float(parts[14] or 0),
                            'real_away_win_rate': float(parts[15] or 0),
                            'real_return_rate': float(parts[16] or 0),
                            'k_home': float(parts[17] or 0),
                            'k_draw': float(parts[18] or 0),
                            'k_away': float(parts[19] or 0),
                            'update_time': parts[20] if len(parts) > 20 else ""
                        }
                        filtered_odds.append(odds_record)
                        # 移除成功匹配的详细日志
                    except (ValueError, IndexError) as e:
                        logger.error(f"处理旧格式赔率数据出错: {e}")

            # 添加一条简洁的日志，仅记录匹配到的公司数量
            if filtered_odds:
                logger.info(f"比赛 {matchid} 从旧格式数据匹配到 {len(filtered_odds)} 家公司的赔率")

            return {'matchid': matchid, 'odds_list': filtered_odds}
        except Exception as e:
            logger.error(f"解析旧格式赔率数据失败: {e}")
            return None

    @staticmethod
    def get_league_id_from_url(url):
        """从URL中提取联赛ID"""
        # 提取形如 s36.js 或 s36_123.js 中的36
        match = re.search(r's(\d+)(?:_\d+)?\.js', url)
        if match:
            return int(match.group(1))
        return None

    @staticmethod
    def get_stats():
        """获取爬虫统计信息"""
        return CrawlerUtils._rate_limiter.get_stats()

    @staticmethod
    def reset_stats():
        """重置爬虫统计信息"""
        CrawlerUtils._rate_limiter.stats.clear()
        logger.info("爬虫统计信息已重置")

    @staticmethod
    def get_domain_stats():
        """获取域名特定的统计信息"""
        with CrawlerUtils._rate_limiter.lock:
            return dict(CrawlerUtils._rate_limiter.domain_stats)

    @staticmethod
    def get_circuit_breaker_status():
        """获取断路器状态"""
        rate_limiter = CrawlerUtils._rate_limiter
        return {
            'circuit_open': rate_limiter.circuit_open,
            'failure_count': rate_limiter.failure_count,
            'circuit_reset_time': rate_limiter.circuit_reset_time,
            'state': 'OPEN' if rate_limiter.circuit_open else 'CLOSED'
        }