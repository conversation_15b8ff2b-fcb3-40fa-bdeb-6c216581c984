#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较不同比赛中相同博彩公司的记录ID
"""

import time
import requests
import re

def get_match_data(match_id):
    """获取指定比赛的赔率数据"""
    
    print(f"\n=== 获取比赛 {match_id} 的数据 ===")
    
    # 生成时间戳参数
    timestamp_part = str(int(time.time() * 1000))
    if len(timestamp_part) > 13:
        timestamp_part = timestamp_part[-13:]
    else:
        timestamp_part = timestamp_part.ljust(13, '0')
    timestamp = "007" + timestamp_part
    
    # 构建URL
    url = f"https://1x2d.titan007.com/{match_id}.js?r={timestamp}"
    print(f"请求URL: {url}")
    
    # 构建请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Referer': f'https://op1.titan007.com/oddslist/{match_id}.htm',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        
        if response.status_code == 200:
            content = response.text
            
            # 提取game数组内容
            game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', content, re.DOTALL)
            if game_match:
                game_data = game_match.group(1)
                
                # 提取所有记录
                company_records = re.findall(r'"([^"]*)"', game_data)
                
                # 解析记录
                companies_data = {}
                for record in company_records:
                    fields = record.split('|')
                    if len(fields) >= 3:
                        company_id = fields[0]
                        record_id = fields[1]  # 这是我们要比较的字段
                        company_name = fields[2]
                        
                        companies_data[company_id] = {
                            'record_id': record_id,
                            'company_name': company_name,
                            'full_record': record
                        }
                
                print(f"✅ 成功获取比赛 {match_id} 的数据，找到 {len(companies_data)} 家公司")
                return companies_data
            else:
                print(f"❌ 无法提取比赛 {match_id} 的game数组")
                return None
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 请求出错: {e}")
        return None

def compare_companies(match1_data, match2_data, match1_id, match2_id):
    """比较两场比赛中相同公司的记录ID"""
    
    print(f"\n=== 比较比赛 {match1_id} 和 {match2_id} 中相同公司的记录ID ===")
    
    if not match1_data or not match2_data:
        print("❌ 数据不完整，无法比较")
        return
    
    # 找到两场比赛中都存在的公司
    common_companies = set(match1_data.keys()) & set(match2_data.keys())
    
    print(f"共同的公司数量: {len(common_companies)}")
    
    # 重点关注的公司ID (包括香港马会和其他主要公司)
    focus_companies = {
        '432': '香港马会',
        '115': '威廉希尔', 
        '90': '易胜博',
        '80': '澳门',
        '255': 'BWIN',
        '281': 'Bet365'
    }
    
    print(f"\n=== 重点公司对比 ===")
    
    for company_id, company_display_name in focus_companies.items():
        if company_id in common_companies:
            record1 = match1_data[company_id]
            record2 = match2_data[company_id]
            
            print(f"\n🔍 {company_display_name} (ID: {company_id}):")
            print(f"  比赛 {match1_id}: 记录ID = {record1['record_id']}")
            print(f"  比赛 {match2_id}: 记录ID = {record2['record_id']}")
            
            if record1['record_id'] == record2['record_id']:
                print(f"  ✅ 记录ID相同")
            else:
                print(f"  ❌ 记录ID不同")
                
            # 显示公司名称是否一致
            if record1['company_name'] == record2['company_name']:
                print(f"  ✅ 公司名称一致: {record1['company_name']}")
            else:
                print(f"  ⚠️  公司名称不一致: '{record1['company_name']}' vs '{record2['company_name']}'")
        else:
            print(f"\n❌ {company_display_name} (ID: {company_id}) 在两场比赛中都不存在")
    
    print(f"\n=== 所有共同公司对比 ===")
    
    same_record_id_count = 0
    different_record_id_count = 0
    
    for company_id in sorted(common_companies):
        record1 = match1_data[company_id]
        record2 = match2_data[company_id]
        
        if record1['record_id'] == record2['record_id']:
            same_record_id_count += 1
            status = "✅相同"
        else:
            different_record_id_count += 1
            status = "❌不同"
            
        print(f"公司ID {company_id:4s} ({record1['company_name']:15s}): {record1['record_id']} vs {record2['record_id']} {status}")
    
    print(f"\n=== 统计结果 ===")
    print(f"共同公司总数: {len(common_companies)}")
    print(f"记录ID相同的公司: {same_record_id_count}")
    print(f"记录ID不同的公司: {different_record_id_count}")
    
    if same_record_id_count > 0 and different_record_id_count == 0:
        print("🎯 结论: 相同公司在不同比赛中的记录ID是相同的")
    elif same_record_id_count == 0 and different_record_id_count > 0:
        print("🎯 结论: 相同公司在不同比赛中的记录ID是不同的")
    else:
        print("🎯 结论: 情况混合，需要进一步分析")

def main():
    """主函数"""
    
    print("=" * 80)
    print("🎯 比较不同比赛中相同博彩公司的记录ID")
    print("=" * 80)
    
    # 测试的两个比赛ID
    match1_id = "2702192"
    match2_id = "2703025"
    
    # 获取两场比赛的数据
    match1_data = get_match_data(match1_id)
    match2_data = get_match_data(match2_id)
    
    # 比较数据
    compare_companies(match1_data, match2_data, match1_id, match2_id)
    
    # 保存详细数据到文件
    if match1_data and match2_data:
        with open(f"match_comparison_{match1_id}_vs_{match2_id}.txt", "w", encoding="utf-8") as f:
            f.write(f"比赛对比: {match1_id} vs {match2_id}\n\n")
            
            f.write(f"=== 比赛 {match1_id} 数据 ===\n")
            for company_id, data in sorted(match1_data.items()):
                f.write(f"ID: {company_id}, 记录ID: {data['record_id']}, 名称: {data['company_name']}\n")
                
            f.write(f"\n=== 比赛 {match2_id} 数据 ===\n")
            for company_id, data in sorted(match2_data.items()):
                f.write(f"ID: {company_id}, 记录ID: {data['record_id']}, 名称: {data['company_name']}\n")
        
        print(f"\n✅ 详细对比数据已保存到: match_comparison_{match1_id}_vs_{match2_id}.txt")

if __name__ == "__main__":
    main() 