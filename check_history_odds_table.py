"""
检查history_odds表结构和数据
"""

import sys
import os
import sqlite3
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from football_analysis_system.config import DB_MATCHES
from football_analysis_system.db.database_manager import DatabaseManager

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_history_odds_table():
    """检查history_odds表"""
    try:
        print(f"检查数据库: {DB_MATCHES}")
        
        # 检查数据库文件是否存在
        if not os.path.exists(DB_MATCHES):
            print(f"❌ 数据库文件不存在: {DB_MATCHES}")
            return False
        
        # 连接数据库
        db_manager = DatabaseManager(DB_MATCHES)
        
        # 获取所有表名
        tables = db_manager.get_table_names()
        print(f"数据库中的表: {tables}")
        
        # 检查history_odds表是否存在
        if 'history_odds' not in tables:
            print("❌ history_odds表不存在")
            return False
        
        print("✅ history_odds表存在")
        
        # 检查表结构
        columns = db_manager.get_column_names('history_odds')
        print(f"history_odds表的列: {columns}")
        
        # 检查数据量
        result = db_manager.execute_query("SELECT COUNT(*) FROM history_odds", fetchall=False)
        total_count = result[0] if result else 0
        print(f"history_odds表总记录数: {total_count}")
        
        if total_count == 0:
            print("⚠️ history_odds表为空")
            return False
        
        # 检查有多少个不同的比赛
        result = db_manager.execute_query("SELECT COUNT(DISTINCT match_id) FROM history_odds", fetchall=False)
        match_count = result[0] if result else 0
        print(f"有历史数据的比赛数量: {match_count}")
        
        # 检查有多少个不同的公司
        result = db_manager.execute_query("SELECT COUNT(DISTINCT company_id) FROM history_odds", fetchall=False)
        company_count = result[0] if result else 0
        print(f"有历史数据的博彩公司数量: {company_count}")
        
        # 显示一些示例数据
        print("\n示例数据:")
        sample_data = db_manager.execute_query("""
            SELECT match_id, company_id, company_name, home_odds, draw_odds, away_odds, update_time 
            FROM history_odds 
            LIMIT 5
        """)
        
        if sample_data:
            for row in sample_data:
                print(f"  比赛ID: {row[0]}, 公司ID: {row[1]}, 公司名: {row[2]}, 赔率: {row[3]}/{row[4]}/{row[5]}, 时间: {row[6]}")
        else:
            print("  没有示例数据")
        
        # 检查特定比赛ID的数据
        test_match_id = "2793308"  # 从截图中看到的比赛ID
        print(f"\n检查比赛ID {test_match_id} 的数据:")
        
        match_data = db_manager.execute_query("""
            SELECT DISTINCT company_id, company_name 
            FROM history_odds 
            WHERE match_id = ?
        """, (test_match_id,))
        
        if match_data:
            print(f"  找到 {len(match_data)} 家博彩公司:")
            for row in match_data:
                print(f"    公司ID: {row[0]}, 公司名: {row[1]}")
        else:
            print(f"  ❌ 比赛ID {test_match_id} 没有历史数据")
            
            # 查看实际存在的比赛ID
            existing_matches = db_manager.execute_query("""
                SELECT DISTINCT match_id 
                FROM history_odds 
                LIMIT 10
            """)
            
            if existing_matches:
                print("  实际存在的比赛ID示例:")
                for row in existing_matches:
                    print(f"    {row[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("检查history_odds表数据")
    print("=" * 50)
    
    # 检查history_odds表
    history_odds_ok = check_history_odds_table()
    
    print("\n" + "=" * 50)
    print("检查结果总结:")
    print(f"history_odds表: {'✅ 正常' if history_odds_ok else '❌ 有问题'}")
    
    return history_odds_ok


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)