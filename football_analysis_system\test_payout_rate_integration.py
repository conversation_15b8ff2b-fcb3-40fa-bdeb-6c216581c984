#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试带返还率的历史赔率功能
"""

import sqlite3
import logging
from scrapers.odds_scraper import OddsScraper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_local_js_content():
    """获取本地JS内容"""
    try:
        # 读取game数据
        with open("game_data.txt", "r", encoding="utf-8") as f:
            game_content = f.read().strip()
        
        # 构造完整的JS内容，包含gameDetail
        js_content = f"""
var game = Array({game_content});

var gameDetail = Array("144414641^1.75|4.2|3.4|08-03 13:58|0.93|0.91|0.86|2025;1.75|4|3.7|08-03 13:56|0.93|0.86|0.93|2025;1.7|4|3.8|08-03 13:46|0.90|0.86|0.96|2025","144418332^1.71|4|3.5|08-03 13:59|0.91|0.86|0.88|2025;1.65|4.1|3.75|08-03 13:48|0.88|0.88|0.94|2025;1.65|4.1|3.7|08-03 12:21|0.88|0.88|0.93|2025","144421767^1.7|4.33|3.6|08-03 13:59|0.90|0.93|0.91|2025;1.67|4.33|3.8|08-03 13:56|0.89|0.93|0.96|2025;1.65|4.33|3.9|08-03 12:28|0.88|0.93|0.98|2025");
"""
        return js_content
        
    except FileNotFoundError:
        print("❌ 未找到本地数据文件")
        return None

def test_payout_rate_integration():
    """测试返还率集成功能"""
    
    print("🧪 测试带返还率的历史赔率功能")
    print("=" * 60)
    
    # 1. 清空现有数据
    print("1️⃣ 清空现有历史赔率数据:")
    try:
        conn = sqlite3.connect("data/matches_and_odds.db")
        cursor = conn.cursor()
        cursor.execute("DELETE FROM history_odds WHERE match_id = '2696009';")
        conn.commit()
        conn.close()
        print("✅ 已清空测试数据")
    except Exception as e:
        print(f"❌ 清空失败: {e}")
        return
    
    # 2. 初始化爬虫
    print("\n2️⃣ 初始化爬虫:")
    try:
        scraper = OddsScraper(
            url_template="https://1x2d.titan007.com/{match_id}.js?r={timestamp}",
            headers_template={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': '*/*',
                'Referer': 'https://op1.titan007.com/oddslist/{match_id}.htm',
            },
            target_companies=['澳门', '威廉希尔', '易胜博', '香港马会', 'BWIN', '伟德'],
            db_path="data/matches_and_odds.db"
        )
        print("✅ 爬虫初始化成功")
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {e}")
        return
    
    # 3. 测试返还率解析
    print("\n3️⃣ 测试返还率解析:")
    test_match_id = "2696009"
    
    try:
        # 获取本地JS内容
        js_content = get_local_js_content()
        if not js_content:
            print("❌ 无法获取本地JS内容")
            return
        
        print(f"正在解析比赛 {test_match_id} 的历史赔率...")
        
        # 直接调用历史赔率解析方法
        target_companies_history = scraper._parse_target_companies_history(js_content, test_match_id)
        
        if target_companies_history:
            print(f"✅ 成功解析 {len(target_companies_history)} 家目标公司的历史赔率")
            
            # 显示解析结果，包括返还率
            for target_name, data in target_companies_history.items():
                print(f"\n📊 {target_name} ({data['history_count']} 条历史记录):")
                for i, record in enumerate(data['odds_history'][:3]):  # 只显示前3条
                    payout_str = f" 返还率:{record['payout_rate']:.2f}%" if record['payout_rate'] else " 返还率:未计算"
                    print(f"  {i+1}. {record['home_odds']:.2f}/{record['draw_odds']:.2f}/{record['away_odds']:.2f}{payout_str} ({record['update_time']})")
            
            # 保存到数据库
            print("\n正在保存历史赔率到数据库...")
            scraper._save_history_odds_to_db(test_match_id, target_companies_history)
            
        else:
            print("❌ 未解析到任何历史赔率数据")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. 检查保存结果
    print("\n4️⃣ 检查保存结果:")
    try:
        conn = sqlite3.connect("data/matches_and_odds.db")
        cursor = conn.cursor()
        
        # 检查是否有payout_rate字段
        cursor.execute("PRAGMA table_info(history_odds);")
        columns = cursor.fetchall()
        has_payout_rate = any(col[1] == 'payout_rate' for col in columns)
        
        if has_payout_rate:
            print("✅ history_odds表包含payout_rate字段")
        else:
            print("❌ history_odds表缺少payout_rate字段")
        
        # 查询带返还率的记录
        cursor.execute("""
            SELECT target_name, home_odds, draw_odds, away_odds, payout_rate, update_time
            FROM history_odds 
            WHERE match_id = ? AND payout_rate IS NOT NULL
            ORDER BY target_name, update_time DESC
            LIMIT 10
        """, (test_match_id,))
        
        records = cursor.fetchall()
        
        if records:
            print(f"\n✅ 找到 {len(records)} 条带返还率的记录:")
            for target_name, home_odds, draw_odds, away_odds, payout_rate, update_time in records:
                print(f"  {target_name}: {home_odds:.2f}/{draw_odds:.2f}/{away_odds:.2f} 返还率:{payout_rate:.2f}% ({update_time})")
        else:
            print("⚠️ 未找到带返还率的记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    test_payout_rate_integration() 