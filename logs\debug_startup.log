2025-05-24 11:00:07,555 - root - INFO - debug_start.py:321 - 开始系统诊断...
2025-05-24 11:00:07,555 - root - INFO - debug_start.py:337 - 
--- 检查: 依赖模块 ---
2025-05-24 11:00:07,555 - root - INFO - debug_start.py:31 - === 检查依赖模块 ===
2025-05-24 11:00:07,555 - root - INFO - debug_start.py:47 - ✓ tkinter: 已安装
2025-05-24 11:00:07,558 - root - INFO - debug_start.py:47 - ✓ sqlite3: 已安装
2025-05-24 11:00:07,846 - root - INFO - debug_start.py:47 - ✓ pandas: 已安装
2025-05-24 11:00:07,846 - root - INFO - debug_start.py:47 - ✓ numpy: 已安装
2025-05-24 11:00:07,953 - root - INFO - debug_start.py:47 - ✓ requests: 已安装
2025-05-24 11:00:08,002 - root - INFO - debug_start.py:44 - ✓ beautifulsoup4 (bs4): 已安装
2025-05-24 11:00:08,002 - root - INFO - debug_start.py:47 - ✓ lxml: 已安装
2025-05-24 11:00:08,242 - root - INFO - debug_start.py:47 - ✓ openpyxl: 已安装
2025-05-24 11:00:08,243 - root - INFO - debug_start.py:56 - 所有依赖模块检查通过
2025-05-24 11:00:08,243 - root - INFO - debug_start.py:337 - 
--- 检查: 配置文件 ---
2025-05-24 11:00:08,243 - root - INFO - debug_start.py:61 - === 检查配置文件 ===
2025-05-24 11:00:08,243 - root - INFO - debug_start.py:68 - ✓ config.py 导入成功
2025-05-24 11:00:08,243 - root - INFO - debug_start.py:69 -   APP_TITLE: 足球比赛分析系统 Plus
2025-05-24 11:00:08,244 - root - INFO - debug_start.py:70 -   DATA_DIR: I:\football_analysis_system 0428\football_analysis_system\football_analysis_system\data
2025-05-24 11:00:08,244 - root - INFO - debug_start.py:71 -   DB_MATCHES: I:\football_analysis_system 0428\football_analysis_system\football_analysis_system\data\matches_and_odds.db
2025-05-24 11:00:08,244 - root - INFO - debug_start.py:337 - 
--- 检查: 目录结构 ---
2025-05-24 11:00:08,244 - root - INFO - debug_start.py:80 - === 检查目录结构 ===
2025-05-24 11:00:08,244 - root - INFO - debug_start.py:88 - 当前工作目录: I:\football_analysis_system 0428\football_analysis_system\football_analysis_system
2025-05-24 11:00:08,244 - root - INFO - debug_start.py:94 - ✓ data/: 存在
2025-05-24 11:00:08,244 - root - INFO - debug_start.py:94 - ✓ logs/: 存在
2025-05-24 11:00:08,245 - root - INFO - debug_start.py:94 - ✓ temp/: 存在
2025-05-24 11:00:08,245 - root - INFO - debug_start.py:94 - ✓ ui/: 存在
2025-05-24 11:00:08,245 - root - INFO - debug_start.py:94 - ✓ models/: 存在
2025-05-24 11:00:08,246 - root - INFO - debug_start.py:94 - ✓ db/: 存在
2025-05-24 11:00:08,246 - root - INFO - debug_start.py:94 - ✓ analysis/: 存在
2025-05-24 11:00:08,246 - root - INFO - debug_start.py:94 - ✓ scrapers/: 存在
2025-05-24 11:00:08,246 - root - INFO - debug_start.py:94 - ✓ utils/: 存在
2025-05-24 11:00:08,246 - root - INFO - debug_start.py:337 - 
--- 检查: 数据库文件 ---
2025-05-24 11:00:08,246 - root - INFO - debug_start.py:113 - === 检查数据库文件 ===
2025-05-24 11:00:08,246 - root - INFO - debug_start.py:127 - ✓ matches 数据库: I:\football_analysis_system 0428\football_analysis_system\football_analysis_system\data\matches_and_odds.db (大小: 151552 bytes)
2025-05-24 11:00:08,247 - root - INFO - debug_start.py:127 - ✓ guangyishili 数据库: I:\football_analysis_system 0428\football_analysis_system\football_analysis_system\data\guangyishili.db (大小: 1478656 bytes)
2025-05-24 11:00:08,247 - root - WARNING - debug_start.py:129 - ✗ william_hill 数据库不存在: I:\football_analysis_system 0428\football_analysis_system\football_analysis_system\data\WilliamHillTeam.db
2025-05-24 11:00:08,247 - root - INFO - debug_start.py:337 - 
--- 检查: Tkinter ---
2025-05-24 11:00:08,247 - root - INFO - debug_start.py:138 - === 测试Tkinter ===
2025-05-24 11:00:08,296 - root - INFO - debug_start.py:154 - ✓ Tkinter 基本功能正常
2025-05-24 11:00:08,298 - root - INFO - debug_start.py:337 - 
--- 检查: 应用模块 ---
2025-05-24 11:00:08,299 - root - INFO - debug_start.py:166 - === 测试应用模块导入 ===
2025-05-24 11:00:08,299 - root - INFO - debug_start.py:170 - 导入 models...
2025-05-24 11:00:08,300 - root - INFO - debug_start.py:174 - ✓ models 导入成功
2025-05-24 11:00:08,301 - root - INFO - debug_start.py:176 - 导入 db...
2025-05-24 11:00:08,302 - root - INFO - debug_start.py:179 - ✓ db 导入成功
2025-05-24 11:00:08,303 - root - INFO - debug_start.py:181 - 导入 analysis...
2025-05-24 11:00:08,304 - root - INFO - debug_start.py:184 - ✓ analysis 导入成功
2025-05-24 11:00:08,304 - root - INFO - debug_start.py:186 - 导入 scrapers...
2025-05-24 11:00:08,316 - root - INFO - debug_start.py:189 - ✓ scrapers 导入成功
2025-05-24 11:00:08,316 - root - INFO - debug_start.py:191 - 导入 ui...
2025-05-24 11:00:08,371 - matplotlib - DEBUG - __init__.py:350 - matplotlib data path: C:\Python312\Lib\site-packages\matplotlib\mpl-data
2025-05-24 11:00:08,375 - matplotlib - DEBUG - __init__.py:350 - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-05-24 11:00:08,376 - matplotlib - DEBUG - __init__.py:1511 - interactive is False
2025-05-24 11:00:08,376 - matplotlib - DEBUG - __init__.py:1512 - platform is win32
2025-05-24 11:00:08,413 - matplotlib - DEBUG - __init__.py:350 - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-05-24 11:00:08,414 - matplotlib.font_manager - DEBUG - font_manager.py:1574 - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v330.json
2025-05-24 11:00:08,912 - root - INFO - debug_start.py:193 - ✓ ui.app 导入成功
2025-05-24 11:00:08,912 - root - INFO - debug_start.py:345 - 
==================================================
2025-05-24 11:00:08,912 - root - INFO - debug_start.py:346 - 系统诊断完成
2025-05-24 11:00:08,913 - root - INFO - debug_start.py:347 - ==================================================
2025-05-24 11:00:08,913 - root - INFO - debug_start.py:353 - 所有检查通过！
2025-05-24 11:00:08,913 - root - INFO - debug_start.py:204 - === 创建简单GUI测试 ===
2025-05-24 11:00:09,010 - root - INFO - debug_start.py:304 - ✓ 调试界面创建成功
2025-05-24 11:00:09,011 - root - INFO - debug_start.py:358 - 启动调试界面...
