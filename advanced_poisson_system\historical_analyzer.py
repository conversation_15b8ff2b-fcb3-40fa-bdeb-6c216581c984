"""
历史分析器 - 基于历史比赛数据的球队攻防强度分析
复用现有poisson_module的成熟算法
"""

import numpy as np
from scipy.stats import poisson
from typing import Dict, List, Optional, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

class HistoricalAnalyzer:
    """历史分析器 - 提供基于历史数据的λ参数估算"""
    
    def __init__(self, data_manager):
        """
        初始化历史分析器
        
        Args:
            data_manager: 数据管理器实例
        """
        self.data_manager = data_manager
        self.default_lambda_home = 1.5
        self.default_lambda_away = 1.2
        
    def calculate_historical_lambdas(self, home_team, away_team, recent_matches=10):
        """
        基于历史数据计算λ参数
        
        Args:
            home_team: 主队名称
            away_team: 客队名称
            recent_matches: 最近比赛数量
            
        Returns:
            dict: 历史分析结果
        """
        try:
            # 获取历史统计数据
            historical_stats = self.data_manager.calculate_team_historical_stats(
                home_team, away_team, recent_matches
            )
            
            if not historical_stats['team_stats']:
                # 没有历史数据，返回默认值
                return self._get_default_result(home_team, away_team)
            
            # 提取关键数据
            team_stats = historical_stats['team_stats']
            league_avg_home = historical_stats['league_avg_home']
            league_avg_away = historical_stats['league_avg_away']
            
            # 计算预期进球数
            lambda_home, lambda_away = self._calculate_expected_goals(
                home_team, away_team, team_stats, league_avg_home, league_avg_away
            )
            
            # 计算数据质量评分
            quality_score = self._assess_data_quality(historical_stats, team_stats)
            
            return {
                'success': True,
                'lambda_estimates': {
                    'home': lambda_home,
                    'away': lambda_away,
                    'total': lambda_home + lambda_away
                },
                'team_analysis': {
                    'home_team': self._analyze_team_strength(home_team, team_stats),
                    'away_team': self._analyze_team_strength(away_team, team_stats)
                },
                'league_context': {
                    'avg_home_goals': league_avg_home,
                    'avg_away_goals': league_avg_away,
                    'total_avg_goals': league_avg_home + league_avg_away
                },
                'data_quality': quality_score,
                'confidence_level': self._calculate_confidence(quality_score)
            }
            
        except Exception as e:
            print(f"历史分析失败: {e}")
            return self._get_default_result(home_team, away_team)
    
    def _calculate_expected_goals(self, home_team, away_team, team_stats, league_avg_home, league_avg_away):
        """
        计算预期进球数（使用现有算法）
        """
        home_stats = team_stats.get(home_team, {})
        away_stats = team_stats.get(away_team, {})
        
        # 如果没有统计数据，使用默认值
        if not home_stats or not away_stats:
            return self.default_lambda_home, self.default_lambda_away
        
        # 提取攻防强度（来自现有poisson_module算法）
        home_attack = home_stats.get('home_attack', 1.0)
        home_defense = home_stats.get('home_defense', 1.0)
        away_attack = away_stats.get('away_attack', 1.0)
        away_defense = away_stats.get('away_defense', 1.0)
        
        # 计算预期进球（现有算法）
        lambda_home = home_attack * away_defense * league_avg_home
        lambda_away = away_attack * home_defense * league_avg_away
        
        # 确保结果在合理范围内
        lambda_home = max(0.1, min(5.0, lambda_home))
        lambda_away = max(0.1, min(5.0, lambda_away))
        
        return lambda_home, lambda_away
    
    def _analyze_team_strength(self, team_name, team_stats):
        """
        分析球队强度
        """
        stats = team_stats.get(team_name, {})
        
        if not stats:
            return {
                'team_name': team_name,
                'attack_strength': 1.0,
                'defense_strength': 1.0,
                'form_assessment': 'unknown',
                'data_available': False
            }
        
        # 攻防强度
        home_attack = stats.get('home_attack', 1.0)
        away_attack = stats.get('away_attack', 1.0)
        home_defense = stats.get('home_defense', 1.0)
        away_defense = stats.get('away_defense', 1.0)
        
        # 综合攻防强度
        avg_attack = (home_attack + away_attack) / 2
        avg_defense = (home_defense + away_defense) / 2
        
        # 状态评估
        form_assessment = self._assess_team_form(stats)
        
        return {
            'team_name': team_name,
            'attack_strength': avg_attack,
            'defense_strength': avg_defense,
            'home_attack': home_attack,
            'home_defense': home_defense,
            'away_attack': away_attack,
            'away_defense': away_defense,
            'form_assessment': form_assessment,
            'recent_matches': stats.get('total_games', 0),
            'data_available': True
        }
    
    def _assess_team_form(self, stats):
        """
        评估球队状态
        """
        home_goals = stats.get('home_goals', 0)
        away_goals = stats.get('away_goals', 0)
        home_conceded = stats.get('home_conceded', 0)
        away_conceded = stats.get('away_conceded', 0)
        home_games = stats.get('home_games', 1)
        away_games = stats.get('away_games', 1)
        
        # 计算场均进球和失球
        avg_goals_scored = (home_goals + away_goals) / (home_games + away_games)
        avg_goals_conceded = (home_conceded + away_conceded) / (home_games + away_games)
        
        # 简单的状态评估
        if avg_goals_scored > 2.0 and avg_goals_conceded < 1.0:
            return 'excellent'
        elif avg_goals_scored > 1.5 and avg_goals_conceded < 1.5:
            return 'good'
        elif avg_goals_scored > 1.0 and avg_goals_conceded < 2.0:
            return 'average'
        elif avg_goals_scored < 1.0 or avg_goals_conceded > 2.5:
            return 'poor'
        else:
            return 'below_average'
    
    def _assess_data_quality(self, historical_stats, team_stats):
        """
        评估数据质量
        """
        total_matches = historical_stats['data_quality']['total_matches']
        home_matches = historical_stats['data_quality']['home_team_matches']
        away_matches = historical_stats['data_quality']['away_team_matches']
        
        quality_factors = {
            'total_matches_score': min(total_matches / 100, 1.0),  # 100场以上为满分
            'home_team_data_score': min(home_matches / 10, 1.0),   # 10场以上为满分
            'away_team_data_score': min(away_matches / 10, 1.0),   # 10场以上为满分
            'data_completeness': 1.0 if team_stats else 0.0
        }
        
        # 加权平均
        weights = [0.3, 0.3, 0.3, 0.1]
        overall_score = sum(score * weight for score, weight in zip(quality_factors.values(), weights))
        
        return {
            'overall_score': overall_score,
            'factors': quality_factors,
            'total_matches': total_matches,
            'home_team_matches': home_matches,
            'away_team_matches': away_matches
        }
    
    def _calculate_confidence(self, quality_score):
        """
        计算置信度等级
        """
        score = quality_score['overall_score']
        
        if score >= 0.8:
            return 'high'
        elif score >= 0.6:
            return 'medium'
        elif score >= 0.4:
            return 'low'
        else:
            return 'very_low'
    
    def _get_default_result(self, home_team, away_team):
        """
        获取默认结果（当没有历史数据时）
        """
        return {
            'success': False,
            'lambda_estimates': {
                'home': self.default_lambda_home,
                'away': self.default_lambda_away,
                'total': self.default_lambda_home + self.default_lambda_away
            },
            'team_analysis': {
                'home_team': {
                    'team_name': home_team,
                    'attack_strength': 1.0,
                    'defense_strength': 1.0,
                    'form_assessment': 'unknown',
                    'data_available': False
                },
                'away_team': {
                    'team_name': away_team,
                    'attack_strength': 1.0,
                    'defense_strength': 1.0,
                    'form_assessment': 'unknown',
                    'data_available': False
                }
            },
            'league_context': {
                'avg_home_goals': 1.5,
                'avg_away_goals': 1.2,
                'total_avg_goals': 2.7
            },
            'data_quality': {
                'overall_score': 0.0,
                'factors': {},
                'total_matches': 0,
                'home_team_matches': 0,
                'away_team_matches': 0
            },
            'confidence_level': 'very_low'
        }

if __name__ == "__main__":
    # 测试历史分析器
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    
    from data_manager import DataManager
    
    print("=== 历史分析器测试 ===")
    
    # 初始化
    dm = DataManager()
    analyzer = HistoricalAnalyzer(dm)
    
    # 测试比赛
    home_team = "哥德堡盖斯"
    away_team = "天狼星"
    
    # 进行历史分析
    result = analyzer.calculate_historical_lambdas(home_team, away_team)
    
    print(f"分析结果: {result['success']}")
    print(f"λ估算: {result['lambda_estimates']}")
    print(f"置信度: {result['confidence_level']}")
    print(f"数据质量: {result['data_quality']['overall_score']:.3f}") 