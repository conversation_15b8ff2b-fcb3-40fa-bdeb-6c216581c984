"""
路径解析器模块
提供统一的数据库路径解析功能，解决硬编码路径问题
"""

import os
from pathlib import Path
from typing import Optional
from .config_manager import config_manager


class PathResolver:
    """统一路径解析器"""
    
    @staticmethod
    def get_database_path(db_name: str = "matches_and_odds.db") -> str:
        """
        获取标准化的数据库路径
        
        Args:
            db_name: 数据库文件名
            
        Returns:
            str: 标准化的数据库绝对路径
        """
        # 从配置管理器获取数据目录
        data_dir = config_manager.get('paths.data_dir')
        
        # 确保数据目录存在
        Path(data_dir).mkdir(parents=True, exist_ok=True)
        
        # 返回完整的数据库路径
        return str(Path(data_dir) / db_name)
    
    @staticmethod
    def resolve_relative_path(relative_path: str) -> str:
        """
        将相对路径解析为标准路径
        
        Args:
            relative_path: 相对路径，如 "data/matches_and_odds.db"
            
        Returns:
            str: 解析后的标准绝对路径
        """
        # 检查是否为外层data目录的相对路径
        if relative_path.startswith("data/") or relative_path.startswith("data\\"):
            # 提取数据库文件名
            db_name = Path(relative_path).name
            # 返回内层标准路径
            return PathResolver.get_database_path(db_name)
        
        # 如果已经是绝对路径，检查是否需要转换
        if os.path.isabs(relative_path):
            path_obj = Path(relative_path)
            # 如果路径指向外层data目录，转换为内层路径
            if path_obj.parent.name == "data" and path_obj.parent.parent.name != "football_analysis_system":
                db_name = path_obj.name
                return PathResolver.get_database_path(db_name)
            return relative_path
        
        # 其他情况，假设是数据库文件名
        return PathResolver.get_database_path(relative_path)
    
    @staticmethod
    def get_project_root() -> str:
        """
        获取项目根目录路径
        
        Returns:
            str: 项目根目录绝对路径
        """
        return config_manager.get('paths.project_root')
    
    @staticmethod
    def get_data_dir() -> str:
        """
        获取数据目录路径
        
        Returns:
            str: 数据目录绝对路径
        """
        return config_manager.get('paths.data_dir')
    
    @staticmethod
    def ensure_directory_exists(path: str) -> bool:
        """
        确保目录存在
        
        Args:
            path: 目录路径
            
        Returns:
            bool: 是否成功创建或目录已存在
        """
        try:
            Path(path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception:
            return False
    
    @staticmethod
    def is_external_data_path(path: str) -> bool:
        """
        检查路径是否指向外层data目录
        
        Args:
            path: 要检查的路径
            
        Returns:
            bool: 是否为外层data目录路径
        """
        path_obj = Path(path).resolve()
        
        # 检查是否为外层data目录模式
        if path_obj.parent.name == "data":
            # 检查父目录的父目录是否不是football_analysis_system
            grandparent = path_obj.parent.parent
            if grandparent.name != "football_analysis_system":
                return True
        
        return False
    
    @staticmethod
    def get_legacy_database_path(db_name: str = "matches_and_odds.db") -> Optional[str]:
        """
        获取外层数据库路径（如果存在）
        
        Args:
            db_name: 数据库文件名
            
        Returns:
            Optional[str]: 外层数据库路径，如果不存在则返回None
        """
        # 获取项目根目录
        project_root = Path(config_manager.get('paths.project_root'))
        
        # 构建外层data目录路径
        external_data_dir = project_root.parent / "data"
        external_db_path = external_data_dir / db_name
        
        # 检查文件是否存在
        if external_db_path.exists():
            return str(external_db_path)
        
        return None