#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的增强版爬虫测试
"""

import time
import requests
import re
import sqlite3
from datetime import datetime

def create_record_id_table(db_path="data/matches_and_odds.db"):
    """创建记录ID存储表"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建记录ID表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS odds_record_ids (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT NOT NULL,
                company_id TEXT NOT NULL,
                company_name TEXT NOT NULL,
                record_id TEXT NOT NULL,
                update_timestamp TEXT NOT NULL,
                scrape_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(match_id, company_id, record_id)
            )
        ''')
        
        # 创建索引
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_odds_record_ids_match_company 
            ON odds_record_ids(match_id, company_id)
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ 记录ID表创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 创建记录ID表失败: {e}")
        if 'conn' in locals():
            conn.close()
        return False

def get_match_data_with_record_ids(match_id):
    """获取比赛数据并解析记录ID"""
    
    print(f"=== 获取比赛 {match_id} 的数据并解析记录ID ===")
    
    # 生成时间戳
    timestamp_part = str(int(time.time() * 1000))
    if len(timestamp_part) > 13:
        timestamp_part = timestamp_part[-13:]
    else:
        timestamp_part = timestamp_part.ljust(13, '0')
    timestamp = "007" + timestamp_part
    
    # 构建URL
    url = f"https://1x2d.titan007.com/{match_id}.js?r={timestamp}"
    
    # 构建请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Referer': f'https://op1.titan007.com/oddslist/{match_id}.htm',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        
        if response.status_code == 200:
            content = response.text
            
            # 提取game数组
            game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', content, re.DOTALL)
            if game_match:
                game_data = game_match.group(1)
                company_records = re.findall(r'"([^"]*)"', game_data)
                
                # 目标公司（简化版）
                target_companies = ['威廉希尔', '易胜博', '香港马会', '澳门', 'BWIN']
                
                record_ids_data = []
                
                for record in company_records:
                    fields = record.split('|')
                    if len(fields) >= 21:
                        company_id = fields[0]
                        record_id = fields[1]
                        company_name = fields[2].strip()
                        update_timestamp = fields[20].strip() if len(fields) > 20 else ""
                        
                        # 简单匹配主要公司
                        matched = False
                        if company_id == '115' or 'william hill' in company_name.lower():
                            matched_name = '威廉希尔'
                            matched = True
                        elif company_id == '90' or 'easybets' in company_name.lower():
                            matched_name = '易胜博'
                            matched = True
                        elif company_id == '432' or 'hk jockey club' in company_name.lower():
                            matched_name = '香港马会'
                            matched = True
                        elif company_id == '80' or 'macauslot' in company_name.lower():
                            matched_name = '澳门'
                            matched = True
                        elif company_id == '255' or 'bwin' in company_name.lower():
                            matched_name = 'BWIN'
                            matched = True
                        
                        if matched:
                            record_ids_data.append({
                                'company_id': company_id,
                                'company_name': company_name,
                                'matched_name': matched_name,
                                'record_id': record_id,
                                'update_timestamp': update_timestamp
                            })
                
                print(f"✅ 找到 {len(record_ids_data)} 家目标公司的记录ID")
                return record_ids_data
            else:
                print("❌ 未找到game数组")
                return []
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 请求出错: {e}")
        return []

def save_record_ids_to_db(match_id, record_ids_data, db_path="data/matches_and_odds.db"):
    """保存记录ID到数据库"""
    
    if not record_ids_data:
        print("没有数据需要保存")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        saved_count = 0
        
        for data in record_ids_data:
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO odds_record_ids 
                    (match_id, company_id, company_name, record_id, update_timestamp, scrape_time)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    str(match_id),
                    data['company_id'],
                    data['company_name'],
                    data['record_id'],
                    data['update_timestamp'],
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ))
                
                saved_count += 1
                print(f"保存: {data['matched_name']} (ID: {data['company_id']}) -> 记录ID: {data['record_id']}")
                
            except Exception as e:
                print(f"保存失败: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        print(f"✅ 成功保存 {saved_count} 条记录ID")
        return True
        
    except Exception as e:
        print(f"❌ 保存到数据库失败: {e}")
        if 'conn' in locals():
            conn.close()
        return False

def query_record_ids_from_db(match_id, db_path="data/matches_and_odds.db"):
    """从数据库查询记录ID"""
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT company_id, company_name, record_id, update_timestamp, scrape_time
            FROM odds_record_ids 
            WHERE match_id = ?
            ORDER BY company_name
        ''', (str(match_id),))
        
        results = cursor.fetchall()
        conn.close()
        
        print(f"\n=== 数据库中比赛 {match_id} 的记录ID ===")
        for row in results:
            company_id, company_name, record_id, update_timestamp, scrape_time = row
            print(f"{company_name} (ID: {company_id}): 记录ID={record_id}, 更新时间={update_timestamp}")
        
        return results
        
    except Exception as e:
        print(f"❌ 查询数据库失败: {e}")
        if 'conn' in locals():
            conn.close()
        return []

def main():
    """主函数"""
    
    print("=" * 60)
    print("🎯 增强版赔率爬虫测试 - 记录ID功能")
    print("=" * 60)
    
    # 1. 创建记录ID表
    if not create_record_id_table():
        return
    
    # 2. 测试比赛ID
    test_match_id = "2696009"
    
    # 3. 获取数据
    record_ids_data = get_match_data_with_record_ids(test_match_id)
    
    if record_ids_data:
        print(f"\n=== 解析结果 ===")
        for data in record_ids_data:
            print(f"{data['matched_name']}: 公司ID={data['company_id']}, 记录ID={data['record_id']}")
        
        # 4. 保存到数据库
        print(f"\n=== 保存到数据库 ===")
        save_record_ids_to_db(test_match_id, record_ids_data)
        
        # 5. 从数据库查询验证
        query_record_ids_from_db(test_match_id)
        
    else:
        print("❌ 未获取到数据")

if __name__ == "__main__":
    main() 