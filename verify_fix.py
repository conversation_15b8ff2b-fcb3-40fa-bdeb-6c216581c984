#!/usr/bin/env python3
"""
验证路径问题修复
测试原始错误是否已经解决
"""

import sys
import os
from pathlib import Path

def main():
    print("=" * 60)
    print("验证路径问题修复")
    print("=" * 60)
    
    try:
        # 添加路径
        sys.path.insert(0, str(Path("football_analysis_system")))
        
        print("1. 测试配置管理器...")
        from core.config_manager import config_manager
        print(f"   ✓ 配置管理器加载成功")
        print(f"   ✓ 项目根目录: {config_manager.project_root}")
        
        print("\n2. 测试日志管理器...")
        from core.logger_manager import logger_manager
        print(f"   ✓ 日志管理器加载成功")
        
        print("\n3. 测试日志文件创建...")
        # 这是原始错误发生的地方
        logger = logger_manager.get_logger('test_fix')
        logger.info("测试日志文件创建")
        print(f"   ✓ 日志文件创建成功")
        
        print("\n4. 验证路径配置...")
        paths = {
            'logs': config_manager.get('paths.logs_dir'),
            'data': config_manager.get('paths.data_dir'),
            'temp': config_manager.get('paths.temp_dir'),
            'backup': config_manager.get('paths.backup_dir')
        }
        
        for name, path in paths.items():
            resolved = config_manager.resolve_path(path)
            print(f"   {name}: {path} -> {resolved}")
            
            # 确保目录存在
            resolved.mkdir(parents=True, exist_ok=True)
            if resolved.exists():
                print(f"      ✓ 目录创建成功")
            else:
                print(f"      ✗ 目录创建失败")
                return False
        
        print("\n" + "=" * 60)
        print("🎉 修复验证成功！")
        print("=" * 60)
        print("✅ 问题已解决:")
        print("  - 硬编码的绝对路径已改为相对路径")
        print("  - 路径解析功能正常工作")
        print("  - 日志目录创建不再出错")
        print("  - 程序可以在任何环境下运行")
        print("\n💡 现在您可以:")
        print("  - 将项目复制到任何位置")
        print("  - 在不同的电脑上运行")
        print("  - 不再依赖特定的盘符")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🚀 您现在可以正常运行应用程序了！")
    else:
        print("\n⚠️  还有一些问题需要解决。")
    
    sys.exit(0 if success else 1)
