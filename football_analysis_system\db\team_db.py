import re
import logging
import sqlite3
import os
from .database_manager import DatabaseManager
from football_analysis_system.models.team import Team
from football_analysis_system.config import DATA_DIR  # 导入配置的数据目录

class TeamDatabase(DatabaseManager):
    """球队数据库操作类"""

    def __init__(self, db_path, preferred_company_id="115"):
        """
        初始化球队数据库管理器

        Args:
            db_path: 数据库文件路径
            preferred_company_id: 首选博彩公司ID，默认为"115"(威廉希尔)
        """
        super().__init__(db_path)
        self.teams = {}  # 缓存加载的球队数据
        self.preferred_company_id = preferred_company_id  # 首选博彩公司ID
        self.current_company_name = None  # 当前使用的博彩公司名称
        
        # 自动加载球队数据
        self.load_team_data()

    def _verify_db_path(self):
        """验证数据库路径，尝试使用备用路径"""
        logging.info(f"验证广义实力数据库路径: {self.db_path}")

        if not os.path.exists(self.db_path):
            logging.warning(f"未找到数据库: {self.db_path}")

            # 尝试在配置的数据目录中查找各种可能的文件名
            possible_names = ['guangyishili.db', 'WilliamHillTeam.db', 'team_database.db']

            # 确保数据目录存在
            if not os.path.exists(DATA_DIR):
                logging.warning(f"数据目录不存在: {DATA_DIR}")
                os.makedirs(DATA_DIR, exist_ok=True)
                logging.info(f"已创建数据目录: {DATA_DIR}")

            # 在配置的数据目录中查找
            logging.info(f"在配置的数据目录中查找: {DATA_DIR}")
            for name in possible_names:
                alt_path = os.path.join(DATA_DIR, name)
                if os.path.exists(alt_path):
                    self.db_path = alt_path
                    logging.info(f"使用配置目录中的数据库: {self.db_path}")
                    return

            # 最后尝试相对路径
            logging.info(f"尝试使用相对路径查找数据库")
            for name in possible_names:
                if os.path.exists(name):
                    self.db_path = name
                    logging.info(f"使用相对路径数据库: {self.db_path}")
                    return

            logging.warning(f"未能找到任何可用的球队数据库")
        else:
            logging.info(f"使用球队数据库: {self.db_path}")

    def load_team_data(self):
        """
        加载球队数据，优先从team_power_ratings表加载

        Returns:
            dict: 球队名称到球队数据的映射
        """
        try:
            logging.info(f"开始从数据库加载球队数据: {self.db_path}")

            # 获取表名
            tables = self.get_table_names()
            if not tables:
                logging.warning(f"在数据库中未找到任何表")
                return {}

            logging.info(f"数据库中的表: {', '.join(tables)}")

            # 首先尝试从team_power_ratings表加载
            if "team_power_ratings" in tables:
                logging.info("找到team_power_ratings表，从该表加载数据")
                result = self._load_from_power_ratings()
                logging.info(f"从team_power_ratings表加载了 {len(result)} 支球队")
                return result

            # 如果没有新表，回退到原来的加载方式
            logging.info("未找到team_power_ratings表，使用原始加载方式")

            # 查找包含"威廉"和"广实"或"实力"的表
            target_table = None
            for table in tables:
                if "威廉" in table and ("广实" in table or "实力" in table):
                    target_table = table
                    logging.info(f"找到匹配的表名: {target_table}")
                    break

            # 如果没找到特定表，使用第一个表
            if not target_table:
                target_table = tables[0]
                logging.info(f"未找到预期的球队表名，使用第一个表: {target_table}")

            # 获取表结构
            columns = self.get_column_names(target_table)
            if not columns:
                logging.warning(f"表 {target_table} 没有列")
                return {}

            logging.info(f"表 {target_table} 的列: {', '.join(columns)}")

            # 识别关键列名
            team_column = next((col for col in columns if "球队" in col or "队名" in col), None)

            # 更灵活地匹配广义实力列
            power_column = None
            for col in columns:
                if ("广义" in col and "实力" in col) or ("广" in col and "实" in col) or "实力" in col:
                    power_column = col
                    break

            rating_column = next((col for col in columns if "评分" in col), None)

            logging.info(f"识别的列: 球队={team_column}, 广义实力={power_column}, 评分={rating_column}")

            # 没有完全识别列结构时，尝试使用位置推断
            if not team_column or not power_column or not rating_column:
                logging.warning(f"未能完全识别表结构: 球队列={team_column}, 广义实力列={power_column}, 评分列={rating_column}")

                if len(columns) >= 3:
                    team_column = team_column or columns[1]  # 假设第2列是球队名
                    power_column = power_column or columns[2]  # 假设第3列是广义实力
                    rating_column = rating_column or (columns[3] if len(columns) > 3 else None)  # 假设第4列是评分
                    logging.info(f"使用推断的列: 球队={team_column}, 广义实力={power_column}, 评分={rating_column}")

            # 查询球队数据
            query = f"SELECT {team_column}, {power_column}, {rating_column} FROM {target_table}"
            team_data_raw = self.execute_query(query)

            if not team_data_raw:
                logging.warning(f"从表 {target_table} 中未查询到数据")
                return {}

            logging.info(f"从表 {target_table} 中查询到 {len(team_data_raw)} 条数据")

            # 处理数据
            teams = {}
            for row in team_data_raw:
                team_name, power, rating = row

                # 处理广义实力
                power_value = str(power).strip() if power is not None else None

                # 处理评分
                rating_value = None
                if rating is not None:
                    try:
                        if isinstance(rating, (int, float)):
                            rating_value = float(rating)
                        elif isinstance(rating, str) and rating.strip():
                            rating_value = float(rating.replace(',', '.'))
                    except ValueError:
                        rating_value = str(rating).strip() if rating else None

                # 创建球队对象并添加到字典
                teams[team_name] = Team(
                    name=team_name,
                    power=power_value,
                    rating=rating_value
                )

            # 保存到实例变量
            self.teams = teams

            # 统计数据情况
            power_values_count = sum(1 for team in teams.values() if team.power is not None)
            rating_values_count = sum(1 for team in teams.values() if team.rating is not None)

            logging.info(f"成功加载 {len(teams)} 支球队数据，"
                         f"其中有广义实力的球队: {power_values_count}, "
                         f"有评分的球队: {rating_values_count}")

            # 输出几个样本数据供调试
            sample_count = min(5, len(teams))
            if sample_count > 0:
                sample_teams = list(teams.items())[:sample_count]
                logging.info("球队数据样本:")
                for team_name, team_obj in sample_teams:
                    logging.info(f"  {team_name}: 广义实力={team_obj.power}, 评分={team_obj.rating}")

            return teams

        except Exception as e:
            logging.error(f"加载球队数据时出错: {e}")
            import traceback
            logging.error(traceback.format_exc())
            return {}

    def _load_from_power_ratings(self):
        """
        从team_power_ratings表加载球队数据

        Returns:
            dict: 球队名称到球队数据的映射
        """
        try:
            target_table = "team_power_ratings"

            # 获取所有可用的公司ID
            company_query = f"SELECT DISTINCT company_id, company_name FROM {target_table}"
            company_data = self.execute_query(company_query)

            if not company_data:
                logging.warning(f"未找到任何博彩公司数据")
                return {}

            # 查找首选公司是否可用
            available_companies = {row[0]: row[1] for row in company_data}
            logging.info(f"可用的博彩公司: {available_companies}")

            if self.preferred_company_id not in available_companies:
                # 如果首选公司不可用，使用第一个可用的公司
                self.preferred_company_id = list(available_companies.keys())[0]
                logging.info(f"首选公司不可用，使用: {self.preferred_company_id} ({available_companies[self.preferred_company_id]})")
            else:
                logging.info(f"使用首选公司: {self.preferred_company_id} ({available_companies[self.preferred_company_id]})")

            # 保存当前使用的公司名称
            self.current_company_name = available_companies[self.preferred_company_id]

            # 加载所有球队和联赛信息 - 首先使用首选公司数据创建球队对象
            query = f"""
            SELECT team_name, power_category, power_level, league_name
            FROM {target_table}
            WHERE company_id = '{self.preferred_company_id}'
            """
            team_data_raw = self.execute_query(query)

            # 首先收集所有球队名称（来自所有公司）
            all_teams_query = f"SELECT DISTINCT team_name FROM {target_table}"
            all_teams_raw = self.execute_query(all_teams_query)
            all_team_names = [row[0] for row in all_teams_raw]
            
            # 处理首选公司的数据
            teams = {}
            preferred_teams = {}
            for row in team_data_raw:
                team_name, power_category, power_level, league_name = row
                preferred_teams[team_name] = (power_category, power_level)

            # 为所有球队创建对象（包括首选公司没有的球队）
            for team_name in all_team_names:
                if team_name in preferred_teams:
                    power_category, power_level = preferred_teams[team_name]
                    teams[team_name] = Team(
                        name=team_name,
                        power=power_category,  # 档次分类作为广义实力
                        rating=power_level     # 档距作为评分
                    )
                else:
                    # 首选公司没有的球队，创建空对象
                    teams[team_name] = Team(name=team_name)

            # 如果没有找到首选公司的数据，尝试使用其他公司的数据
            if not teams and len(available_companies) > 1:
                logging.warning(f"首选公司 {self.current_company_name} 没有数据，尝试其他公司")
                # 尝试其他公司
                for company_id in available_companies:
                    if company_id != self.preferred_company_id:
                        query = f"""
                        SELECT team_name, power_category, power_level, league_name
                        FROM {target_table}
                        WHERE company_id = '{company_id}'
                        """
                        team_data_raw = self.execute_query(query)

                        # 如果找到数据，使用此公司
                        if team_data_raw:
                            self.preferred_company_id = company_id
                            self.current_company_name = available_companies[company_id]
                            logging.info(f"切换到 {self.current_company_name} 公司的数据")

                            for row in team_data_raw:
                                team_name, power_category, power_level, league_name = row
                                teams[team_name] = Team(
                                    name=team_name,
                                    power=power_category,
                                    rating=power_level
                                )
                            break

            # 保存到实例变量
            self.teams = teams

            # 为每个球队添加其他公司的评分数据
            # 创建多公司评分映射字典
            multi_company_ratings = {}
            for company_id, company_name in available_companies.items():
                query = f"""
                SELECT team_name, power_category, power_level, league_name
                FROM {target_table}
                WHERE company_id = '{company_id}'
                """
                company_data_raw = self.execute_query(query)

                for row in company_data_raw:
                    team_name, power_category, power_level, league_name = row

                    # 如果球队还不在多公司字典中，创建一个空字典
                    if team_name not in multi_company_ratings:
                        multi_company_ratings[team_name] = {}

                    # 添加该公司的评分数据
                    multi_company_ratings[team_name][company_id] = {
                        'company_name': company_name,
                        'power': power_category,
                        'rating': power_level,
                        'league': league_name
                    }

            # 为每个球队对象添加多公司评分属性
            for team_name, team_obj in self.teams.items():
                if team_name in multi_company_ratings:
                    team_obj.multi_company_ratings = multi_company_ratings[team_name]
                else:
                    team_obj.multi_company_ratings = {}

            # 统计数据情况
            power_values_count = sum(1 for team in teams.values() if team.power is not None)
            rating_values_count = sum(1 for team in teams.values() if team.rating is not None)

            logging.info(f"成功加载 {len(teams)} 支球队数据 (来自{self.current_company_name})，"
                      f"其中有广义实力的球队: {power_values_count}, "
                      f"有评分的球队: {rating_values_count}")

            return teams

        except Exception as e:
            logging.error(f"从team_power_ratings表加载球队数据时出错: {e}")
            return {}

    def set_preferred_company(self, company_id):
        """
        设置首选博彩公司ID并重新加载数据

        Args:
            company_id: 博彩公司ID

        Returns:
            bool: 是否成功切换公司
        """
        old_id = self.preferred_company_id
        self.preferred_company_id = company_id

        # 重新加载数据
        result = self.load_team_data()

        if not result:
            # 如果加载失败，恢复原来的公司ID
            self.preferred_company_id = old_id
            self.load_team_data()
            return False

        return True

    def get_available_companies(self):
        """
        获取所有可用的博彩公司

        Returns:
            dict: 公司ID到公司名称的映射
        """
        try:
            tables = self.get_table_names()
            if "team_power_ratings" not in tables:
                return {}

            # 只显示指定的公司
            target_company_ids = ["115", "90", "432", "80", "255", "81", "474", "4", "976", "1132", "657"]

            # 确保正确的公司名称显示
            company_names = {
                "115": "威廉希尔",
                "90": "易胜博",
                "432": "香港马会",
                "80": "澳门",
                "255": "BWIN",
                "81": "伟德",
                "474": "利记",
                "4": "Nordicbet",
                "976": "18BET",
                "1132": "Coolbet",
                "657": "iddaa"
            }

            # 无论数据库中是否有数据，都返回所有目标公司
            filtered_companies = {}
            for company_id in target_company_ids:
                if company_id in company_names:
                    filtered_companies[company_id] = company_names[company_id]

            # 如果数据库中有数据，查询确认
            company_query = "SELECT DISTINCT company_id, company_name FROM team_power_ratings"
            company_data = self.execute_query(company_query)

            if company_data:
                logging.info(f"数据库中找到的公司: {[row[0] for row in company_data]}")
                # 记录哪些公司在数据库中有数据
                for row in company_data:
                    if row[0] in filtered_companies:
                        logging.info(f"公司 {row[0]} ({filtered_companies[row[0]]}) 在数据库中有数据")

            # 确保首选公司存在
            if self.preferred_company_id not in filtered_companies:
                if len(filtered_companies) > 0:
                    self.preferred_company_id = list(filtered_companies.keys())[0]
                    logging.info(f"首选公司不可用，改用: {self.preferred_company_id} ({filtered_companies[self.preferred_company_id]})")

            return filtered_companies

        except Exception as e:
            logging.error(f"获取可用博彩公司时出错: {e}")
            return {}

    def get_current_company_info(self):
        """
        获取当前使用的博彩公司信息

        Returns:
            tuple: (公司ID, 公司名称)
        """
        return (self.preferred_company_id, self.current_company_name)

    def get_team_data(self, team_name):
        """
        获取指定球队的数据，如果没有直接匹配则尝试模糊匹配

        Args:
            team_name: 球队名称

        Returns:
            Team: 球队对象，如果未找到则返回None
        """
        if not team_name:
            return Team(name=team_name)

        # 确保team_name是字符串
        team_name = str(team_name) if team_name is not None else ""

        # 直接匹配
        if team_name in self.teams:
            return self.teams[team_name]

        # 如果没有直接匹配，尝试模糊匹配
        best_match = None
        highest_ratio = 0

        for db_team_name, team in self.teams.items():
            # 确保db_team_name是字符串
            db_team_name_str = str(db_team_name) if db_team_name is not None else ""

            # 跳过空名称
            if not db_team_name_str or not team_name:
                continue

            try:
                # 简单的名称清理，移除括号内容等
                clean_db_name = re.sub(r'\s*\([^)]*\)', '', db_team_name_str).strip()
                clean_team_name = re.sub(r'\s*\([^)]*\)', '', team_name).strip()

                # 跳过清理后为空的名称
                if not clean_db_name or not clean_team_name:
                    continue

                # 首先检查是否一个名称包含另一个
                if clean_db_name in clean_team_name or clean_team_name in clean_db_name:
                    ratio = max(len(clean_db_name) / len(clean_team_name) if len(clean_team_name) > 0 else 0,
                               len(clean_team_name) / len(clean_db_name) if len(clean_db_name) > 0 else 0)

                    if ratio > highest_ratio:
                        highest_ratio = ratio
                        best_match = team
            except Exception as e:
                logging.error(f"处理球队名称时出错: {e}, db_team_name={db_team_name}, team_name={team_name}")
                continue

        # 如果找到了良好的匹配
        if best_match and highest_ratio > 0.7:  # 设置一个阈值，避免错误匹配
            logging.info(f"找到球队 '{team_name}' 的最佳匹配: '{best_match.name}' (匹配度: {highest_ratio:.2f})")
            return best_match

        # 返回默认球队对象
        logging.warning(f"未找到球队 '{team_name}' 的数据")
        return Team(name=team_name)

    def calculate_strength_gap(self, home_team, away_team):
        """
        计算两队之间的档距差

        Args:
            home_team: 主队对象或名称
            away_team: 客队对象或名称

        Returns:
            float: 档距差，如果无法计算则返回None
        """
        # 处理输入为球队名称的情况
        if isinstance(home_team, str):
            home_team = self.get_team_data(home_team)
        if isinstance(away_team, str):
            away_team = self.get_team_data(away_team)

        # 获取评分
        home_rating = home_team.rating
        away_rating = away_team.rating

        # 检查输入是否为有效数值
        if home_rating is None or away_rating is None:
            return None

        try:
            # 将输入转换为浮点数
            home_float = float(home_rating)
            away_float = float(away_rating)

            # 计算档距差（主队评分 - 客队评分）
            gap = home_float - away_float
            return round(gap, 2)  # 保留两位小数
        except (ValueError, TypeError):
            logging.error(f"无法计算档距差: home_rating={home_rating}, away_rating={away_rating}")
            return None