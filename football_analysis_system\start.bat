@echo off
:: 设置控制台代码页为UTF-8
chcp 65001 > nul
echo 正在启动足球分析系统...

:: 设置Python路径（如果不在PATH中）
set PYTHON_CMD=python

:: 检查Python是否可用
%PYTHON_CMD% --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Python未找到，请安装Python或设置正确的路径
    pause
    exit /b 1
)

:: 进入程序目录
cd /d "%~dp0"

:: 启动Python脚本
echo 使用 %PYTHON_CMD% 启动程序...
%PYTHON_CMD% main.py

:: 如果出错，显示错误信息
if %ERRORLEVEL% neq 0 (
    echo 程序运行出错，错误代码: %ERRORLEVEL%
    pause
) else (
    echo 程序已正常退出
    pause
) 