#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 OddsScraper 的 debug_game_content 方法
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrapers.odds_scraper import test_game_content_analysis
from football_analysis_system.config import ODDS_URL_TEMPLATE, ODDS_HEADERS_TEMPLATE, TARGET_COMPANIES

def main():
    """主测试函数"""
    
    # 设置日志级别为INFO，这样可以看到详细的调试信息
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_debug.log', encoding='utf-8')
        ]
    )
    
    print("=== 足球赔率数据调试测试 ===")
    print(f"URL模板: {ODDS_URL_TEMPLATE}")
    print(f"目标公司: {TARGET_COMPANIES}")
    
    # 使用从JSON文件中找到的真实比赛ID
    test_match_ids = [
        "2696009",  # 澳维甲比赛
        # 如果需要测试更多，可以在这里添加其他比赛ID
    ]
    
    for match_id in test_match_ids:
        print(f"\n{'='*60}")
        print(f"正在测试比赛ID: {match_id}")
        print(f"{'='*60}")
        
        try:
            # 调用测试函数
            result = test_game_content_analysis(
                match_id=match_id,
                url_template=ODDS_URL_TEMPLATE,
                headers_template=ODDS_HEADERS_TEMPLATE,
                target_companies=TARGET_COMPANIES
            )
            
            if result:
                print(f"\n✅ 测试成功完成！")
                print(f"   - 比赛ID: {result['match_id']}")
                print(f"   - 总记录数: {result['total_records']}")
                print(f"   - 解析记录数: {len(result['parsed_records'])}")
                
                # 显示找到的目标公司
                found_companies = []
                for record in result['parsed_records']:
                    company_name = record['parsed_fields'].get('company_name', '')
                    if company_name:
                        found_companies.append(company_name)
                
                if found_companies:
                    print(f"   - 找到的公司: {', '.join(found_companies[:5])}{'...' if len(found_companies) > 5 else ''}")
                
            else:
                print(f"❌ 测试失败：无法获取比赛 {match_id} 的数据")
                
        except Exception as e:
            print(f"❌ 测试过程中出现异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print("测试完成！请查看生成的调试文件了解详细信息。")
    print(f"{'='*60}")

if __name__ == "__main__":
    main() 