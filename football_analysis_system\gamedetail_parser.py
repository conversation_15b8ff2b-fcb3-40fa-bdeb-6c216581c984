#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GameDetail 专业解析器 - 解析赔率历史变化数据
"""

import time
import requests
import re
import json
import sqlite3
from datetime import datetime
from collections import defaultdict

def get_match_js_content(match_id):
    """获取比赛的JS内容"""
    timestamp_part = str(int(time.time() * 1000))
    if len(timestamp_part) > 13:
        timestamp_part = timestamp_part[-13:]
    else:
        timestamp_part = timestamp_part.ljust(13, '0')
    timestamp = "007" + timestamp_part
    
    url = f"https://1x2d.titan007.com/{match_id}.js?r={timestamp}"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Referer': f'https://op1.titan007.com/oddslist/{match_id}.htm',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        if response.status_code == 200:
            return response.text
        return None
    except Exception as e:
        print(f"❌ 请求出错: {e}")
        return None

def get_company_name_mapping(js_content):
    """从game数组获取公司ID到名称的映射"""
    company_mapping = {}
    
    game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', js_content, re.DOTALL)
    if game_match:
        game_data = game_match.group(1)
        company_records = re.findall(r'"([^"]*)"', game_data)
        
        for record in company_records:
            fields = record.split('|')
            if len(fields) >= 3:
                company_id = fields[0]
                record_id = fields[1]
                company_name = fields[2].strip()
                company_mapping[record_id] = {
                    'company_id': company_id,
                    'company_name': company_name
                }
    
    return company_mapping

def parse_odds_history_record(odds_string):
    """解析单条赔率历史记录"""
    # 格式: 主胜|平局|客胜|时间|kelly1|kelly2|kelly3|年份
    parts = odds_string.split('|')
    
    if len(parts) >= 8:
        return {
            'home_odds': float(parts[0]) if parts[0] else None,
            'draw_odds': float(parts[1]) if parts[1] else None,
            'away_odds': float(parts[2]) if parts[2] else None,
            'update_time': parts[3],  # 格式: MM-DD HH:MM
            'kelly_home': float(parts[4]) if parts[4] else None,
            'kelly_draw': float(parts[5]) if parts[5] else None,
            'kelly_away': float(parts[6]) if parts[6] else None,
            'year': parts[7]
        }
    return None

def parse_gamedetail_data(js_content):
    """解析gameDetail数据，提取所有公司的赔率历史"""
    
    # 获取公司映射
    company_mapping = get_company_name_mapping(js_content)
    
    # 提取gameDetail数据
    gamedetail_match = re.search(r'var\s+gameDetail\s*=\s*Array\((.*?)\);', js_content, re.DOTALL)
    if not gamedetail_match:
        print("❌ 未找到gameDetail数组")
        return {}
    
    gamedetail_data = gamedetail_match.group(1)
    company_records = re.findall(r'"([^"]*)"', gamedetail_data)
    
    print(f"✅ 找到 {len(company_records)} 条gameDetail记录")
    
    companies_history = {}
    
    for record in company_records:
        try:
            # gameDetail格式: record_id^历史数据
            parts = record.split('^')
            if len(parts) < 2:
                continue
            
            record_id = parts[0]
            history_data = parts[1]
            
            # 获取公司信息
            company_info = company_mapping.get(record_id, {
                'company_id': 'unknown',
                'company_name': f'Unknown_{record_id}'
            })
            
            # 解析历史数据，用分号分隔不同时间点的记录
            history_entries = history_data.split(';')
            
            odds_history = []
            for entry in history_entries:
                if entry.strip():
                    parsed_entry = parse_odds_history_record(entry)
                    if parsed_entry:
                        odds_history.append(parsed_entry)
            
            if odds_history:
                companies_history[record_id] = {
                    'company_id': company_info['company_id'],
                    'company_name': company_info['company_name'],
                    'record_id': record_id,
                    'history_count': len(odds_history),
                    'odds_history': odds_history
                }
                
        except Exception as e:
            print(f"解析记录失败: {e}")
            continue
    
    return companies_history

def analyze_odds_changes(companies_history):
    """分析赔率变化趋势"""
    
    print(f"\n=== 赔率变化分析 ===")
    
    analysis_results = {}
    
    for record_id, company_data in companies_history.items():
        company_name = company_data['company_name']
        history = company_data['odds_history']
        
        if len(history) < 2:
            continue
        
        # 计算变化统计
        first_odds = history[-1]  # 最早的赔率
        latest_odds = history[0]  # 最新的赔率
        
        home_change = latest_odds['home_odds'] - first_odds['home_odds'] if latest_odds['home_odds'] and first_odds['home_odds'] else 0
        draw_change = latest_odds['draw_odds'] - first_odds['draw_odds'] if latest_odds['draw_odds'] and first_odds['draw_odds'] else 0
        away_change = latest_odds['away_odds'] - first_odds['away_odds'] if latest_odds['away_odds'] and first_odds['away_odds'] else 0
        
        analysis_results[record_id] = {
            'company_name': company_name,
            'history_count': len(history),
            'first_odds': first_odds,
            'latest_odds': latest_odds,
            'changes': {
                'home': round(home_change, 3),
                'draw': round(draw_change, 3),
                'away': round(away_change, 3)
            },
            'time_span': f"{first_odds['update_time']} -> {latest_odds['update_time']}"
        }
    
    # 显示分析结果
    print(f"分析了 {len(analysis_results)} 家公司的赔率变化")
    
    # 显示变化最大的公司
    print(f"\n=== 赔率变化最大的公司 (前5家) ===")
    sorted_companies = sorted(
        analysis_results.items(), 
        key=lambda x: abs(x[1]['changes']['home']) + abs(x[1]['changes']['draw']) + abs(x[1]['changes']['away']), 
        reverse=True
    )
    
    for i, (record_id, data) in enumerate(sorted_companies[:5]):
        print(f"\n{i+1}. {data['company_name']} (记录数: {data['history_count']})")
        print(f"   时间跨度: {data['time_span']}")
        print(f"   首次赔率: {data['first_odds']['home_odds']:.2f} / {data['first_odds']['draw_odds']:.2f} / {data['first_odds']['away_odds']:.2f}")
        print(f"   最新赔率: {data['latest_odds']['home_odds']:.2f} / {data['latest_odds']['draw_odds']:.2f} / {data['latest_odds']['away_odds']:.2f}")
        print(f"   变化幅度: {data['changes']['home']:+.3f} / {data['changes']['draw']:+.3f} / {data['changes']['away']:+.3f}")
    
    return analysis_results

def save_odds_history_to_database(match_id, companies_history, db_path="data/matches_and_odds.db"):
    """保存赔率历史到数据库"""
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建赔率历史表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS odds_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT NOT NULL,
                company_id TEXT NOT NULL,
                company_name TEXT NOT NULL,
                record_id TEXT NOT NULL,
                home_odds REAL,
                draw_odds REAL,
                away_odds REAL,
                update_time TEXT,
                kelly_home REAL,
                kelly_draw REAL,
                kelly_away REAL,
                year TEXT,
                scrape_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_odds_history_match_company 
            ON odds_history(match_id, company_id, update_time)
        ''')
        
        saved_count = 0
        
        for record_id, company_data in companies_history.items():
            company_id = company_data['company_id']
            company_name = company_data['company_name']
            
            for history_entry in company_data['odds_history']:
                cursor.execute('''
                    INSERT OR REPLACE INTO odds_history 
                    (match_id, company_id, company_name, record_id, home_odds, draw_odds, away_odds, 
                     update_time, kelly_home, kelly_draw, kelly_away, year, scrape_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    str(match_id),
                    company_id,
                    company_name,
                    record_id,
                    history_entry['home_odds'],
                    history_entry['draw_odds'],
                    history_entry['away_odds'],
                    history_entry['update_time'],
                    history_entry['kelly_home'],
                    history_entry['kelly_draw'],
                    history_entry['kelly_away'],
                    history_entry['year'],
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ))
                saved_count += 1
        
        conn.commit()
        conn.close()
        
        print(f"✅ 成功保存 {saved_count} 条赔率历史记录到数据库")
        return True
        
    except Exception as e:
        print(f"❌ 保存赔率历史失败: {e}")
        if 'conn' in locals():
            conn.close()
        return False

def export_odds_history_report(match_id, companies_history, analysis_results):
    """导出赔率历史报告"""
    
    try:
        report_file = f"odds_history_report_{match_id}.txt"
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(f"赔率历史分析报告\n")
            f.write(f"比赛ID: {match_id}\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析公司数: {len(companies_history)}\n\n")
            
            f.write("=== 公司赔率历史概览 ===\n")
            for record_id, company_data in companies_history.items():
                f.write(f"\n{company_data['company_name']} (ID: {company_data['company_id']}, 记录ID: {record_id})\n")
                f.write(f"  历史记录数: {company_data['history_count']}\n")
                
                # 显示前5条和最后5条历史记录
                history = company_data['odds_history']
                f.write(f"  最新记录 (前5条):\n")
                for i, entry in enumerate(history[:5]):
                    f.write(f"    {i+1}. {entry['update_time']} -> {entry['home_odds']:.2f}/{entry['draw_odds']:.2f}/{entry['away_odds']:.2f}\n")
                
                if len(history) > 10:
                    f.write(f"    ... (省略 {len(history)-10} 条记录)\n")
                
                if len(history) > 5:
                    f.write(f"  最早记录 (最后5条):\n")
                    for i, entry in enumerate(history[-5:]):
                        f.write(f"    {len(history)-5+i+1}. {entry['update_time']} -> {entry['home_odds']:.2f}/{entry['draw_odds']:.2f}/{entry['away_odds']:.2f}\n")
            
            # 添加变化分析
            if analysis_results:
                f.write(f"\n=== 赔率变化分析 ===\n")
                sorted_companies = sorted(
                    analysis_results.items(), 
                    key=lambda x: abs(x[1]['changes']['home']) + abs(x[1]['changes']['draw']) + abs(x[1]['changes']['away']), 
                    reverse=True
                )
                
                for i, (record_id, data) in enumerate(sorted_companies):
                    f.write(f"\n{i+1}. {data['company_name']}\n")
                    f.write(f"   时间跨度: {data['time_span']}\n")
                    f.write(f"   变化幅度: 主胜{data['changes']['home']:+.3f}, 平局{data['changes']['draw']:+.3f}, 客胜{data['changes']['away']:+.3f}\n")
        
        print(f"✅ 赔率历史报告已保存到: {report_file}")
        
        # 同时保存JSON格式
        json_file = f"odds_history_data_{match_id}.json"
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump({
                'match_id': match_id,
                'companies_history': companies_history,
                'analysis_results': analysis_results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 结构化数据已保存到: {json_file}")
        
    except Exception as e:
        print(f"❌ 导出报告失败: {e}")

def main():
    """主函数"""
    
    print("=" * 60)
    print("📈 GameDetail 赔率历史解析器")
    print("=" * 60)
    
    # 测试比赛ID
    test_match_id = "2696009"
    
    print(f"分析比赛ID: {test_match_id}")
    
    # 1. 获取JS内容
    print("1. 获取JS数据...")
    js_content = get_match_js_content(test_match_id)
    if not js_content:
        print("❌ 无法获取JS内容")
        return
    
    # 2. 解析gameDetail数据
    print("2. 解析赔率历史数据...")
    companies_history = parse_gamedetail_data(js_content)
    if not companies_history:
        print("❌ 无法解析赔率历史数据")
        return
    
    # 3. 分析赔率变化
    print("3. 分析赔率变化趋势...")
    analysis_results = analyze_odds_changes(companies_history)
    
    # 4. 保存到数据库
    print("4. 保存到数据库...")
    save_odds_history_to_database(test_match_id, companies_history)
    
    # 5. 导出报告
    print("5. 导出分析报告...")
    export_odds_history_report(test_match_id, companies_history, analysis_results)
    
    print(f"\n🎉 GameDetail 解析完成！")
    print(f"总结:")
    print(f"  - 解析了 {len(companies_history)} 家公司的赔率历史")
    print(f"  - 总历史记录数: {sum(c['history_count'] for c in companies_history.values())}")
    print(f"  - 数据已保存到数据库和文件")

if __name__ == "__main__":
    main() 