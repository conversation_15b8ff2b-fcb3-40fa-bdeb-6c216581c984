#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用本地数据测试目标公司历史赔率解析器
"""

import re
import json
from datetime import datetime

# 目标公司列表
TARGET_COMPANIES = [
    '澳门', '威廉希尔', '易胜博', '香港马会', 'BWIN', '伟德', 
    'Nordicbet', '利记', 'BetISn', 'iddaa'
]

def load_local_js_data():
    """加载本地JS数据（从之前的gamedetail_data_2696009.json）"""
    try:
        with open("gamedetail_data_2696009.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        
        # 模拟JS内容，我们需要构造game和gameDetail数组
        # 从game_data.txt获取game数组内容
        with open("game_data.txt", "r", encoding="utf-8") as f:
            game_content = f.read().strip()
        
        # 构造完整的JS内容
        js_content = f"var game = Array({game_content});\n"
        
        # 从JSON数据构造gameDetail内容
        gamedetail_records = []
        
        # 这里我们需要从之前解析的数据重新构造gameDetail格式
        # 由于我们已经有了解析后的数据，我们可以直接使用
        print("✅ 加载本地数据成功")
        return js_content, data
        
    except FileNotFoundError:
        print("❌ 未找到本地数据文件")
        return None, None

def get_target_company_mapping_from_game_data():
    """从本地game_data.txt获取目标公司映射"""
    company_mapping = {}
    
    try:
        with open("game_data.txt", "r", encoding="utf-8") as f:
            game_content = f.read().strip()
        
        # 解析game数据
        company_records = re.findall(r'"([^"]*)"', game_content)
        
        print(f"📋 开始匹配目标公司...")
        
        for record in company_records:
            fields = record.split('|')
            if len(fields) >= 3:
                company_id = fields[0]
                record_id = fields[1]
                company_name = fields[2].strip()
                
                # 匹配目标公司
                matched_target_name = None
                for target_name in TARGET_COMPANIES:
                    normalized_target_name = target_name.lower().replace(' ','')
                    normalized_company_name = company_name.lower().replace(' ','')
                    
                    # 精确匹配
                    if normalized_target_name == normalized_company_name:
                        matched_target_name = target_name
                        break
                    
                    # 特殊匹配规则
                    if target_name == '威廉希尔' and (company_id == '115' or company_name.startswith('威') or 'william hill' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == '易胜博' and (company_id == '90' or company_name.startswith('易') or 'easybets' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == '香港马会' and (company_id == '432' or company_name.startswith('香港马') or 'hkjc' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == '澳门' and (company_id == '80' or company_name.startswith('澳') or 'macao' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == 'BWIN' and (company_id == '255' or company_name.startswith('Bwi')):
                        matched_target_name = target_name
                        break
                    if target_name == '伟德' and (company_id == '81' or company_name.startswith('伟')):
                        matched_target_name = target_name
                        break
                    if target_name == 'Nordicbet' and (company_id == '4' or company_name.startswith('Nordic')):
                        matched_target_name = target_name
                        break
                    if target_name == '利记' and (company_id == '474' or company_name.startswith('利')):
                        matched_target_name = target_name
                        break
                    if target_name == 'BetISn' and (company_id == '937' or 'betisn' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == 'iddaa' and (company_id == '657' or 'iddaa' in normalized_company_name):
                        matched_target_name = target_name
                        break
                
                if matched_target_name:
                    company_mapping[record_id] = {
                        'company_id': company_id,
                        'company_name': company_name,
                        'target_name': matched_target_name
                    }
                    print(f"  ✅ 匹配到: {matched_target_name} -> {company_name} (ID: {company_id}, 记录ID: {record_id})")
        
        print(f"📊 总共匹配到 {len(company_mapping)} 家目标公司")
        return company_mapping
        
    except FileNotFoundError:
        print("❌ 未找到game_data.txt文件")
        return {}

def parse_target_companies_from_json():
    """从JSON数据中提取目标公司的历史赔率"""
    
    # 获取目标公司映射
    company_mapping = get_target_company_mapping_from_game_data()
    if not company_mapping:
        print("❌ 未找到任何目标公司")
        return {}
    
    # 加载历史数据
    try:
        with open("odds_history_data_2696009.json", "r", encoding="utf-8") as f:
            history_data = json.load(f)
        
        companies_history = history_data.get('companies_history', {})
        
        print(f"🔍 从历史数据中筛选目标公司...")
        
        target_companies_history = {}
        
        # 遍历所有历史数据，找到目标公司
        for record_id, company_data in companies_history.items():
            # 检查是否为目标公司
            if record_id in company_mapping:
                target_name = company_mapping[record_id]['target_name']
                
                target_companies_history[target_name] = {
                    'company_id': company_data['company_id'],
                    'company_name': company_data['company_name'],
                    'record_id': record_id,
                    'target_name': target_name,
                    'history_count': company_data['history_count'],
                    'odds_history': company_data['odds_history']
                }
                
                print(f"  ✅ {target_name}: {company_data['history_count']} 条历史记录")
        
        return target_companies_history
        
    except FileNotFoundError:
        print("❌ 未找到odds_history_data_2696009.json文件")
        return {}

def display_target_companies_detailed(target_companies_history):
    """显示目标公司详细历史赔率"""
    
    print(f"\n" + "="*80)
    print(f"🎯 目标公司完整历史赔率数据")
    print(f"="*80)
    
    if not target_companies_history:
        print("❌ 没有找到任何目标公司的历史数据")
        return
    
    total_records = sum(data['history_count'] for data in target_companies_history.values())
    
    print(f"📊 匹配的目标公司数: {len(target_companies_history)}")
    print(f"📈 总历史记录数: {total_records}")
    
    for target_name, data in target_companies_history.items():
        print(f"\n" + "="*60)
        print(f"🏢 【{target_name}】")
        print(f"="*60)
        print(f"公司名称: {data['company_name']}")
        print(f"公司ID: {data['company_id']}")
        print(f"记录ID: {data['record_id']}")
        print(f"历史记录数: {data['history_count']}")
        
        print(f"\n完整历史赔率变化:")
        print(f"{'序号':<4} {'时间':<12} {'主胜':<8} {'平局':<8} {'客胜':<8} {'Kelly主':<8} {'Kelly平':<8} {'Kelly客':<8}")
        print("-" * 80)
        
        # 显示所有历史记录
        for i, entry in enumerate(data['odds_history'], 1):
            print(f"{i:<4} {entry['update_time']:<12} {entry['home_odds']:<8.2f} {entry['draw_odds']:<8.2f} {entry['away_odds']:<8.2f} ", end="")
            print(f"{entry['kelly_home']:<8.2f} {entry['kelly_draw']:<8.2f} {entry['kelly_away']:<8.2f}")
        
        # 显示变化统计
        if data['odds_history']:
            latest = data['odds_history'][0]
            earliest = data['odds_history'][-1]
            
            home_change = latest['home_odds'] - earliest['home_odds']
            draw_change = latest['draw_odds'] - earliest['draw_odds']
            away_change = latest['away_odds'] - earliest['away_odds']
            
            print(f"\n📊 赔率变化统计:")
            print(f"   时间跨度: {earliest['update_time']} -> {latest['update_time']}")
            print(f"   主胜变化: {earliest['home_odds']:.2f} -> {latest['home_odds']:.2f} ({home_change:+.3f})")
            print(f"   平局变化: {earliest['draw_odds']:.2f} -> {latest['draw_odds']:.2f} ({draw_change:+.3f})")
            print(f"   客胜变化: {earliest['away_odds']:.2f} -> {latest['away_odds']:.2f} ({away_change:+.3f})")

def export_target_companies_detailed_report(match_id, target_companies_history):
    """导出目标公司详细历史赔率报告"""
    
    if not target_companies_history:
        print("❌ 没有数据需要导出")
        return
    
    try:
        # 导出详细报告
        report_file = f"target_companies_detailed_history_{match_id}.txt"
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(f"目标公司完整历史赔率详细报告\n")
            f.write(f"比赛ID: {match_id}\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"目标公司数: {len(target_companies_history)}\n")
            f.write(f"总历史记录数: {sum(data['history_count'] for data in target_companies_history.values())}\n\n")
            
            f.write("="*100 + "\n")
            f.write("目标公司完整历史赔率数据\n")
            f.write("="*100 + "\n")
            
            for target_name, company_data in target_companies_history.items():
                f.write(f"\n【{target_name}】\n")
                f.write(f"公司名称: {company_data['company_name']}\n")
                f.write(f"公司ID: {company_data['company_id']}\n")
                f.write(f"记录ID: {company_data['record_id']}\n")
                f.write(f"历史记录数: {company_data['history_count']}\n")
                f.write(f"\n所有历史赔率记录:\n")
                f.write(f"{'序号':<4} {'时间':<12} {'主胜':<8} {'平局':<8} {'客胜':<8} {'Kelly主':<8} {'Kelly平':<8} {'Kelly客':<8}\n")
                f.write("-" * 90 + "\n")
                
                for i, entry in enumerate(company_data['odds_history'], 1):
                    f.write(f"{i:<4} {entry['update_time']:<12} {entry['home_odds']:<8.2f} {entry['draw_odds']:<8.2f} {entry['away_odds']:<8.2f} ")
                    f.write(f"{entry['kelly_home']:<8.2f} {entry['kelly_draw']:<8.2f} {entry['kelly_away']:<8.2f}\n")
                
                # 添加变化统计
                if company_data['odds_history']:
                    latest = company_data['odds_history'][0]
                    earliest = company_data['odds_history'][-1]
                    
                    home_change = latest['home_odds'] - earliest['home_odds']
                    draw_change = latest['draw_odds'] - earliest['draw_odds']
                    away_change = latest['away_odds'] - earliest['away_odds']
                    
                    f.write(f"\n赔率变化统计:\n")
                    f.write(f"  时间跨度: {earliest['update_time']} -> {latest['update_time']}\n")
                    f.write(f"  主胜变化: {earliest['home_odds']:.2f} -> {latest['home_odds']:.2f} ({home_change:+.3f})\n")
                    f.write(f"  平局变化: {earliest['draw_odds']:.2f} -> {latest['draw_odds']:.2f} ({draw_change:+.3f})\n")
                    f.write(f"  客胜变化: {earliest['away_odds']:.2f} -> {latest['away_odds']:.2f} ({away_change:+.3f})\n")
                
                f.write("\n" + "="*100 + "\n")
        
        print(f"✅ 详细报告已保存到: {report_file}")
        
        # 同时保存JSON格式
        json_file = f"target_companies_detailed_history_{match_id}.json"
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump({
                'match_id': match_id,
                'target_companies_history': target_companies_history,
                'summary': {
                    'total_companies': len(target_companies_history),
                    'total_records': sum(data['history_count'] for data in target_companies_history.values())
                }
            }, f, ensure_ascii=False, indent=2)
        
        print(f"✅ JSON数据已保存到: {json_file}")
        
    except Exception as e:
        print(f"❌ 导出报告失败: {e}")

def main():
    """主函数"""
    
    print("=" * 80)
    print("🎯 目标公司历史赔率详细解析器 (使用本地数据)")
    print("=" * 80)
    
    # 显示配置的目标公司
    print(f"📋 配置的目标公司: {', '.join(TARGET_COMPANIES)}")
    
    # 测试比赛ID
    test_match_id = "2696009"
    
    print(f"\n🔍 分析比赛ID: {test_match_id}")
    
    # 1. 从JSON数据中解析目标公司历史数据
    print("\n1️⃣ 从本地数据解析目标公司历史赔率...")
    target_companies_history = parse_target_companies_from_json()
    if not target_companies_history:
        print("❌ 未找到任何目标公司的历史数据")
        return
    
    # 2. 显示详细结果
    print("\n2️⃣ 显示完整历史赔率数据...")
    display_target_companies_detailed(target_companies_history)
    
    # 3. 导出报告
    print(f"\n3️⃣ 导出详细报告...")
    export_target_companies_detailed_report(test_match_id, target_companies_history)
    
    print(f"\n🎉 目标公司历史赔率详细解析完成！")
    print(f"📄 已生成包含所有时间点主平客赔率的完整报告")

if __name__ == "__main__":
    main() 