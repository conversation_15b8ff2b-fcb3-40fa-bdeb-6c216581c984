# Design Document

## Overview

The football analysis system startup failure is caused by a circular import dependency between `config_manager.py` and `path_resolver.py`. The config manager tries to import path_resolver during initialization, while path_resolver imports config_manager at module level. This creates a circular dependency that prevents proper initialization.

The solution involves restructuring the dependency chain to eliminate circular imports and implementing robust directory creation with proper error handling.

## Architecture

### Current Problem Architecture
```
config_manager.py (initialization)
    ↓ (imports during _ensure_directories)
path_resolver.py (module level)
    ↓ (imports at top)
config_manager.py (circular dependency)
```

### Proposed Solution Architecture
```
config_manager.py (standalone initialization)
    ↓ (no imports during init)
path_resolver.py (lazy import)
    ↓ (imports only when needed)
config_manager.py (after full initialization)
```

## Components and Interfaces

### 1. ConfigManager (Modified)
- **Responsibility**: Manage all system configuration without external dependencies during initialization
- **Key Changes**:
  - Remove dependency on PathResolver during `__init__`
  - Implement directory creation directly in config manager
  - Use lazy loading for any external dependencies

### 2. PathResolver (Modified)
- **Responsibility**: Provide path resolution services after config manager is fully initialized
- **Key Changes**:
  - Use lazy import of config_manager (import inside methods, not at module level)
  - Add initialization check before accessing config_manager
  - Provide fallback behavior when config_manager is not ready

### 3. DirectoryManager (New)
- **Responsibility**: Handle directory creation with robust error handling
- **Interface**:
  ```python
  class DirectoryManager:
      @staticmethod
      def ensure_directory(path: str, create_parents: bool = True) -> bool
      @staticmethod
      def create_project_directories(base_paths: dict) -> dict
      @staticmethod
      def handle_directory_error(path: str, error: Exception) -> str
  ```

## Data Models

### Configuration Structure
```python
{
    "paths": {
        "project_root": str,
        "data_dir": str,
        "logs_dir": str,
        "temp_dir": str,
        "backup_dir": str
    },
    "initialization": {
        "directories_created": bool,
        "config_loaded": bool,
        "errors": list
    }
}
```

### Directory Creation Result
```python
{
    "success": bool,
    "created_paths": list,
    "failed_paths": list,
    "errors": dict  # path -> error_message
}
```

## Error Handling

### Directory Creation Errors
1. **Permission Denied**: Log error, attempt to use alternative directory (temp folder)
2. **Path Too Long**: Truncate path or use shorter alternative
3. **Disk Full**: Log error, continue with existing directories
4. **Invalid Characters**: Sanitize path and retry

### Import Errors
1. **Circular Import**: Use lazy imports and initialization checks
2. **Module Not Found**: Provide fallback implementations
3. **Initialization Failure**: Continue with default configuration

### Error Recovery Strategy
```python
def safe_directory_creation(path: str) -> tuple[bool, str]:
    """
    Attempt directory creation with multiple fallback strategies
    Returns: (success, actual_path_used)
    """
    strategies = [
        lambda: create_exact_path(path),
        lambda: create_in_temp_dir(path),
        lambda: create_in_user_home(path),
        lambda: use_current_directory(path)
    ]
    
    for strategy in strategies:
        try:
            result_path = strategy()
            return True, result_path
        except Exception as e:
            log_error(f"Strategy failed: {e}")
            continue
    
    return False, ""
```

## Testing Strategy

### Unit Tests
1. **ConfigManager Tests**:
   - Test initialization without circular imports
   - Test directory creation with various permission scenarios
   - Test configuration loading from different sources

2. **PathResolver Tests**:
   - Test lazy import behavior
   - Test path resolution with and without config_manager
   - Test fallback path resolution

3. **DirectoryManager Tests**:
   - Test directory creation success scenarios
   - Test error handling for various failure modes
   - Test fallback directory strategies

### Integration Tests
1. **Startup Tests**:
   - Test complete system startup without errors
   - Test startup with various directory permission scenarios
   - Test startup with missing configuration files

2. **Module Import Tests**:
   - Test import order independence
   - Test circular import prevention
   - Test lazy loading behavior

### Error Simulation Tests
1. **Permission Error Simulation**: Test behavior when directories cannot be created
2. **Disk Full Simulation**: Test behavior when disk space is insufficient
3. **Path Length Simulation**: Test behavior with very long paths
4. **Network Drive Simulation**: Test behavior with network-mounted directories

## Implementation Phases

### Phase 1: Fix Circular Import
- Remove PathResolver import from ConfigManager.__init__
- Implement lazy import in PathResolver
- Add initialization checks

### Phase 2: Robust Directory Creation
- Create DirectoryManager class
- Implement fallback directory strategies
- Add comprehensive error handling

### Phase 3: Enhanced Error Recovery
- Implement multiple fallback strategies
- Add detailed error logging
- Provide user-friendly error messages

### Phase 4: Testing and Validation
- Create comprehensive test suite
- Test various error scenarios
- Validate startup reliability