# Design Document

## Overview

历史赔率区间分析功能是一个新的标签页模块，它将从odds_history表中获取历史赔率数据，使用现有的区间分析算法计算每个时间点的开盘区间，并通过可视化折线图展示主平客赔率在不同区间的时间变化趋势。该功能复用现有的OddsAnalyzer和IntervalAnalyzer组件，确保与当前系统的一致性。

## Architecture

### 系统架构图

```mermaid
graph TB
    A[历史赔率区间分析标签页] --> B[数据获取层]
    A --> C[计算处理层]
    A --> D[可视化展示层]
    
    B --> E[odds_history表]
    B --> F[公司选择器]
    
    C --> G[返还率计算器]
    C --> H[现有OddsAnalyzer]
    C --> I[现有IntervalAnalyzer]
    
    D --> J[matplotlib图表]
    D --> K[区间映射器]
    
    H --> L[calculate_true_odds]
    H --> M[find_gap_for_odds]
    I --> N[find_interval_for_gap]
```

### 数据流程

1. **数据获取**: 从odds_history表按公司和比赛ID获取历史赔率数据
2. **返还率计算**: 使用博彩公式计算每个时间点的返还率
3. **满水赔率计算**: 使用现有calculate_true_odds方法计算满水赔率
4. **档距查找**: 使用现有find_gap_for_odds方法查找档距
5. **区间计算**: 使用现有find_interval_for_gap方法计算开盘区间
6. **数据可视化**: 将区间类型转换为Y轴坐标，绘制时间序列折线图

## Components and Interfaces

### 1. HistoricalOddsIntervalTab (主标签页类)

```python
class HistoricalOddsIntervalTab(tk.Frame):
    """历史赔率区间分析标签页"""
    
    def __init__(self, parent, odds_analyzer, interval_analyzer, db_path=None):
        """
        初始化标签页
        
        Args:
            parent: 父容器
            odds_analyzer: 现有的赔率分析器实例
            interval_analyzer: 现有的区间分析器实例
            db_path: 数据库路径
        """
        
    def setup_ui(self):
        """设置用户界面"""
        
    def load_companies(self):
        """从odds_history表加载可用的博彩公司列表"""
        
    def on_company_selected(self, event):
        """公司选择事件处理"""
        
    def analyze_historical_data(self, company_id, match_id):
        """分析指定公司的历史数据"""
        
    def create_visualization(self, processed_data):
        """创建可视化图表"""
```

### 2. HistoricalDataProcessor (数据处理器)

```python
class HistoricalDataProcessor:
    """历史数据处理器"""
    
    def __init__(self, odds_analyzer, interval_analyzer):
        """
        初始化处理器
        
        Args:
            odds_analyzer: 赔率分析器
            interval_analyzer: 区间分析器
        """
        
    def calculate_payout_rate(self, home_odds, draw_odds, away_odds):
        """
        计算返还率
        
        Args:
            home_odds: 主胜赔率
            draw_odds: 平局赔率  
            away_odds: 客胜赔率
            
        Returns:
            float: 返还率百分比
        """
        
    def process_historical_odds(self, historical_data, gap_difference):
        """
        处理历史赔率数据
        
        Args:
            historical_data: 从数据库获取的历史数据
            gap_difference: 档距差
            
        Returns:
            list: 处理后的数据，包含时间、区间类型等信息
        """
```

### 3. IntervalVisualizer (可视化组件)

```python
class IntervalVisualizer:
    """区间可视化组件"""
    
    def __init__(self, parent_frame):
        """
        初始化可视化组件
        
        Args:
            parent_frame: 父框架
        """
        
    def create_interval_chart(self, processed_data):
        """
        创建区间变化折线图
        
        Args:
            processed_data: 处理后的数据
            
        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        
    def map_interval_to_y_coordinate(self, interval_type):
        """
        将区间类型映射为Y轴坐标
        
        Args:
            interval_type: 区间类型字符串
            
        Returns:
            int: Y轴坐标值
        """
```

### 4. DatabaseInterface (数据库接口)

```python
class DatabaseInterface:
    """数据库接口类"""
    
    def __init__(self, db_path):
        """
        初始化数据库接口
        
        Args:
            db_path: 数据库路径
        """
        
    def get_available_companies(self, match_id):
        """
        获取指定比赛的可用博彩公司
        
        Args:
            match_id: 比赛ID
            
        Returns:
            list: 公司列表，包含company_id和company_name
        """
        
    def get_historical_odds(self, match_id, company_id):
        """
        获取指定公司的历史赔率数据
        
        Args:
            match_id: 比赛ID
            company_id: 公司ID
            
        Returns:
            list: 历史赔率数据，按时间排序
        """
```

## Data Models

### 1. HistoricalOddsRecord (历史赔率记录)

```python
@dataclass
class HistoricalOddsRecord:
    """历史赔率记录数据模型"""
    
    match_id: str
    company_id: str
    company_name: str
    home_odds: float
    draw_odds: float
    away_odds: float
    update_time: str
    payout_rate: float = None  # 计算得出
```

### 2. IntervalAnalysisResult (区间分析结果)

```python
@dataclass
class IntervalAnalysisResult:
    """区间分析结果数据模型"""
    
    timestamp: str
    home_interval: str
    draw_interval: str
    away_interval: str
    home_rule_value: float
    away_rule_value: float
    home_true_odds: float
    draw_true_odds: float
    away_true_odds: float
```

### 3. VisualizationData (可视化数据)

```python
@dataclass
class VisualizationData:
    """可视化数据模型"""
    
    timestamps: List[str]
    home_y_coords: List[int]
    draw_y_coords: List[int]
    away_y_coords: List[int]
    interval_labels: Dict[int, str]  # Y坐标到区间名称的映射
```

## Error Handling

### 1. 数据获取错误处理

- **数据库连接失败**: 显示错误消息，禁用功能
- **无历史数据**: 显示友好提示信息
- **数据格式错误**: 跳过异常记录，记录日志

### 2. 计算错误处理

- **返还率计算异常**: 使用默认返还率或跳过该记录
- **满水赔率计算失败**: 记录警告日志，跳过该时间点
- **区间匹配失败**: 标记为"未知区间"

### 3. 可视化错误处理

- **图表创建失败**: 显示错误消息，提供重试选项
- **数据为空**: 显示"暂无数据"提示
- **内存不足**: 限制数据量，分批处理

## Testing Strategy

### 1. 单元测试

- **返还率计算测试**: 验证博彩公式的正确性
- **区间映射测试**: 确保区间类型正确映射到Y坐标
- **数据处理测试**: 验证历史数据处理的准确性

### 2. 集成测试

- **数据库集成测试**: 验证与odds_history表的交互
- **组件集成测试**: 测试与现有OddsAnalyzer和IntervalAnalyzer的集成
- **UI集成测试**: 验证用户界面的响应性

### 3. 性能测试

- **大数据量测试**: 测试处理大量历史数据的性能
- **内存使用测试**: 监控内存使用情况
- **响应时间测试**: 确保用户体验的流畅性

## Implementation Details

### 1. 返还率计算实现

```python
def calculate_payout_rate(self, home_odds, draw_odds, away_odds):
    """
    使用博彩公式计算返还率
    公式: 返还率 = 胜赔率 × 平赔率 × 负赔率 / (胜赔率 × 平赔率 + 平赔率 × 负赔率 + 胜赔率 × 负赔率) × 100%
    """
    try:
        h, d, a = float(home_odds), float(draw_odds), float(away_odds)
        numerator = h * d * a
        denominator = h * d + d * a + h * a
        return (numerator / denominator) * 100
    except (ValueError, ZeroDivisionError):
        return None
```

### 2. 区间Y轴映射

```python
INTERVAL_Y_MAPPING = {
    "超深诱盘": 1,
    "超深盘中下水": 2,
    "超深盘中上水": 3,
    "超散盘低水": 4,
    "超散盘中低水": 5,
    "超散盘中水": 6,
    "超散盘中高水": 7,
    "超实盘超低水": 8,
    "超实盘低水": 9,
    "超实盘中低水": 10,
    "超实盘中水": 11,
    "超实盘中高水": 12,
    "超实盘高水": 13,
    "超实盘超高水": 14,
    "低开盘中低水": 15,
    "低开盘中水": 16,
    "低开盘中高水": 17,
    "实开盘超低水": 18,
    "实开盘低水": 19,
    "实开盘中低水": 20,
    "实开盘中水": 21,
    "实开盘中高水": 22,
    "实开盘高水": 23,
    "实开盘超高水": 24,
    "高开盘中低水": 25,
    "高开盘中水": 26,
    "高开盘中高水": 27,
    "韬光盘超低水": 28,
    "韬光盘低水": 29,
    "韬光盘中低水": 30,
    "韬光盘中水": 31,
    "韬光盘中高水": 32,
    "韬光盘高水": 33,
    "超韬盘中下水": 34,
    "超韬盘中上水": 35,
    "超韬利盘": 36
}
```

### 3. 数据库查询优化

- 使用索引优化查询性能
- 分页加载大量历史数据
- 缓存常用查询结果

### 4. 内存管理

- 限制同时处理的数据量
- 及时释放不需要的数据
- 使用生成器处理大数据集