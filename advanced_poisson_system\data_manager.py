"""
数据管理器 - 统一管理历史数据和赔率数据的访问
"""

import sqlite3
import pandas as pd
import os
import sys
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

# 添加父目录到路径以导入现有模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
# 指向football_analysis_system目录
football_system_dir = os.path.join(parent_dir, 'football_analysis_system')
sys.path.insert(0, football_system_dir)

try:
    from analysis.poisson_module import load_match_data, get_available_leagues, calculate_team_stats, calculate_league_averages
except ImportError:
    print("警告：无法导入现有的泊松模块，将使用简化版本")

class DataManager:
    """数据管理器 - 统一数据访问接口"""
    
    def __init__(self, football_db_path=None, odds_db_path=None):
        """
        初始化数据管理器
        
        Args:
            football_db_path: football.db路径（历史数据）
            odds_db_path: matches_and_odds.db路径（赔率数据）
        """
        # 设置默认数据库路径
        if football_db_path is None:
            football_db_path = os.path.join(football_system_dir, 'data', 'football.db')
        if odds_db_path is None:
            odds_db_path = os.path.join(football_system_dir, 'data', 'matches_and_odds.db')
            
        self.football_db_path = football_db_path
        self.odds_db_path = odds_db_path
        
        # 验证数据库文件存在
        self._validate_databases()
    
    def _validate_databases(self):
        """验证数据库文件是否存在"""
        if not os.path.exists(self.football_db_path):
            print(f"警告：找不到历史数据库文件：{self.football_db_path}")
            
        if not os.path.exists(self.odds_db_path):
            print(f"警告：找不到赔率数据库文件：{self.odds_db_path}")
    
    def get_football_connection(self):
        """获取football.db连接"""
        return sqlite3.connect(self.football_db_path)
    
    def get_odds_connection(self):
        """获取matches_and_odds.db连接"""
        return sqlite3.connect(self.odds_db_path)
    
    # ===== 历史数据访问方法 =====
    
    def get_historical_match_data(self, league_name=None, team_name=None, limit=None):
        """
        获取历史比赛数据
        
        Args:
            league_name: 联赛名称过滤
            team_name: 球队名称过滤  
            limit: 限制返回数量
            
        Returns:
            DataFrame: 历史比赛数据
        """
        try:
            with self.get_football_connection() as conn:
                # 利用现有的load_match_data函数
                return load_match_data(conn, league_name)
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_team_historical_stats(self, home_team, away_team, recent_matches=10):
        """
        计算球队历史统计数据
        
        Args:
            home_team: 主队名称
            away_team: 客队名称
            recent_matches: 最近比赛数量
            
        Returns:
            dict: 球队统计数据
        """
        try:
            with self.get_football_connection() as conn:
                # 获取历史数据
                match_data = load_match_data(conn)
                
                if match_data.empty:
                    return self._get_default_team_stats()
                
                # 计算球队统计
                teams = [home_team, away_team]
                team_stats = calculate_team_stats(match_data, teams, recent_matches)
                league_avg_home, league_avg_away = calculate_league_averages(match_data)
                
                return {
                    'team_stats': team_stats,
                    'league_avg_home': league_avg_home,
                    'league_avg_away': league_avg_away,
                    'data_quality': {
                        'total_matches': len(match_data),
                        'home_team_matches': team_stats.get(home_team, {}).get('total_games', 0),
                        'away_team_matches': team_stats.get(away_team, {}).get('total_games', 0)
                    }
                }
        except Exception as e:
            print(f"计算历史统计失败: {e}")
            return self._get_default_team_stats()
    
    def _get_default_team_stats(self):
        """获取默认的球队统计数据"""
        return {
            'team_stats': {},
            'league_avg_home': 1.5,
            'league_avg_away': 1.2,
            'data_quality': {
                'total_matches': 0,
                'home_team_matches': 0,
                'away_team_matches': 0
            }
        }
    
    # ===== 赔率数据访问方法 =====
    
    def get_overunder_odds(self, match_id, line=2.5):
        """
        获取大小球赔率数据
        
        Args:
            match_id: 比赛ID
            line: 盘口线（如2.5）
            
        Returns:
            list: 大小球赔率数据
        """
        try:
            with self.get_odds_connection() as conn:
                cursor = conn.cursor()
                
                # 查询大小球赔率
                query = """
                SELECT company_name, company_id, instant_over, instant_under, 
                       instant_line, line_numeric, update_time
                FROM overunder_odds 
                WHERE match_id = ? 
                AND (instant_line LIKE ? OR line_numeric = ?)
                ORDER BY company_name
                """
                
                cursor.execute(query, (match_id, f"%{line}%", line))
                rows = cursor.fetchall()
                
                # 如果指定盘口没有数据，查询所有可用盘口
                if not rows:
                    available_query = """
                    SELECT DISTINCT line_numeric
                    FROM overunder_odds 
                    WHERE match_id = ? AND line_numeric IS NOT NULL
                    ORDER BY line_numeric
                    """
                    cursor.execute(available_query, (match_id,))
                    available_lines = [row[0] for row in cursor.fetchall()]
                    
                    if available_lines:
                        print(f"⚠️ 没有找到盘口 {line} 的大小球赔率数据")
                        print(f"📊 可用的盘口: {', '.join(map(str, available_lines))}")
                        print(f"💡 建议选择可用的盘口进行分析")
                    else:
                        print(f"❌ 比赛 {match_id} 没有任何大小球赔率数据")
                
                return [
                    {
                        'company_name': row[0],
                        'company_id': row[1],
                        'over_odds': row[2],
                        'under_odds': row[3],
                        'line': row[4],
                        'line_numeric': row[5],
                        'update_time': row[6]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            print(f"获取大小球赔率失败: {e}")
            return []
    
    def get_available_overunder_lines(self, match_id):
        """
        获取比赛的可用大小球盘口列表
        
        Args:
            match_id: 比赛ID
            
        Returns:
            list: 可用的盘口列表
        """
        try:
            with self.get_odds_connection() as conn:
                cursor = conn.cursor()
                
                query = """
                SELECT DISTINCT line_numeric
                FROM overunder_odds 
                WHERE match_id = ? AND line_numeric IS NOT NULL
                ORDER BY line_numeric
                """
                
                cursor.execute(query, (match_id,))
                return [row[0] for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"获取可用盘口失败: {e}")
            return []
    
    def get_1x2_odds(self, match_id):
        """
        获取胜平负赔率数据
        
        Args:
            match_id: 比赛ID
            
        Returns:
            list: 胜平负赔率数据
        """
        try:
            with self.get_odds_connection() as conn:
                cursor = conn.cursor()
                
                # 查询胜平负赔率 - 修正字段名
                query = """
                SELECT bookmaker, instant_home_win, instant_draw, instant_away_win, scrape_time
                FROM odds 
                WHERE match_id = ?
                ORDER BY bookmaker
                """
                
                cursor.execute(query, (match_id,))
                rows = cursor.fetchall()
                
                return [
                    {
                        'company_name': row[0],  # 对外保持一致的字段名
                        'home_win_odds': row[1],
                        'draw_odds': row[2],
                        'away_win_odds': row[3],
                        'update_time': row[4]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            print(f"获取胜平负赔率失败: {e}")
            return []
    
    def get_match_info(self, match_id):
        """
        获取比赛基础信息
        
        Args:
            match_id: 比赛ID
            
        Returns:
            dict: 比赛信息
        """
        try:
            with self.get_odds_connection() as conn:
                cursor = conn.cursor()
                
                # 查询比赛信息 - 修正字段名
                query = """
                SELECT match_id, league_name, home_team, 
                       away_team, start_time
                FROM matches 
                WHERE match_id = ?
                """
                
                cursor.execute(query, (match_id,))
                row = cursor.fetchone()
                
                if row:
                    return {
                        'match_id': row[0],
                        'league_name': row[1],
                        'home_team_name': row[2],  # 对外保持一致的字段名
                        'away_team_name': row[3],  # 对外保持一致的字段名
                        'match_time': row[4]
                    }
                else:
                    return None
                    
        except Exception as e:
            print(f"获取比赛信息失败: {e}")
            return None
    
    def get_overunder_odds_with_fallback(self, match_id, line=2.5):
        """
        获取大小球赔率数据，带智能回退机制
        
        Args:
            match_id: 比赛ID
            line: 首选盘口线（如5.5）
            
        Returns:
            tuple: (赔率数据列表, 实际使用的盘口)
        """
        # 先尝试获取指定盘口的数据
        odds_data = self.get_overunder_odds(match_id, line)
        
        if odds_data:
            return odds_data, line
        
        # 如果没有数据，获取所有可用盘口
        available_lines = self.get_available_overunder_lines(match_id)
        
        if not available_lines:
            print(f"❌ 比赛 {match_id} 没有任何大小球赔率数据")
            return [], line
        
        # 找到最接近的盘口
        closest_line = min(available_lines, key=lambda x: abs(x - line))
        
        print(f"🔄 智能回退: 请求盘口 {line}，使用最接近的可用盘口 {closest_line}")
        
        # 获取最接近盘口的数据
        fallback_odds = self.get_overunder_odds(match_id, closest_line)
        
        return fallback_odds, closest_line
    
    # ===== 数据质量评估方法 =====
    
    def assess_data_availability(self, match_id, home_team, away_team):
        """
        评估数据可用性
        
        Args:
            match_id: 比赛ID
            home_team: 主队名称
            away_team: 客队名称
            
        Returns:
            dict: 数据可用性评估
        """
        assessment = {
            'match_info': False,
            'overunder_odds': False,
            '1x2_odds': False,
            'historical_data': False,
            'data_quality_score': 0
        }
        
        # 检查比赛信息
        match_info = self.get_match_info(match_id)
        if match_info:
            assessment['match_info'] = True
            assessment['data_quality_score'] += 1
        
        # 检查大小球赔率
        overunder_data = self.get_overunder_odds(match_id)
        if overunder_data:
            assessment['overunder_odds'] = True
            assessment['data_quality_score'] += 2  # 权重更高
        
        # 检查胜平负赔率
        odds_1x2_data = self.get_1x2_odds(match_id)
        if odds_1x2_data:
            assessment['1x2_odds'] = True
            assessment['data_quality_score'] += 2  # 权重更高
        
        # 检查历史数据
        historical_stats = self.calculate_team_historical_stats(home_team, away_team)
        if historical_stats['data_quality']['total_matches'] > 0:
            assessment['historical_data'] = True
            assessment['data_quality_score'] += 1
        
        # 计算总分（满分6分）
        assessment['data_quality_percentage'] = assessment['data_quality_score'] / 6 * 100
        
        return assessment
    
    def get_available_bookmakers(self, match_id):
        """
        获取可用的博彩公司列表
        
        Args:
            match_id: 比赛ID
            
        Returns:
            dict: 各类型赔率的可用博彩公司
        """
        overunder_companies = set()
        odds_1x2_companies = set()
        
        # 大小球博彩公司
        overunder_data = self.get_overunder_odds(match_id)
        for item in overunder_data:
            overunder_companies.add(item['company_name'])
        
        # 胜平负博彩公司
        odds_1x2_data = self.get_1x2_odds(match_id)
        for item in odds_1x2_data:
            odds_1x2_companies.add(item['company_name'])
        
        # 共同的博彩公司
        common_companies = overunder_companies.intersection(odds_1x2_companies)
        
        return {
            'overunder_companies': list(overunder_companies),
            '1x2_companies': list(odds_1x2_companies),
            'common_companies': list(common_companies),
            'total_unique_companies': len(overunder_companies.union(odds_1x2_companies))
        }

if __name__ == "__main__":
    # 测试数据管理器
    dm = DataManager()
    
    # 测试比赛ID 2702969
    match_id = 2702969
    
    print("=== 数据管理器测试 ===")
    
    # 测试比赛信息获取
    match_info = dm.get_match_info(match_id)
    print(f"比赛信息: {match_info}")
    
    # 测试大小球赔率获取
    overunder_data = dm.get_overunder_odds(match_id)
    print(f"大小球赔率数量: {len(overunder_data)}")
    if overunder_data:
        print(f"示例赔率: {overunder_data[0]}")
    
    # 测试胜平负赔率获取
    odds_1x2_data = dm.get_1x2_odds(match_id)
    print(f"胜平负赔率数量: {len(odds_1x2_data)}")
    if odds_1x2_data:
        print(f"示例赔率: {odds_1x2_data[0]}")
    
    # 测试数据可用性评估
    if match_info:
        assessment = dm.assess_data_availability(
            match_id, 
            match_info.get('home_team_name', '主队'), 
            match_info.get('away_team_name', '客队')
        )
        print(f"数据可用性评估: {assessment}")
    
    # 测试博彩公司列表
    bookmakers = dm.get_available_bookmakers(match_id)
    print(f"可用博彩公司: {bookmakers}") 