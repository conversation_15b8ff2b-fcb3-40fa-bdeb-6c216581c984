"""
异常处理器模块
提供统一的异常处理、错误记录和恢复机制
"""

import logging
import traceback
import functools
from typing import Any, Callable, Dict, Optional, Type, Union
from enum import Enum

from .validators import ValidationError
from .logger_manager import get_logger


class ErrorSeverity(Enum):
    """错误严重级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SystemError(Exception):
    """系统错误基类"""
    
    def __init__(self, message: str, error_code: str = None, 
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 context: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or "SYSTEM_ERROR"
        self.severity = severity
        self.context = context or {}
        super().__init__(message)


class DatabaseError(SystemError):
    """数据库错误"""
    
    def __init__(self, message: str, operation: str = None, **kwargs):
        super().__init__(message, error_code="DB_ERROR", **kwargs)
        self.operation = operation


class NetworkError(SystemError):
    """网络错误"""
    
    def __init__(self, message: str, url: str = None, **kwargs):
        super().__init__(message, error_code="NETWORK_ERROR", **kwargs)
        self.url = url


class APIError(SystemError):
    """API错误"""
    
    def __init__(self, message: str, api_name: str = None, status_code: int = None, **kwargs):
        super().__init__(message, error_code="API_ERROR", **kwargs)
        self.api_name = api_name
        self.status_code = status_code


class ConfigurationError(SystemError):
    """配置错误"""
    
    def __init__(self, message: str, config_key: str = None, **kwargs):
        super().__init__(message, error_code="CONFIG_ERROR", severity=ErrorSeverity.HIGH, **kwargs)
        self.config_key = config_key


class ExceptionHandler:
    """异常处理器"""
    
    def __init__(self, logger_name: str = "exception_handler"):
        self.logger = get_logger(logger_name)
        self.error_counts = {}
        self.circuit_breaker_thresholds = {
            ErrorSeverity.LOW: 100,
            ErrorSeverity.MEDIUM: 50,
            ErrorSeverity.HIGH: 10,
            ErrorSeverity.CRITICAL: 5
        }
    
    def handle_exception(self, exc: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理异常
        
        Args:
            exc: 异常对象
            context: 上下文信息
            
        Returns:
            错误信息字典
        """
        context = context or {}
        
        # 获取异常信息
        error_info = self._extract_error_info(exc, context)
        
        # 记录异常
        self._log_exception(error_info)
        
        # 更新错误计数
        self._update_error_count(error_info)
        
        # 检查是否需要熔断
        if self._should_circuit_break(error_info):
            self.logger.critical(f"达到熔断阈值: {error_info['error_type']}")
        
        return error_info
    
    def _extract_error_info(self, exc: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """提取异常信息"""
        if isinstance(exc, SystemError):
            severity = exc.severity
            error_code = exc.error_code
            error_context = exc.context
        elif isinstance(exc, ValidationError):
            severity = ErrorSeverity.MEDIUM
            error_code = "VALIDATION_ERROR"
            error_context = {"field": exc.field, "value": exc.value}
        elif isinstance(exc, (ConnectionError, TimeoutError)):
            severity = ErrorSeverity.HIGH
            error_code = "CONNECTION_ERROR"
            error_context = {}
        elif isinstance(exc, KeyError):
            severity = ErrorSeverity.MEDIUM
            error_code = "KEY_ERROR"
            error_context = {"missing_key": str(exc)}
        elif isinstance(exc, ValueError):
            severity = ErrorSeverity.MEDIUM
            error_code = "VALUE_ERROR"
            error_context = {}
        elif isinstance(exc, FileNotFoundError):
            severity = ErrorSeverity.HIGH
            error_code = "FILE_NOT_FOUND"
            error_context = {"filename": exc.filename}
        elif isinstance(exc, PermissionError):
            severity = ErrorSeverity.HIGH
            error_code = "PERMISSION_ERROR"
            error_context = {"filename": exc.filename}
        else:
            severity = ErrorSeverity.MEDIUM
            error_code = "UNKNOWN_ERROR"
            error_context = {}
        
        return {
            "error_type": type(exc).__name__,
            "error_code": error_code,
            "message": str(exc),
            "severity": severity,
            "traceback": traceback.format_exc(),
            "context": {**error_context, **context}
        }
    
    def _log_exception(self, error_info: Dict[str, Any]):
        """记录异常日志"""
        severity = error_info["severity"]
        message = f"[{error_info['error_code']}] {error_info['message']}"
        
        if severity == ErrorSeverity.CRITICAL:
            self.logger.critical(message, extra=error_info)
        elif severity == ErrorSeverity.HIGH:
            self.logger.error(message, extra=error_info)
        elif severity == ErrorSeverity.MEDIUM:
            self.logger.warning(message, extra=error_info)
        else:
            self.logger.info(message, extra=error_info)
        
        # 详细的异常信息记录到debug级别
        self.logger.debug(f"异常详情: {error_info}")
    
    def _update_error_count(self, error_info: Dict[str, Any]):
        """更新错误计数"""
        error_key = f"{error_info['error_type']}_{error_info['severity'].value}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
    
    def _should_circuit_break(self, error_info: Dict[str, Any]) -> bool:
        """检查是否应该熔断"""
        severity = error_info["severity"]
        error_key = f"{error_info['error_type']}_{severity.value}"
        count = self.error_counts.get(error_key, 0)
        threshold = self.circuit_breaker_thresholds.get(severity, 50)
        
        return count >= threshold
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return {
            "error_counts": self.error_counts.copy(),
            "thresholds": {k.value: v for k, v in self.circuit_breaker_thresholds.items()},
            "total_errors": sum(self.error_counts.values())
        }
    
    def reset_error_counts(self):
        """重置错误计数"""
        self.error_counts.clear()
        self.logger.info("错误计数已重置")


# 全局异常处理器实例
exception_handler = ExceptionHandler()


def handle_exceptions(
    fallback_value: Any = None,
    reraise: bool = False,
    log_context: Dict[str, Any] = None
):
    """
    异常处理装饰器
    
    Args:
        fallback_value: 发生异常时的返回值
        reraise: 是否重新抛出异常
        log_context: 日志上下文信息
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = log_context or {}
                context.update({
                    "function": func.__name__,
                    "args": str(args)[:200],  # 限制长度避免日志过长
                    "kwargs": str(kwargs)[:200]
                })
                
                exception_handler.handle_exception(e, context)
                
                if reraise:
                    raise
                
                return fallback_value
        
        return wrapper
    return decorator


def safe_execute(
    func: Callable,
    *args,
    fallback_value: Any = None,
    max_retries: int = 3,
    retry_delay: float = 1.0,
    **kwargs
) -> Any:
    """
    安全执行函数，包含重试机制
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        fallback_value: 失败时的返回值
        max_retries: 最大重试次数
        retry_delay: 重试延迟（秒）
        **kwargs: 函数关键字参数
        
    Returns:
        函数执行结果或fallback_value
    """
    import time
    
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            last_exception = e
            
            context = {
                "function": func.__name__,
                "attempt": attempt + 1,
                "max_retries": max_retries
            }
            
            if attempt < max_retries:
                exception_handler.logger.warning(
                    f"执行失败，{retry_delay}秒后重试 (尝试 {attempt + 1}/{max_retries + 1}): {e}"
                )
                time.sleep(retry_delay)
            else:
                exception_handler.handle_exception(e, context)
    
    return fallback_value


def create_error_response(
    success: bool = False,
    message: str = "",
    data: Any = None,
    error_code: str = None
) -> Dict[str, Any]:
    """
    创建标准错误响应
    
    Args:
        success: 是否成功
        message: 响应消息
        data: 响应数据
        error_code: 错误代码
        
    Returns:
        标准响应字典
    """
    response = {
        "success": success,
        "message": message,
        "data": data
    }
    
    if error_code:
        response["error_code"] = error_code
    
    return response


def validate_and_handle(validator_func: Callable, data: Any, context: str = "") -> Any:
    """
    验证数据并处理异常
    
    Args:
        validator_func: 验证函数
        data: 待验证的数据
        context: 上下文描述
        
    Returns:
        验证后的数据
        
    Raises:
        ValidationError: 验证失败时抛出
    """
    try:
        return validator_func(data)
    except ValidationError as e:
        exception_handler.handle_exception(e, {"context": context})
        raise
    except Exception as e:
        exception_handler.handle_exception(e, {"context": context, "validator": validator_func.__name__})
        raise ValidationError(f"数据验证过程中发生错误: {str(e)}")