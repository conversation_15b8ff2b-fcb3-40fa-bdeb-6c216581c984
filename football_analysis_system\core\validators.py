"""
输入验证器模块
提供各种输入验证功能，确保数据安全性和完整性
"""

import re
import os
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, date
from pathlib import Path


class ValidationError(Exception):
    """验证错误异常类"""
    
    def __init__(self, message: str, field: str = None, value: Any = None):
        self.message = message
        self.field = field
        self.value = value
        super().__init__(message)


class InputValidator:
    """输入验证器类"""
    
    # 正则表达式模式
    PATTERNS = {
        'match_id': r'^[0-9]+$',
        'team_name': r'^[a-zA-Z0-9\u4e00-\u9fa5\s\-_\.]+$',  # 支持中文、英文、数字、空格、横线、下划线、点
        'league_name': r'^[a-zA-Z0-9\u4e00-\u9fa5\s\-_\.]+$',
        'date': r'^\d{4}-\d{2}-\d{2}$',
        'datetime': r'^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}$',
        'odds_value': r'^\d+\.?\d*$',
        'table_name': r'^[a-zA-Z_][a-zA-Z0-9_]*$',  # SQL表名规范
        'db_name': r'^[a-zA-Z0-9_]+$',
        'api_key': r'^sk-[a-zA-Z0-9]{48}$',  # DeepSeek API key格式
        'url': r'^https?://[^\s/$.?#].[^\s]*$'
    }
    
    @staticmethod
    def validate_string(value: Any, field_name: str, min_length: int = 1, 
                       max_length: int = 255, pattern: str = None, 
                       allow_empty: bool = False) -> str:
        """
        验证字符串输入
        
        Args:
            value: 待验证的值
            field_name: 字段名称
            min_length: 最小长度
            max_length: 最大长度
            pattern: 正则表达式模式名称
            allow_empty: 是否允许空值
            
        Returns:
            验证后的字符串
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        if value is None:
            if allow_empty:
                return ""
            raise ValidationError(f"{field_name}不能为空", field_name, value)
        
        if not isinstance(value, str):
            value = str(value)
        
        # 去除首尾空格
        value = value.strip()
        
        if not value and not allow_empty:
            raise ValidationError(f"{field_name}不能为空", field_name, value)
        
        if len(value) < min_length:
            raise ValidationError(
                f"{field_name}长度不能少于{min_length}个字符", 
                field_name, value
            )
        
        if len(value) > max_length:
            raise ValidationError(
                f"{field_name}长度不能超过{max_length}个字符", 
                field_name, value
            )
        
        # 模式验证
        if pattern and pattern in InputValidator.PATTERNS:
            if not re.match(InputValidator.PATTERNS[pattern], value):
                raise ValidationError(
                    f"{field_name}格式不正确", 
                    field_name, value
                )
        
        return value
    
    @staticmethod
    def validate_integer(value: Any, field_name: str, min_value: int = None, 
                        max_value: int = None, allow_none: bool = False) -> Optional[int]:
        """
        验证整数输入
        
        Args:
            value: 待验证的值
            field_name: 字段名称
            min_value: 最小值
            max_value: 最大值
            allow_none: 是否允许None
            
        Returns:
            验证后的整数
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        if value is None:
            if allow_none:
                return None
            raise ValidationError(f"{field_name}不能为空", field_name, value)
        
        try:
            value = int(value)
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name}必须是整数", field_name, value)
        
        if min_value is not None and value < min_value:
            raise ValidationError(
                f"{field_name}不能小于{min_value}", 
                field_name, value
            )
        
        if max_value is not None and value > max_value:
            raise ValidationError(
                f"{field_name}不能大于{max_value}", 
                field_name, value
            )
        
        return value
    
    @staticmethod
    def validate_float(value: Any, field_name: str, min_value: float = None, 
                      max_value: float = None, allow_none: bool = False) -> Optional[float]:
        """
        验证浮点数输入
        
        Args:
            value: 待验证的值
            field_name: 字段名称
            min_value: 最小值
            max_value: 最大值
            allow_none: 是否允许None
            
        Returns:
            验证后的浮点数
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        if value is None:
            if allow_none:
                return None
            raise ValidationError(f"{field_name}不能为空", field_name, value)
        
        try:
            value = float(value)
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name}必须是数字", field_name, value)
        
        if min_value is not None and value < min_value:
            raise ValidationError(
                f"{field_name}不能小于{min_value}", 
                field_name, value
            )
        
        if max_value is not None and value > max_value:
            raise ValidationError(
                f"{field_name}不能大于{max_value}", 
                field_name, value
            )
        
        return value
    
    @staticmethod
    def validate_date(value: Any, field_name: str, allow_none: bool = False) -> Optional[date]:
        """
        验证日期输入
        
        Args:
            value: 待验证的值
            field_name: 字段名称
            allow_none: 是否允许None
            
        Returns:
            验证后的日期对象
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        if value is None:
            if allow_none:
                return None
            raise ValidationError(f"{field_name}不能为空", field_name, value)
        
        if isinstance(value, date):
            return value
        
        if isinstance(value, str):
            # 尝试解析日期字符串
            for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%d/%m/%Y', '%d-%m-%Y']:
                try:
                    return datetime.strptime(value, fmt).date()
                except ValueError:
                    continue
        
        raise ValidationError(f"{field_name}日期格式不正确", field_name, value)
    
    @staticmethod
    def validate_odds(value: Any, field_name: str, allow_none: bool = False) -> Optional[float]:
        """
        验证赔率值
        
        Args:
            value: 待验证的值
            field_name: 字段名称
            allow_none: 是否允许None
            
        Returns:
            验证后的赔率值
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        if value is None:
            if allow_none:
                return None
            raise ValidationError(f"{field_name}不能为空", field_name, value)
        
        odds = InputValidator.validate_float(value, field_name, 1.0, 100.0)
        
        # 赔率通常在1.0到100.0之间
        if odds < 1.0:
            raise ValidationError(f"{field_name}赔率不能小于1.0", field_name, value)
        
        if odds > 100.0:
            raise ValidationError(f"{field_name}赔率不能大于100.0", field_name, value)
        
        return odds
    
    @staticmethod
    def validate_file_path(value: Any, field_name: str, must_exist: bool = False, 
                          allowed_extensions: List[str] = None) -> str:
        """
        验证文件路径
        
        Args:
            value: 待验证的值
            field_name: 字段名称
            must_exist: 文件是否必须存在
            allowed_extensions: 允许的文件扩展名列表
            
        Returns:
            验证后的文件路径
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        path_str = InputValidator.validate_string(value, field_name)
        
        # 检查路径是否安全（防止路径遍历攻击）
        if '..' in path_str or path_str.startswith('/'):
            if not os.path.abspath(path_str).startswith(os.getcwd()):
                raise ValidationError(f"{field_name}包含不安全的路径", field_name, value)
        
        path = Path(path_str)
        
        if must_exist and not path.exists():
            raise ValidationError(f"{field_name}文件不存在", field_name, value)
        
        if allowed_extensions:
            if path.suffix.lower() not in [ext.lower() for ext in allowed_extensions]:
                raise ValidationError(
                    f"{field_name}文件扩展名必须是: {', '.join(allowed_extensions)}", 
                    field_name, value
                )
        
        return str(path)
    
    @staticmethod
    def validate_api_key(value: Any, field_name: str = "API密钥") -> str:
        """
        验证API密钥格式
        
        Args:
            value: 待验证的值
            field_name: 字段名称
            
        Returns:
            验证后的API密钥
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        api_key = InputValidator.validate_string(
            value, field_name, min_length=50, max_length=100, pattern='api_key'
        )
        return api_key
    
    @staticmethod
    def validate_sql_identifier(value: Any, field_name: str) -> str:
        """
        验证SQL标识符（表名、列名等）
        
        Args:
            value: 待验证的值
            field_name: 字段名称
            
        Returns:
            验证后的标识符
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        identifier = InputValidator.validate_string(
            value, field_name, min_length=1, max_length=64, pattern='table_name'
        )
        
        # 检查是否为SQL保留字
        sql_keywords = {
            'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER',
            'TABLE', 'INDEX', 'VIEW', 'TRIGGER', 'PROCEDURE', 'FUNCTION',
            'WHERE', 'ORDER', 'GROUP', 'HAVING', 'JOIN', 'UNION', 'AND', 'OR',
            'NOT', 'NULL', 'TRUE', 'FALSE', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END'
        }
        
        if identifier.upper() in sql_keywords:
            raise ValidationError(f"{field_name}不能使用SQL保留字", field_name, value)
        
        return identifier


class MatchValidator:
    """比赛数据验证器"""
    
    @staticmethod
    def validate_match_data(match_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证比赛数据
        
        Args:
            match_data: 比赛数据字典
            
        Returns:
            验证后的比赛数据
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        validator = InputValidator()
        validated_data = {}
        
        # 验证必填字段
        required_fields = ['match_id', 'home_team', 'away_team', 'league_name']
        for field in required_fields:
            if field not in match_data:
                raise ValidationError(f"缺少必填字段: {field}")
        
        # 验证各个字段
        validated_data['match_id'] = validator.validate_string(
            match_data['match_id'], '比赛ID', pattern='match_id'
        )
        
        validated_data['home_team'] = validator.validate_string(
            match_data['home_team'], '主队名称', min_length=1, max_length=100, pattern='team_name'
        )
        
        validated_data['away_team'] = validator.validate_string(
            match_data['away_team'], '客队名称', min_length=1, max_length=100, pattern='team_name'
        )
        
        validated_data['league_name'] = validator.validate_string(
            match_data['league_name'], '联赛名称', min_length=1, max_length=100, pattern='league_name'
        )
        
        # 验证可选字段
        if 'match_time' in match_data:
            validated_data['match_time'] = validator.validate_date(
                match_data['match_time'], '比赛时间', allow_none=True
            )
        
        if 'home_score' in match_data:
            validated_data['home_score'] = validator.validate_integer(
                match_data['home_score'], '主队得分', min_value=0, allow_none=True
            )
        
        if 'away_score' in match_data:
            validated_data['away_score'] = validator.validate_integer(
                match_data['away_score'], '客队得分', min_value=0, allow_none=True
            )
        
        return validated_data


class OddsValidator:
    """赔率数据验证器"""
    
    @staticmethod
    def validate_odds_data(odds_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证赔率数据
        
        Args:
            odds_data: 赔率数据字典
            
        Returns:
            验证后的赔率数据
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        validator = InputValidator()
        validated_data = {}
        
        # 验证必填字段
        required_fields = ['match_id', 'bookmaker']
        for field in required_fields:
            if field not in odds_data:
                raise ValidationError(f"缺少必填字段: {field}")
        
        validated_data['match_id'] = validator.validate_string(
            odds_data['match_id'], '比赛ID', pattern='match_id'
        )
        
        validated_data['bookmaker'] = validator.validate_string(
            odds_data['bookmaker'], '博彩公司', min_length=1, max_length=50
        )
        
        # 验证赔率值
        odds_fields = ['home_win', 'draw', 'away_win']
        for field in odds_fields:
            if field in odds_data:
                validated_data[field] = validator.validate_odds(
                    odds_data[field], f"{field}赔率", allow_none=True
                )
        
        # 验证赔率总和合理性（通常应该接近但不等于各概率倒数之和）
        if all(field in validated_data for field in odds_fields):
            total_implied_prob = sum(1/validated_data[field] for field in odds_fields)
            if total_implied_prob < 0.8 or total_implied_prob > 1.5:
                raise ValidationError("赔率值不合理，隐含概率总和异常")
        
        return validated_data