# Implementation Plan

- [x] 1. 创建路径解析器核心组件





  - 实现PathResolver类，提供统一的数据库路径解析功能
  - 集成到现有的ConfigManager系统中
  - 添加相对路径到绝对路径的自动转换功能
  - _Requirements: 1.1, 2.1, 5.1, 5.3_

- [ ] 2. 实现数据库迁移工具
  - 创建DatabaseMigrator类，支持外层数据到内层数据库的迁移
  - 实现数据完整性验证和重复数据处理机制
  - 添加迁移状态跟踪和错误处理功能
  - _Requirements: 3.1, 3.2, 4.2_

- [x] 3. 修复OddsScraper的数据库路径配置





  - 更新OddsScraper构造函数，使用PathResolver解析数据库路径
  - 保持向后兼容性，确保现有调用方式仍然有效
  - 添加路径解析的调试日志和错误处理
  - _Requirements: 2.1, 2.2, 5.1_

- [ ] 4. 批量修复测试文件的硬编码路径
  - 更新所有使用"data/matches_and_odds.db"硬编码路径的测试文件
  - 替换为PathResolver.get_database_path()调用
  - 验证测试功能保持正常工作
  - _Requirements: 2.2, 2.3_

- [ ] 5. 修复其他组件的硬编码路径
  - 更新gamedetail_parser.py、final_test.py、enhanced_odds_scraper.py等文件
  - 替换硬编码的数据库路径为配置化路径
  - 确保所有组件使用统一的路径获取方式
  - _Requirements: 2.2, 2.3, 5.1_

- [ ] 6. 执行数据迁移操作
  - 运行DatabaseMigrator将外层数据合并到内层数据库
  - 验证迁移后的数据完整性和一致性
  - 生成迁移报告，记录迁移的详细信息
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 7. 实现数据库路径验证和测试
  - 创建全面的单元测试覆盖PathResolver和DatabaseMigrator
  - 实现集成测试验证整个路径解析流程
  - 添加回归测试确保现有功能不受影响
  - _Requirements: 3.3, 5.2_

- [ ] 8. 清理外层data目录和最终验证
  - 提供安全的外层data目录清理功能
  - 执行全面的功能测试验证所有组件正常工作
  - 更新相关文档和配置说明
  - _Requirements: 4.1, 4.3_