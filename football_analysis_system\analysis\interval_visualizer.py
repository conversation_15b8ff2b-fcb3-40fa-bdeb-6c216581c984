"""
区间可视化组件模块

该模块负责将区间类型映射到Y轴坐标，用于历史赔率区间分析的可视化。
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass


@dataclass
class VisualizationData:
    """可视化数据模型"""
    
    timestamps: List[str]
    home_y_coords: List[int]
    draw_y_coords: List[int]
    away_y_coords: List[int]
    interval_labels: Dict[int, str]  # Y坐标到区间名称的映射


class IntervalVisualizer:
    """区间可视化组件"""
    
    # 区间类型到Y轴坐标的映射
    INTERVAL_Y_MAPPING = {
        "超深诱盘": 1,
        "超深盘中下水": 2,
        "超深盘中上水": 3,
        "超散盘低水": 4,
        "超散盘中低水": 5,
        "超散盘中水": 6,
        "超散盘中高水": 7,
        "超实盘超低水": 8,
        "超实盘低水": 9,
        "超实盘中低水": 10,
        "超实盘中水": 11,
        "超实盘中高水": 12,
        "超实盘高水": 13,
        "超实盘超高水": 14,
        "低开盘中低水": 15,
        "低开盘中水": 16,
        "低开盘中高水": 17,
        "实开盘超低水": 18,
        "实开盘低水": 19,
        "实开盘中低水": 20,
        "实开盘中水": 21,
        "实开盘中高水": 22,
        "实开盘高水": 23,
        "实开盘超高水": 24,
        "高开盘中低水": 25,
        "高开盘中水": 26,
        "高开盘中高水": 27,
        "韬光盘超低水": 28,
        "韬光盘低水": 29,
        "韬光盘中低水": 30,
        "韬光盘中水": 31,
        "韬光盘中高水": 32,
        "韬光盘高水": 33,
        "超韬盘中下水": 34,
        "超韬盘中上水": 35,
        "超韬利盘": 36,
        # 平局区间映射
        "平局区间": 37,
        "超深平": 38,
        "超散平": 39,
        "超实平": 40,
        "低开平": 41,
        "平实低": 42,
        "平中庸下": 43,
        "平中庸上": 44,
        "平实高": 45,
        "高开平": 46,
        "韬光平": 47,
        "超韬平": 48,
        # 未知区间 - 使用不同的Y坐标值避免重复
        "未知": 49,
        "未知区间": 50,
        "未找到对应区间": 51
    }
    
    def __init__(self):
        """初始化可视化组件"""
        self.logger = logging.getLogger(__name__)
        
        # 创建反向映射（Y坐标到区间名称）
        self.y_to_interval_mapping = {v: k for k, v in self.INTERVAL_Y_MAPPING.items()}
        
    def map_interval_to_y_coordinate(self, interval_type: str) -> int:
        """
        将区间类型映射为Y轴坐标
        
        Args:
            interval_type: 区间类型字符串
            
        Returns:
            int: Y轴坐标值
        """
        if not interval_type:
            return self.INTERVAL_Y_MAPPING["未知区间"]
            
        # 直接查找映射
        y_coord = self.INTERVAL_Y_MAPPING.get(interval_type)
        
        if y_coord is None:
            self.logger.warning(f"未找到区间类型 '{interval_type}' 的Y轴映射，使用未知区间")
            return self.INTERVAL_Y_MAPPING["未知区间"]
            
        return y_coord
    
    def get_interval_name_by_y_coord(self, y_coord: int) -> str:
        """
        根据Y轴坐标获取区间名称
        
        Args:
            y_coord: Y轴坐标
            
        Returns:
            str: 区间名称
        """
        return self.y_to_interval_mapping.get(y_coord, "未知")
    
    def create_visualization_data(self, analysis_results: List[Any]) -> VisualizationData:
        """
        创建可视化数据
        
        Args:
            analysis_results: 区间分析结果列表
            
        Returns:
            VisualizationData: 可视化数据对象
        """
        timestamps = []
        home_y_coords = []
        draw_y_coords = []
        away_y_coords = []
        
        for result in analysis_results:
            try:
                # 提取时间戳
                timestamps.append(result.timestamp)
                
                # 映射区间类型到Y坐标
                home_y = self.map_interval_to_y_coordinate(result.home_interval)
                draw_y = self.map_interval_to_y_coordinate(result.draw_interval)
                away_y = self.map_interval_to_y_coordinate(result.away_interval)
                
                home_y_coords.append(home_y)
                draw_y_coords.append(draw_y)
                away_y_coords.append(away_y)
                
            except Exception as e:
                self.logger.error(f"处理分析结果时出错: {e}, 结果: {result}")
                # 添加默认值以保持数据一致性
                timestamps.append(getattr(result, 'timestamp', ''))
                home_y_coords.append(0)
                draw_y_coords.append(0)
                away_y_coords.append(0)
        
        return VisualizationData(
            timestamps=timestamps,
            home_y_coords=home_y_coords,
            draw_y_coords=draw_y_coords,
            away_y_coords=away_y_coords,
            interval_labels=self.y_to_interval_mapping
        )
    
    def get_y_axis_labels(self) -> Dict[int, str]:
        """
        获取Y轴标签映射
        
        Returns:
            dict: Y坐标到区间名称的映射
        """
        return self.y_to_interval_mapping.copy()
    
    def get_interval_categories(self) -> Dict[str, List[str]]:
        """
        获取区间分类
        
        Returns:
            dict: 区间分类字典
        """
        categories = {
            "超深盘": ["超深诱盘", "超深盘中下水", "超深盘中上水"],
            "超散盘": ["超散盘低水", "超散盘中低水", "超散盘中水", "超散盘中高水"],
            "超实盘": ["超实盘超低水", "超实盘低水", "超实盘中低水", "超实盘中水", 
                     "超实盘中高水", "超实盘高水", "超实盘超高水"],
            "低开盘": ["低开盘中低水", "低开盘中水", "低开盘中高水"],
            "实开盘": ["实开盘超低水", "实开盘低水", "实开盘中低水", "实开盘中水", 
                     "实开盘中高水", "实开盘高水", "实开盘超高水"],
            "高开盘": ["高开盘中低水", "高开盘中水", "高开盘中高水"],
            "韬光盘": ["韬光盘超低水", "韬光盘低水", "韬光盘中低水", "韬光盘中水", 
                     "韬光盘中高水", "韬光盘高水"],
            "超韬盘": ["超韬盘中下水", "超韬盘中上水", "超韬利盘"],
            "平局区间": ["平局区间", "超深平", "超散平", "超实平", "低开平", 
                       "平实低", "平中庸下", "平中庸上", "平实高", "高开平", "韬光平", "超韬平"]
        }
        return categories
    
    def validate_interval_mapping(self) -> bool:
        """
        验证区间映射的完整性
        
        Returns:
            bool: 映射是否有效
        """
        try:
            # 检查是否有重复的Y坐标值
            y_values = list(self.INTERVAL_Y_MAPPING.values())
            if len(y_values) != len(set(y_values)):
                duplicates = [y for y in set(y_values) if y_values.count(y) > 1]
                self.logger.error(f"发现重复的Y坐标值: {duplicates}")
                return False
            
            # 检查反向映射是否正确
            for interval, y_coord in self.INTERVAL_Y_MAPPING.items():
                if self.y_to_interval_mapping.get(y_coord) != interval:
                    self.logger.error(f"反向映射不一致: {interval} -> {y_coord}")
                    return False
            
            self.logger.info("区间映射验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"验证区间映射时出错: {e}")
            return False
    
    def get_color_mapping(self) -> Dict[str, str]:
        """
        获取区间类型的颜色映射
        
        Returns:
            dict: 区间类型到颜色的映射
        """
        color_mapping = {
            # 超深盘 - 红色系
            "超深诱盘": "#8B0000",
            "超深盘中下水": "#DC143C",
            "超深盘中上水": "#FF6347",
            
            # 超散盘 - 橙色系
            "超散盘低水": "#FF4500",
            "超散盘中低水": "#FF6600",
            "超散盘中水": "#FF8C00",
            "超散盘中高水": "#FFA500",
            
            # 超实盘 - 黄色系
            "超实盘超低水": "#FFD700",
            "超实盘低水": "#FFFF00",
            "超实盘中低水": "#FFFF66",
            "超实盘中水": "#FFFF99",
            "超实盘中高水": "#FFFFCC",
            "超实盘高水": "#F0E68C",
            "超实盘超高水": "#BDB76B",
            
            # 低开盘 - 绿色系
            "低开盘中低水": "#9ACD32",
            "低开盘中水": "#32CD32",
            "低开盘中高水": "#00FF32",
            
            # 实开盘 - 青色系
            "实开盘超低水": "#00FFFF",
            "实开盘低水": "#00CED1",
            "实开盘中低水": "#20B2AA",
            "实开盘中水": "#48D1CC",
            "实开盘中高水": "#40E0D0",
            "实开盘高水": "#00BFFF",
            "实开盘超高水": "#87CEEB",
            
            # 高开盘 - 蓝色系
            "高开盘中低水": "#4169E1",
            "高开盘中水": "#0000FF",
            "高开盘中高水": "#0000CD",
            
            # 韬光盘 - 紫色系
            "韬光盘超低水": "#9370DB",
            "韬光盘低水": "#8A2BE2",
            "韬光盘中低水": "#9932CC",
            "韬光盘中水": "#BA55D3",
            "韬光盘中高水": "#DA70D6",
            "韬光盘高水": "#DDA0DD",
            
            # 超韬盘 - 深紫色系
            "超韬盘中下水": "#4B0082",
            "超韬盘中上水": "#6A0DAD",
            "超韬利盘": "#800080",
            
            # 平局区间 - 灰色系
            "平局区间": "#808080",
            "超深平": "#696969",
            "超散平": "#778899",
            "超实平": "#708090",
            "低开平": "#2F4F4F",
            "平实低": "#556B2F",
            "平中庸下": "#8FBC8F",
            "平中庸上": "#90EE90",
            "平实高": "#98FB98",
            "高开平": "#F0FFF0",
            "韬光平": "#E6E6FA",
            "超韬平": "#D8BFD8",
            
            # 未知区间
            "未知": "#000000",
            "未知区间": "#000000",
            "未找到对应区间": "#000000"
        }
        return color_mapping