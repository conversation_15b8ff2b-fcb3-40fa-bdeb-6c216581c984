"""
统一工具类
提供项目中常用的工具函数和实用方法
"""

import os
import json
import shutil
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import tempfile
import threading
import time

from .config_manager import config_manager
from .logger_manager import get_logger


class FileManager:
    """文件管理工具"""
    
    @staticmethod
    def ensure_directory(path: Union[str, Path]) -> Path:
        """确保目录存在"""
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def move_to_temp(file_path: Union[str, Path]) -> str:
        """将文件移动到临时目录"""
        file_path = Path(file_path)
        if not file_path.exists():
            return ""
        
        temp_dir = Path(config_manager.get('paths.temp_dir'))
        FileManager.ensure_directory(temp_dir)
        
        # 生成唯一的临时文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        temp_file = temp_dir / f"{file_path.stem}_{timestamp}{file_path.suffix}"
        
        try:
            shutil.move(str(file_path), str(temp_file))
            return str(temp_file)
        except Exception as e:
            get_logger().error(f"移动文件到临时目录失败: {e}")
            return ""
    
    @staticmethod
    def backup_file(file_path: Union[str, Path], backup_dir: Optional[str] = None) -> str:
        """备份文件"""
        file_path = Path(file_path)
        if not file_path.exists():
            return ""
        
        if backup_dir is None:
            backup_dir = config_manager.get('paths.backup_dir')
        
        backup_dir = Path(backup_dir)
        FileManager.ensure_directory(backup_dir)
        
        # 生成备份文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = backup_dir / f"{file_path.stem}_{timestamp}{file_path.suffix}"
        
        try:
            shutil.copy2(str(file_path), str(backup_file))
            return str(backup_file)
        except Exception as e:
            get_logger().error(f"备份文件失败: {e}")
            return ""
    
    @staticmethod
    def cleanup_old_files(directory: Union[str, Path], days: int = 7, pattern: str = "*"):
        """清理旧文件"""
        directory = Path(directory)
        if not directory.exists():
            return
        
        cutoff_time = datetime.now() - timedelta(days=days)
        
        for file_path in directory.glob(pattern):
            if file_path.is_file():
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_time:
                    try:
                        file_path.unlink()
                        get_logger().debug(f"删除旧文件: {file_path}")
                    except Exception as e:
                        get_logger().error(f"删除文件失败 {file_path}: {e}")
    
    @staticmethod
    def get_file_hash(file_path: Union[str, Path], algorithm: str = 'md5') -> str:
        """获取文件哈希值"""
        file_path = Path(file_path)
        if not file_path.exists():
            return ""
        
        hash_algo = hashlib.new(algorithm)
        
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_algo.update(chunk)
            return hash_algo.hexdigest()
        except Exception as e:
            get_logger().error(f"计算文件哈希失败: {e}")
            return ""


class ProjectCleaner:
    """项目清理工具"""
    
    def __init__(self):
        self.logger = get_logger('cleaner')
        self.project_root = Path(config_manager.get('paths.project_root'))
    
    def organize_loose_files(self):
        """整理散落的文件"""
        self.logger.info("开始整理散落的文件...")
        
        # 定义文件类型和目标目录的映射
        file_mappings = {
            'temp': {
                'extensions': ['.txt', '.tmp', '.temp', '.html'],
                'patterns': ['html_response*', 'profile_output*', 'js_content*'],
                'target_dir': 'temp'
            },
            'scripts': {
                'extensions': ['.bat', '.sh', '.cmd'],
                'patterns': ['start*'],
                'target_dir': 'scripts'
            },
            'logs': {
                'extensions': ['.log'],
                'patterns': ['*.log'],
                'target_dir': 'logs'
            },
            'backup': {
                'patterns': ['*_backup*', '*_bak*', '*.backup'],
                'target_dir': 'backup'
            }
        }
        
        moved_files = []
        
        for category, mapping in file_mappings.items():
            target_dir = self.project_root / mapping['target_dir']
            FileManager.ensure_directory(target_dir)
            
            # 处理扩展名匹配
            if 'extensions' in mapping:
                for ext in mapping['extensions']:
                    for file_path in self.project_root.glob(f"*{ext}"):
                        if file_path.is_file() and file_path.parent == self.project_root:
                            self._move_file_safely(file_path, target_dir, moved_files)
            
            # 处理模式匹配
            if 'patterns' in mapping:
                for pattern in mapping['patterns']:
                    for file_path in self.project_root.glob(pattern):
                        if file_path.is_file() and file_path.parent == self.project_root:
                            self._move_file_safely(file_path, target_dir, moved_files)
        
        self.logger.info(f"整理完成，移动了 {len(moved_files)} 个文件")
        return moved_files
    
    def _move_file_safely(self, source: Path, target_dir: Path, moved_files: List[str]):
        """安全地移动文件"""
        try:
            target_path = target_dir / source.name
            
            # 如果目标文件已存在，添加时间戳
            if target_path.exists():
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                stem = target_path.stem
                suffix = target_path.suffix
                target_path = target_dir / f"{stem}_{timestamp}{suffix}"
            
            shutil.move(str(source), str(target_path))
            moved_files.append(f"{source} -> {target_path}")
            self.logger.debug(f"移动文件: {source} -> {target_path}")
            
        except Exception as e:
            self.logger.error(f"移动文件失败 {source}: {e}")
    
    def cleanup_duplicate_configs(self):
        """清理重复的配置文件"""
        self.logger.info("开始清理重复的配置文件...")
        
        config_files = []
        
        # 查找所有配置文件
        for pattern in ['config.*', '*.ini', '*.conf', '*.cfg']:
            config_files.extend(self.project_root.rglob(pattern))
        
        # 按文件名分组
        file_groups = {}
        for config_file in config_files:
            name = config_file.name.lower()
            if name not in file_groups:
                file_groups[name] = []
            file_groups[name].append(config_file)
        
        # 处理重复文件
        for name, files in file_groups.items():
            if len(files) > 1:
                self.logger.info(f"发现重复配置文件: {name}")
                
                # 保留最新的文件
                files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
                keep_file = files[0]
                
                for duplicate_file in files[1:]:
                    # 备份后删除
                    backup_path = FileManager.backup_file(duplicate_file)
                    if backup_path:
                        duplicate_file.unlink()
                        self.logger.info(f"删除重复文件: {duplicate_file} (已备份到: {backup_path})")
    
    def cleanup_empty_directories(self):
        """清理空目录"""
        self.logger.info("开始清理空目录...")
        
        removed_dirs = []
        
        # 递归查找空目录
        for dir_path in self.project_root.rglob('*'):
            if dir_path.is_dir() and dir_path != self.project_root:
                try:
                    # 检查是否为空目录
                    if not any(dir_path.iterdir()):
                        dir_path.rmdir()
                        removed_dirs.append(str(dir_path))
                        self.logger.debug(f"删除空目录: {dir_path}")
                except OSError:
                    # 目录不为空或无法删除
                    pass
        
        self.logger.info(f"清理完成，删除了 {len(removed_dirs)} 个空目录")
        return removed_dirs
    
    def cleanup_old_logs(self, days: int = 7):
        """清理旧日志文件"""
        self.logger.info(f"开始清理 {days} 天前的日志文件...")
        
        logs_dir = Path(config_manager.get('paths.logs_dir'))
        FileManager.cleanup_old_files(logs_dir, days, "*.log")
        
        # 清理根目录的日志文件
        FileManager.cleanup_old_files(self.project_root, days, "*.log")


class ConfigMigrator:
    """配置迁移工具"""
    
    def __init__(self):
        self.logger = get_logger('config_migrator')
        self.project_root = Path(config_manager.get('paths.project_root'))
    
    def migrate_old_configs(self):
        """迁移旧配置文件"""
        self.logger.info("开始迁移旧配置文件...")
        
        # 查找所有INI配置文件
        ini_files = list(self.project_root.rglob('*.ini'))
        
        for ini_file in ini_files:
            self._migrate_ini_config(ini_file)
    
    def _migrate_ini_config(self, ini_file: Path):
        """迁移单个INI配置文件"""
        try:
            import configparser
            
            config = configparser.ConfigParser()
            config.read(ini_file, encoding='utf-8')
            
            # 转换为新的JSON格式
            json_config = {}
            
            for section_name in config.sections():
                section = config[section_name]
                json_config[section_name.lower()] = dict(section)
            
            # 保存到新的配置目录
            config_dir = Path(config_manager.get('paths.project_root')) / 'config'
            FileManager.ensure_directory(config_dir)
            
            json_file = config_dir / f"{ini_file.stem}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(json_config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"已迁移配置文件: {ini_file} -> {json_file}")
            
        except Exception as e:
            self.logger.error(f"迁移配置文件失败 {ini_file}: {e}")


class DatabasePathFixer:
    """数据库路径修复工具"""
    
    def __init__(self):
        self.logger = get_logger('db_path_fixer')
    
    def fix_database_paths(self):
        """修复数据库路径配置"""
        self.logger.info("开始修复数据库路径配置...")
        
        data_dir = Path(config_manager.get('paths.data_dir'))
        FileManager.ensure_directory(data_dir)
        
        # 查找所有数据库文件
        db_files = []
        
        # 在项目根目录及子目录中查找
        project_root = Path(config_manager.get('paths.project_root'))
        for pattern in ['*.db', '*.sqlite', '*.sqlite3']:
            db_files.extend(project_root.rglob(pattern))
        
        moved_files = []
        
        for db_file in db_files:
            if db_file.parent != data_dir:
                target_path = data_dir / db_file.name
                
                try:
                    # 如果目标文件已存在，备份
                    if target_path.exists():
                        FileManager.backup_file(target_path)
                    
                    shutil.move(str(db_file), str(target_path))
                    moved_files.append(f"{db_file} -> {target_path}")
                    self.logger.info(f"移动数据库文件: {db_file} -> {target_path}")
                    
                except Exception as e:
                    self.logger.error(f"移动数据库文件失败 {db_file}: {e}")
        
        return moved_files


class Utils:
    """通用工具类"""
    
    file_manager = FileManager()
    project_cleaner = ProjectCleaner()
    config_migrator = ConfigMigrator()
    db_path_fixer = DatabasePathFixer()
    
    @staticmethod
    def get_project_info() -> Dict[str, Any]:
        """获取项目信息"""
        project_root = Path(config_manager.get('paths.project_root'))
        
        info = {
            'project_root': str(project_root),
            'directories': {},
            'files_count': {},
            'total_size': 0
        }
        
        # 统计各目录信息
        for dir_key in ['data_dir', 'logs_dir', 'temp_dir', 'backup_dir']:
            dir_path = Path(config_manager.get(f'paths.{dir_key}'))
            if dir_path.exists():
                files = list(dir_path.rglob('*'))
                file_count = len([f for f in files if f.is_file()])
                total_size = sum(f.stat().st_size for f in files if f.is_file())
                
                info['directories'][dir_key] = {
                    'path': str(dir_path),
                    'exists': True,
                    'file_count': file_count,
                    'total_size': total_size
                }
                info['total_size'] += total_size
            else:
                info['directories'][dir_key] = {
                    'path': str(dir_path),
                    'exists': False,
                    'file_count': 0,
                    'total_size': 0
                }
        
        return info
    
    @staticmethod
    def format_size(size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f}{size_names[i]}"
    
    @staticmethod
    def clean_project():
        """执行完整的项目清理"""
        logger = get_logger('project_cleaner')
        logger.info("开始执行项目清理...")
        
        cleaner = Utils.project_cleaner
        
        # 1. 整理散落的文件
        moved_files = cleaner.organize_loose_files()
        
        # 2. 清理重复配置
        cleaner.cleanup_duplicate_configs()
        
        # 3. 修复数据库路径
        db_moved = Utils.db_path_fixer.fix_database_paths()
        
        # 4. 清理旧日志
        cleaner.cleanup_old_logs()
        
        # 5. 清理空目录
        removed_dirs = cleaner.cleanup_empty_directories()
        
        # 6. 迁移配置文件
        Utils.config_migrator.migrate_old_configs()
        
        logger.info("项目清理完成")
        
        return {
            'moved_files': moved_files,
            'moved_databases': db_moved,
            'removed_directories': removed_dirs
        } 