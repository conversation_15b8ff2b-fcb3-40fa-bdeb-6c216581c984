"""
历史数据处理器模块

该模块负责处理历史赔率数据，包括返还率计算、满水赔率计算和区间分析。
集成现有的OddsAnalyzer和IntervalAnalyzer组件。
"""

import logging
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from datetime import datetime
from .interval_visualizer import IntervalVisualizer


@dataclass
class HistoricalOddsRecord:
    """历史赔率记录数据模型"""
    
    match_id: str
    company_id: str
    company_name: str
    home_odds: float
    draw_odds: float
    away_odds: float
    update_time: str
    payout_rate: Optional[float] = None  # 计算得出
    record_id: Optional[str] = None
    kelly_home: Optional[float] = None
    kelly_draw: Optional[float] = None
    kelly_away: Optional[float] = None


@dataclass
class IntervalAnalysisResult:
    """区间分析结果数据模型"""
    
    timestamp: str
    home_interval: str
    draw_interval: str
    away_interval: str
    home_rule_value: Optional[float]
    away_rule_value: Optional[float]
    home_true_odds: Optional[float]
    draw_true_odds: Optional[float]
    away_true_odds: Optional[float]
    payout_rate: Optional[float]


class HistoricalDataProcessor:
    """历史数据处理器"""
    
    def __init__(self, odds_analyzer, interval_analyzer):
        """
        初始化处理器
        
        Args:
            odds_analyzer: 赔率分析器实例
            interval_analyzer: 区间分析器实例
        """
        self.odds_analyzer = odds_analyzer
        self.interval_analyzer = interval_analyzer
        self.interval_visualizer = IntervalVisualizer()
        self.logger = logging.getLogger(__name__)
        
    def calculate_payout_rate(self, home_odds: float, draw_odds: float, away_odds: float) -> Optional[float]:
        """
        计算返还率
        使用博彩公式: 返还率 = 胜赔率 × 平赔率 × 负赔率 / (胜赔率 × 平赔率 + 平赔率 × 负赔率 + 胜赔率 × 负赔率) × 100%
        
        Args:
            home_odds: 主胜赔率
            draw_odds: 平局赔率  
            away_odds: 客胜赔率
            
        Returns:
            float: 返还率百分比，如果计算失败返回None
        """
        try:
            h, d, a = float(home_odds), float(draw_odds), float(away_odds)
            
            # 验证赔率值的有效性
            if h <= 0 or d <= 0 or a <= 0:
                self.logger.warning(f"无效的赔率值: {h}/{d}/{a}")
                return None
                
            # 计算返还率
            numerator = h * d * a
            denominator = h * d + d * a + h * a
            
            if denominator == 0:
                self.logger.warning(f"返还率计算分母为0: {h}/{d}/{a}")
                return None
                
            payout_rate = (numerator / denominator) * 100
            return round(payout_rate, 2)
            
        except (ValueError, TypeError, ZeroDivisionError) as e:
            self.logger.error(f"计算返还率时出错: {e}, 赔率: {home_odds}/{draw_odds}/{away_odds}")
            return None
    
    def process_historical_odds(self, historical_data: List[HistoricalOddsRecord], 
                              gap_difference: float) -> List[IntervalAnalysisResult]:
        """
        处理历史赔率数据，计算区间分析结果
        
        Args:
            historical_data: 从数据库获取的历史数据列表
            gap_difference: 档距差（主队评分 - 客队评分）
            
        Returns:
            list: 处理后的区间分析结果列表
        """
        results = []
        
        for record in historical_data:
            try:
                # 1. 计算返还率（如果还没有计算）
                if record.payout_rate is None:
                    record.payout_rate = self.calculate_payout_rate(
                        record.home_odds, record.draw_odds, record.away_odds
                    )
                
                # 如果返还率计算失败，跳过这条记录
                if record.payout_rate is None:
                    self.logger.warning(f"跳过返还率计算失败的记录: {record.update_time}")
                    continue
                
                # 2. 计算满水赔率
                home_true_odds = self.odds_analyzer.calculate_true_odds(
                    record.home_odds, record.payout_rate
                )
                draw_true_odds = self.odds_analyzer.calculate_true_odds(
                    record.draw_odds, record.payout_rate
                )
                away_true_odds = self.odds_analyzer.calculate_true_odds(
                    record.away_odds, record.payout_rate
                )
                
                # 3. 查找档距
                home_gap = self.odds_analyzer.find_gap_for_odds(home_true_odds, "win")
                draw_gap = self.odds_analyzer.find_gap_for_odds(draw_true_odds, "draw")
                away_gap = self.odds_analyzer.find_gap_for_odds(away_true_odds, "loss")
                
                # 4. 计算区间
                home_interval_result = self.interval_analyzer.find_interval_for_gap(
                    home_gap, gap_difference, home_team=True
                )
                away_interval_result = self.interval_analyzer.find_interval_for_gap(
                    away_gap, gap_difference, home_team=False
                )
                
                # 5. 处理平局区间（使用现有的平赔分析方法）
                draw_interval = self._calculate_draw_interval(
                    home_true_odds, away_true_odds, draw_true_odds
                )
                
                # 6. 创建分析结果
                analysis_result = IntervalAnalysisResult(
                    timestamp=record.update_time,
                    home_interval=home_interval_result.get("interval_type", "未知"),
                    draw_interval=draw_interval,
                    away_interval=away_interval_result.get("interval_type", "未知"),
                    home_rule_value=home_interval_result.get("rule_value"),
                    away_rule_value=away_interval_result.get("rule_value"),
                    home_true_odds=home_true_odds,
                    draw_true_odds=draw_true_odds,
                    away_true_odds=away_true_odds,
                    payout_rate=record.payout_rate
                )
                
                results.append(analysis_result)
                
            except Exception as e:
                self.logger.error(f"处理历史记录时出错: {e}, 记录时间: {record.update_time}")
                continue
        
        self.logger.info(f"成功处理 {len(results)} 条历史记录")
        return results
    
    def validate_historical_data(self, data: List[HistoricalOddsRecord]) -> List[HistoricalOddsRecord]:
        """
        验证历史数据的有效性
        
        Args:
            data: 原始历史数据列表
            
        Returns:
            list: 验证后的有效数据列表
        """
        valid_data = []
        
        for record in data:
            # 检查必要字段
            if not all([record.match_id, record.company_id, record.company_name]):
                self.logger.warning(f"记录缺少必要字段: {record}")
                continue
                
            # 检查赔率值
            if not all([
                isinstance(record.home_odds, (int, float)) and record.home_odds > 0,
                isinstance(record.draw_odds, (int, float)) and record.draw_odds > 0,
                isinstance(record.away_odds, (int, float)) and record.away_odds > 0
            ]):
                self.logger.warning(f"记录包含无效赔率: {record}")
                continue
                
            # 检查时间格式
            if not record.update_time:
                self.logger.warning(f"记录缺少更新时间: {record}")
                continue
                
            valid_data.append(record)
        
        self.logger.info(f"数据验证完成: {len(valid_data)}/{len(data)} 条记录有效")
        return valid_data
    
    def sort_by_time(self, data: List[IntervalAnalysisResult], ascending: bool = True) -> List[IntervalAnalysisResult]:
        """
        按时间排序分析结果
        
        Args:
            data: 分析结果列表
            ascending: 是否升序排列
            
        Returns:
            list: 排序后的结果列表
        """
        try:
            return sorted(data, key=lambda x: x.timestamp, reverse=not ascending)
        except Exception as e:
            self.logger.error(f"排序时出错: {e}")
            return data
    
    def create_visualization_data(self, analysis_results: List[IntervalAnalysisResult]):
        """
        创建可视化数据，将区间分析结果转换为可视化格式
        
        Args:
            analysis_results: 区间分析结果列表
            
        Returns:
            VisualizationData: 可视化数据对象
        """
        try:
            return self.interval_visualizer.create_visualization_data(analysis_results)
        except Exception as e:
            self.logger.error(f"创建可视化数据时出错: {e}")
            return None
    
    def map_interval_to_y_coordinate(self, interval_type: str) -> int:
        """
        将区间类型映射为Y轴坐标
        
        Args:
            interval_type: 区间类型字符串
            
        Returns:
            int: Y轴坐标值
        """
        return self.interval_visualizer.map_interval_to_y_coordinate(interval_type)
    
    def get_y_axis_labels(self) -> Dict[int, str]:
        """
        获取Y轴标签映射
        
        Returns:
            dict: Y坐标到区间名称的映射
        """
        return self.interval_visualizer.get_y_axis_labels()
    
    def get_interval_categories(self) -> Dict[str, List[str]]:
        """
        获取区间分类
        
        Returns:
            dict: 区间分类字典
        """
        return self.interval_visualizer.get_interval_categories()
    
    def process_and_visualize_historical_data(self, historical_data: List[HistoricalOddsRecord], 
                                            gap_difference: float):
        """
        处理历史数据并创建可视化数据的完整流程
        
        Args:
            historical_data: 从数据库获取的历史数据列表
            gap_difference: 档距差（主队评分 - 客队评分）
            
        Returns:
            tuple: (区间分析结果列表, 可视化数据对象)
        """
        try:
            # 1. 验证数据
            valid_data = self.validate_historical_data(historical_data)
            if not valid_data:
                self.logger.warning("没有有效的历史数据")
                return [], None
            
            # 2. 处理历史赔率数据
            analysis_results = self.process_historical_odds(valid_data, gap_difference)
            if not analysis_results:
                self.logger.warning("没有成功处理的分析结果")
                return [], None
            
            # 3. 按时间排序
            sorted_results = self.sort_by_time(analysis_results, ascending=True)
            
            # 4. 创建可视化数据
            visualization_data = self.create_visualization_data(sorted_results)
            
            self.logger.info(f"完整处理流程完成: {len(sorted_results)} 条结果")
            return sorted_results, visualization_data
            
        except Exception as e:
            self.logger.error(f"处理和可视化历史数据时出错: {e}")
            return [], None
    
    def _calculate_draw_interval(self, home_true_odds: Optional[float], 
                               away_true_odds: Optional[float], 
                               draw_true_odds: Optional[float]) -> str:
        """
        计算平局区间，使用现有的平赔分析方法
        
        Args:
            home_true_odds: 主胜满水赔率
            away_true_odds: 客胜满水赔率
            draw_true_odds: 平局满水赔率
            
        Returns:
            str: 平局区间类型
        """
        try:
            if not all([home_true_odds, away_true_odds, draw_true_odds]):
                return "未知区间"
            
            # 使用现有的OddsAnalyzer方法查找标准平赔
            standard_draw_odds = self.odds_analyzer.find_standard_draw_odds(
                home_true_odds, away_true_odds
            )
            
            if not standard_draw_odds:
                return "平局区间"  # 默认值
            
            # 计算平赔区间
            intervals = self.odds_analyzer.calculate_draw_odds_intervals(standard_draw_odds)
            
            if not intervals:
                return "平局区间"  # 默认值
            
            # 确定实际平赔落在哪个区间
            draw_interval = self.odds_analyzer.determine_draw_interval(
                draw_true_odds, intervals
            )
            
            return draw_interval if draw_interval != "未知区间" else "平局区间"
            
        except Exception as e:
            self.logger.error(f"计算平局区间时出错: {e}")
            return "平局区间"