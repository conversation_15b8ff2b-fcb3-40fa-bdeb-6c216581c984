// This script uploads data from JSON files to your Tencent CloudBase database.
//
// PRE-REQUISITES:
// 1. Install the CloudBase Node.js SDK:
//    npm install --save @cloudbase/node-sdk
//
// 2. Configure your environment:
//    - Set up a `cloudbaserc.json` file in your project root or provide credentials here.
//    - Ensure your IP is whitelisted in the CloudBase console if needed.
//
// HOW TO RUN:
// 1. Open a terminal in the 'football_analysis_system' directory.
// 2. Run the script: node temp/upload_to_cloud.js
//
const cloudbase = require("@cloudbase/node-sdk");
const fs = require("fs");
const path = require("path");

// !!! IMPORTANT: FILL IN YOUR CLOUDBASE CONFIGURATION HERE !!!
const app = cloudbase.init({
  env: "cloud1-8gtbg28i4d42282e", // Replace with your Environment ID
});
const db = app.database();
const _ = db.command;

// Mapping of collection names to their JSON data files
const collectionsToUpload = {
  'matches_history': './temp/matches_history.json',
  'matches_live': './temp/matches_live.json',
  'odds_live': './temp/odds_live.json',
  'overunder_odds_live': './temp/overunder_odds_live.json',
  'team_power_ratings': './temp/team_power_ratings.json',
  'match_probabilities': './temp/match_probabilities.json',
  'strength_matchups': './temp/strength_matchups.json',
  'standard_odds': './temp/standard_odds.json',

};

// Function to upload data to a collection
async function uploadCollection(collectionName, jsonFilePath) {
  console.log(`\nUploading to collection: ${collectionName}...`);
  const fullPath = path.resolve(__dirname, '..', jsonFilePath);

  if (!fs.existsSync(fullPath)) {
    console.error(`  -> ERROR: JSON file not found at ${fullPath}`);
    return;
  }

  try {
    const dataStr = fs.readFileSync(fullPath, "utf-8");
    const data = JSON.parse(dataStr);

    if (!Array.isArray(data) || data.length === 0) {
      console.log("  -> No data to upload or invalid format.");
      return;
    }

    // Clear the collection before inserting new data
    console.log(`  -> Deleting all existing documents in '${collectionName}'...`);
    await db.collection(collectionName).where({}).remove();
    console.log(`  -> Existing documents deleted.`);

    // CloudBase allows inserting up to 100 documents at a time
    const batchSize = 100;
    console.log(`  -> Inserting ${data.length} new documents in batches of ${batchSize}...`);
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      await db.collection(collectionName).add(batch);
      console.log(`    -> Uploaded batch ${Math.floor(i / batchSize) + 1}`);
    }

    console.log(`  -> Successfully uploaded all documents to '${collectionName}'.`);
  } catch (error) {
    console.error(`  -> ERROR during upload to '${collectionName}':`, error);
  }
}

// Main execution function
async function main() {
  console.log("Starting data migration to CloudBase...");
  for (const [collectionName, jsonFilePath] of Object.entries(collectionsToUpload)) {
    await uploadCollection(collectionName, jsonFilePath);
  }
  console.log("Data migration process finished.");
}

main().catch(console.error);
