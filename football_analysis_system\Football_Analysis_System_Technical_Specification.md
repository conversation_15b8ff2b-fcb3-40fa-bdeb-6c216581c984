# 足球比赛分析系统 - 完整技术规格说明书

## 1. 系统概述

### 1.1 项目信息
- **系统名称**: 足球比赛分析系统 Plus
- **版本**: v4.0 现代版
- **开发语言**: Python 3.8+
- **GUI框架**: Tkinter
- **数据库**: SQLite3
- **数据源**: titan007.com (球探网)
- **架构模式**: 模块化架构

### 1.2 系统功能概述
足球比赛分析系统是一个基于Python/Tkinter的足球数据分析平台，具有以下核心功能：
- 实时比赛数据和赔率爬取（从titan007.com）
- 满水赔率计算和档距分析
- 平赔区间分析（11个标准区间）
- 基于球队实力的区间分析
- 泊松分布比分预测
- AI基本面分析（DeepSeek API）
- 广义实力评分计算
- 双版本UI（传统版和现代版）

## 2. 系统架构

### 2.1 实际项目目录结构
```
football_analysis_system/
├── main.py                     # 传统版本入口
├── run_modern_app.py           # 现代版本入口
├── config.py                   # 统一配置管理
├── analysis/                   # 分析模块
│   ├── odds_analyzer.py        # 赔率分析器
│   ├── interval_analyzer.py    # 区间分析器
│   ├── poisson_module.py       # 泊松分析模块
│   ├── guangyishili_analyzer.py # 广义实力分析器
│   └── fundamental_analysis.py # 基本面分析
├── core/                       # 核心工具模块
│   ├── config_manager.py       # 配置管理器
│   ├── database_manager.py     # 数据库管理器
│   ├── logger_manager.py       # 日志管理器
│   ├── cache_manager.py        # 缓存管理器
│   ├── exception_handler.py    # 异常处理器
│   ├── validators.py           # 输入验证器
│   └── query_optimizer.py     # 查询优化器
├── db/                         # 数据库操作模块
│   ├── database.py             # 数据库基类
│   └── operations.py           # 数据库操作
├── models/                     # 数据模型
│   ├── match.py                # 比赛数据模型
│   ├── team.py                 # 球队数据模型
│   ├── odds.py                 # 赔率数据模型
│   └── over_under_odds.py      # 大小球赔率模型
├── scrapers/                   # 数据爬虫模块
│   ├── match_scraper.py        # 比赛数据爬虫
│   ├── odds_scraper.py         # 赔率数据爬虫
│   ├── overunder_scraper.py    # 大小球赔率爬虫
│   ├── crawler.py              # 通用爬虫类
│   ├── league_mapping.py       # 联赛名称映射
│   └── match_odds_History.py   # 历史数据爬虫
├── ui/                         # 用户界面模块
│   ├── app.py                  # 传统版本主界面
│   ├── modern_app.py           # 现代版本主界面
│   ├── main_window.py          # 主窗口类
│   ├── styles.py               # 样式定义
│   └── tabs/                   # 标签页模块
│       ├── basic_info_tab.py   # 基本信息标签页
│       ├── odds_tab.py         # 赔率分析标签页
│       ├── fundamental_tab.py  # 基本面分析标签页
│       ├── interval_tab.py     # 区间分析标签页
│       ├── poisson_tab.py      # 泊松分析标签页
│       ├── data_management_tab.py # 数据管理标签页
│       ├── prediction_records_tab.py # 预测记录标签页
│       ├── data_update_tab.py  # 数据更新标签页
│       ├── odds_rules_tab.py   # 赔率法则标签页
│       └── confidence_rules_tab.py # 信心规则标签页
├── utils/                      # 工具模块
└── data/                       # 数据存储目录
    ├── matches_and_odds.db     # 主要比赛和赔率数据库
    ├── guangyishili.db         # 广义实力数据库
    ├── StandardOdds.db         # 标准赔率参考库
    ├── football.db             # 泊松分析数据库
    ├── sports_database.db      # 综合体育数据库
    ├── team_database.db        # 球队数据库
    ├── odds_database.db        # 赔率数据库
    └── WilliamHillTeam.db      # 威廉希尔球队数据库
```

### 2.2 核心启动入口

#### 2.2.1 传统版本启动 (main.py)
```python
from ui.app import FootballAnalysisApp

def main():
    app = FootballAnalysisApp()
    app.run()

if __name__ == "__main__":
    main()
```

#### 2.2.2 现代版本启动 (run_modern_app.py)
```python
from ui.modern_app import ModernFootballApp

if __name__ == "__main__":
    app = ModernFootballApp()
    app.run()
```

## 3. 数据库设计

### 3.1 核心数据表结构

#### 3.1.1 比赛表 (matches)
```sql
CREATE TABLE matches (
    match_id TEXT PRIMARY KEY,        -- 比赛ID
    league_name TEXT,                 -- 联赛名称
    home_team TEXT,                   -- 主队名称
    away_team TEXT,                   -- 客队名称
    start_time TEXT,                  -- 开始时间
    scrape_time TEXT,                 -- 爬取时间
    home_score INTEGER,               -- 主队得分
    away_score INTEGER,               -- 客队得分
    match_status TEXT                 -- 比赛状态
);
```

#### 3.1.2 赔率表 (odds)
```sql
CREATE TABLE odds (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    match_id TEXT,                    -- 关联比赛ID
    bookmaker TEXT,                   -- 博彩公司名称
    initial_home_win REAL,            -- 初始主胜赔率
    initial_draw REAL,                -- 初始平局赔率
    initial_away_win REAL,            -- 初始客胜赔率
    instant_home_win REAL,            -- 即时主胜赔率
    instant_draw REAL,                -- 即时平局赔率
    instant_away_win REAL,            -- 即时客胜赔率
    payout_rate REAL,                 -- 返还率
    initial_payout_rate REAL,         -- 初始返还率
    earliest_open_time TEXT,          -- 最早开盘时间
    scrape_time TEXT,                 -- 爬取时间
    FOREIGN KEY (match_id) REFERENCES matches(match_id)
);
```

#### 3.1.3 球队实力评分表 (team_power_ratings)
```sql
CREATE TABLE team_power_ratings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id TEXT NOT NULL,         -- 公司ID
    company_name TEXT NOT NULL,       -- 公司名称
    team_name TEXT NOT NULL,          -- 球队名称
    power_category TEXT,              -- 实力分类
    power_level REAL,                 -- 实力等级
    league_name TEXT,                 -- 联赛名称
    update_time TEXT                  -- 更新时间
);
```

#### 3.1.4 大小球赔率表 (over_under_odds)
```sql
CREATE TABLE over_under_odds (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    match_id TEXT,                    -- 关联比赛ID
    bookmaker TEXT,                   -- 博彩公司
    line REAL,                        -- 盘口线
    over_odds REAL,                   -- 大球赔率
    under_odds REAL,                  -- 小球赔率
    scrape_time TEXT,                 -- 爬取时间
    FOREIGN KEY (match_id) REFERENCES matches(match_id)
);
```

### 3.2 标准赔率参考库结构

#### 3.2.1 标准平赔表 (StandardOdds.db)
包含11个平赔区间的标准参考数据：
- 超深平 (1.60-1.85)
- 超散平 (1.85-2.10)  
- 超实平 (2.10-2.35)
- 低开平 (2.35-2.60)
- 平实低 (2.60-2.85)
- 平中庸下 (2.85-3.10)
- 平中庸上 (3.10-3.35)
- 平实高 (3.35-3.60)
- 高开平 (3.60-3.85)
- 韬光平 (3.85-4.10)
- 超韬平 (4.10以上)

## 4. 核心分析模块

### 4.1 赔率分析模块 (analysis/odds_analyzer.py)

#### 4.1.1 满水赔率计算
```python
class OddsAnalyzer:
    def calculate_true_odds(self, odds_value, payout_rate):
        """
        计算满水赔率（真实赔率）
        满水赔率 = 原始赔率 * (100 / 返还率)
        """
        if not odds_value or not payout_rate or payout_rate <= 0:
            return None
        
        true_odds = odds_value * (100 / payout_rate)
        return round(true_odds, 2)
    
    def calculate_all_true_odds(self, odds_data):
        """计算所有赔率的满水值"""
        result = {}
        for bookmaker, odds in odds_data.items():
            if odds.get('payout_rate'):
                result[bookmaker] = {
                    'true_home': self.calculate_true_odds(
                        odds['instant_home_win'], odds['payout_rate']
                    ),
                    'true_draw': self.calculate_true_odds(
                        odds['instant_draw'], odds['payout_rate']
                    ),
                    'true_away': self.calculate_true_odds(
                        odds['instant_away_win'], odds['payout_rate']
                    )
                }
        return result
```

#### 4.1.2 档距分析
```python
def analyze_odds_range(self, home_odds, away_odds):
    """
    分析主客胜赔率档距
    """
    if not home_odds or not away_odds:
        return None
    
    # 计算档距差
    range_diff = abs(home_odds - away_odds)
    
    # 判断档距类型
    if range_diff < 0.5:
        return "均势"
    elif range_diff < 1.0:
        return "小档距"
    elif range_diff < 2.0:
        return "中档距"
    else:
        return "大档距"
```

#### 4.1.3 平赔区间分析
```python
def get_draw_odds_interval(self, draw_odds):
    """
    根据平赔值判断所属区间
    """
    intervals = {
        (1.60, 1.85): "超深平",
        (1.85, 2.10): "超散平",
        (2.10, 2.35): "超实平",
        (2.35, 2.60): "低开平",
        (2.60, 2.85): "平实低",
        (2.85, 3.10): "平中庸下",
        (3.10, 3.35): "平中庸上",
        (3.35, 3.60): "平实高",
        (3.60, 3.85): "高开平",
        (3.85, 4.10): "韬光平",
        (4.10, float('inf')): "超韬平"
    }
    
    for (min_val, max_val), interval_name in intervals.items():
        if min_val <= draw_odds < max_val:
            return interval_name
    
    return "未知区间"
```

### 4.2 区间分析模块 (analysis/interval_analyzer.py)

#### 4.2.1 基于赔率档距的精细化区间分析
```python
class IntervalAnalyzer:
    def __init__(self, strength_matchup_db_path):
        """
        初始化区间分析器
        Args:
            strength_matchup_db_path: 实力对阵表数据库路径
        """
        self.db_manager = DatabaseManager(strength_matchup_db_path)
        self.strength_matchup_table = {}
        self.load_strength_matchup_table()
    
    def find_interval_for_gap(self, gap_value, gap_difference, home_team=True):
        """
        根据距值和档距D计算区间范围和规则号
        
        Args:
            gap_value: 从赔率表查到的距值
            gap_difference: 档距D（主队评分 - 客队评分）
            home_team: 是否为主队 (True) 或客队 (False)
        
        Returns:
            dict: 包含区间和规则号的字典
        """
        if gap_value is None or gap_difference is None:
            return {"low": None, "high": None, "rule_value": None, "interval_type": "未知"}
        
        try:
            gap_float = float(gap_value)      # 这是从赔率表查到的距值
            d_float = float(gap_difference)    # 这是档距D
            
            # 计算区间值 = 对应档距 - 档距差
            interval_value = round(gap_float - d_float, 1)
            
            # 定义34个精细化区间规则（包含水位信息）
            interval_rules = {
                "超韬利盘": {
                    "rule_value": 5.5,  # 规则值
                    "home_offset_low": -999,  # 无下限
                    "home_offset_high": -3.1,
                    "away_offset_low": 4.0,
                    "away_offset_high": 999  # 无上限
                },
                "超韬盘中上水": {
                    "rule_value": 5,
                    "home_offset_low": -3.0,
                    "home_offset_high": -2.6,
                    "away_offset_low": 3.5,
                    "away_offset_high": 3.9
                },
                "超韬盘中下水": {
                    "rule_value": 4.5,
                    "home_offset_low": -2.5,
                    "home_offset_high": -2.1,
                    "away_offset_low": 3.0,
                    "away_offset_high": 3.4
                },
                "韬光盘高水": {
                    "rule_value": 4,
                    "home_offset_low": -2.0,
                    "home_offset_high": -1.9,
                    "away_offset_low": 2.8,
                    "away_offset_high": 2.9
                },
                "韬光盘中高水": {
                    "rule_value": 3.5,
                    "home_offset_low": -1.8,
                    "home_offset_high": -1.7,
                    "away_offset_low": 2.6,
                    "away_offset_high": 2.7
                },
                "韬光盘中水": {
                    "rule_value": 3,
                    "home_offset_low": -1.6,
                    "home_offset_high": -1.5,
                    "away_offset_low": 2.4,
                    "away_offset_high": 2.5
                },
                "韬光盘中低水": {
                    "rule_value": 2.5,
                    "home_offset_low": -1.4,
                    "home_offset_high": -1.3,
                    "away_offset_low": 2.2,
                    "away_offset_high": 2.3
                },
                "韬光盘低水": {
                    "rule_value": 2,
                    "home_offset_low": -1.2,
                    "home_offset_high": -1.1,
                    "away_offset_low": 2.0,
                    "away_offset_high": 2.1
                },
                "韬光盘超低水": {
                    "rule_value": 3,
                    "home_offset_low": -1.0,
                    "home_offset_high": -0.9,
                    "away_offset_low": 1.8,
                    "away_offset_high": 1.9
                },
                "高开盘中高水": {
                    "rule_value": 4,
                    "home_offset_low": -0.8,
                    "home_offset_high": -0.7,
                    "away_offset_low": 1.6,
                    "away_offset_high": 1.7
                },
                "高开盘中水": {
                    "rule_value": 4.5,
                    "home_offset_low": -0.6,
                    "home_offset_high": -0.5,
                    "away_offset_low": 1.4,
                    "away_offset_high": 1.5
                },
                "高开盘中低水": {
                    "rule_value": 4,
                    "home_offset_low": -0.4,
                    "home_offset_high": -0.3,
                    "away_offset_low": 1.2,
                    "away_offset_high": 1.3
                },
                "实开盘超高水": {
                    "rule_value": 3.5,
                    "home_offset_low": -0.2,
                    "home_offset_high": -0.1,
                    "away_offset_low": 1.0,
                    "away_offset_high": 1.1
                },
                "实开盘高水": {
                    "rule_value": 4,
                    "home_offset_low": 0.0,
                    "home_offset_high": 0.1,
                    "away_offset_low": 0.8,
                    "away_offset_high": 0.9
                },
                "实开盘中高水": {
                    "rule_value": 3.5,
                    "home_offset_low": 0.2,
                    "home_offset_high": 0.3,
                    "away_offset_low": 0.6,
                    "away_offset_high": 0.7
                },
                "实开盘中水": {
                    "rule_value": 3,
                    "home_offset_low": 0.4,
                    "home_offset_high": 0.5,
                    "away_offset_low": 0.4,
                    "away_offset_high": 0.5
                },
                "实开盘中低水": {
                    "rule_value": 2.5,
                    "home_offset_low": 0.6,
                    "home_offset_high": 0.7,
                    "away_offset_low": 0.2,
                    "away_offset_high": 0.3
                },
                "实开盘低水": {
                    "rule_value": 2,
                    "home_offset_low": 0.8,
                    "home_offset_high": 0.9,
                    "away_offset_low": 0.0,
                    "away_offset_high": 0.1
                },
                "实开盘超低水": {
                    "rule_value": 3,
                    "home_offset_low": 1.0,
                    "home_offset_high": 1.1,
                    "away_offset_low": -0.2,
                    "away_offset_high": -0.1
                },
                "低开盘中高水": {
                    "rule_value": 3.5,
                    "home_offset_low": 1.2,
                    "home_offset_high": 1.3,
                    "away_offset_low": -0.4,
                    "away_offset_high": -0.3
                },
                "低开盘中水": {
                    "rule_value": 4,
                    "home_offset_low": 1.4,
                    "home_offset_high": 1.5,
                    "away_offset_low": -0.6,
                    "away_offset_high": -0.5
                },
                "低开盘中低水": {
                    "rule_value": 3,
                    "home_offset_low": 1.6,
                    "home_offset_high": 1.7,
                    "away_offset_low": -0.8,
                    "away_offset_high": -0.7
                },
                "超实盘超高水": {
                    "rule_value": 2.5,
                    "home_offset_low": 1.8,
                    "home_offset_high": 1.9,
                    "away_offset_low": -1.0,
                    "away_offset_high": -0.9
                },
                "超实盘高水": {
                    "rule_value": 0.5,
                    "home_offset_low": 2.0,
                    "home_offset_high": 2.1,
                    "away_offset_low": -1.2,
                    "away_offset_high": -1.1
                },
                "超实盘中高水": {
                    "rule_value": 1,
                    "home_offset_low": 2.2,
                    "home_offset_high": 2.3,
                    "away_offset_low": -1.4,
                    "away_offset_high": -1.3
                },
                "超实盘中水": {
                    "rule_value": 1.5,
                    "home_offset_low": 2.4,
                    "home_offset_high": 2.5,
                    "away_offset_low": -1.6,
                    "away_offset_high": -1.5
                },
                "超实盘中低水": {
                    "rule_value": 2,
                    "home_offset_low": 2.6,
                    "home_offset_high": 2.7,
                    "away_offset_low": -1.8,
                    "away_offset_high": -1.7
                },
                "超实盘低水": {
                    "rule_value": 2.5,
                    "home_offset_low": 2.8,
                    "home_offset_high": 2.9,
                    "away_offset_low": -2.0,
                    "away_offset_high": -1.9
                },
                "超实盘超低水": {
                    "rule_value": 3,
                    "home_offset_low": 3.0,
                    "home_offset_high": 3.1,
                    "away_offset_low": -2.2,
                    "away_offset_high": -2.1
                },
                "超散盘中高水": {
                    "rule_value": 3.5,
                    "home_offset_low": 3.2,
                    "home_offset_high": 3.3,
                    "away_offset_low": -2.4,
                    "away_offset_high": -2.3
                },
                "超散盘中水": {
                    "rule_value": 4,
                    "home_offset_low": 3.4,
                    "home_offset_high": 3.5,
                    "away_offset_low": -2.6,
                    "away_offset_high": -2.5
                },
                "超散盘中低水": {
                    "rule_value": 3.5,
                    "home_offset_low": 3.6,
                    "home_offset_high": 3.7,
                    "away_offset_low": -2.8,
                    "away_offset_high": -2.7
                },
                "超散盘低水": {
                    "rule_value": 3,
                    "home_offset_low": 3.8,
                    "home_offset_high": 3.9,
                    "away_offset_low": -3.0,
                    "away_offset_high": -2.9
                },
                "超深盘中上水": {
                    "rule_value": 2,
                    "home_offset_low": 4.0,
                    "home_offset_high": 4.4,
                    "away_offset_low": -3.5,
                    "away_offset_high": -3.1
                },
                "超深盘中下水": {
                    "rule_value": 3,
                    "home_offset_low": 4.5,
                    "home_offset_high": 4.9,
                    "away_offset_low": -4.0,
                    "away_offset_high": -3.6
                },
                "超深诱盘": {
                    "rule_value": 4,
                    "home_offset_low": 5.0,
                    "home_offset_high": 999,  # 无上限
                    "away_offset_low": -999,  # 无下限
                    "away_offset_high": -4.1
                }
            }
            
            # 找到匹配的盘口类型
            matched_type = None
            matched_offsets = None
            
            for odds_type, offsets in interval_rules.items():
                if home_team:
                    low = offsets["home_offset_low"]
                    high = offsets["home_offset_high"]
                else:
                    low = offsets["away_offset_low"]
                    high = offsets["away_offset_high"]
                
                # 检查区间值是否在区间范围内（增加些微容错空间）
                if low - 0.001 <= interval_value <= high + 0.001:
                    matched_type = odds_type
                    matched_offsets = offsets
                    break
            
            if matched_type and matched_offsets:
                return {
                    "low": interval_value,
                    "high": interval_value,
                    "rule_value": matched_offsets["rule_value"],
                    "interval_type": matched_type
                }
            
            return {
                "low": None,
                "high": None,
                "rule_value": None,
                "interval_type": "未知"
            }
            
        except (ValueError, TypeError) as e:
            return {"low": None, "high": None, "rule_value": None, "interval_type": "未知"}
    
    def get_first_choice(self, rule_diff, gap_diff=None):
        """
        根据规差值和档距差返回首选推荐
        
        Args:
            rule_diff: 规差值（主队规则值 - 客队规则值）
            gap_diff: 档距差（主队评分 - 客队评分）
        
        Returns:
            str: 首选推荐
        """
        if not isinstance(rule_diff, (int, float)):
            try:
                rule_diff = float(rule_diff)
            except (ValueError, TypeError):
                return "未知"
        
        # 检查档距差是否有效
        has_valid_gap_diff = False
        if gap_diff is not None:
            try:
                gap_diff = float(gap_diff)
                has_valid_gap_diff = True
            except (ValueError, TypeError):
                has_valid_gap_diff = False
        
        # 根据档距差和规值差的组合确定首选值
        # 档距差 >= -1.5 的映射关系
        rule_diff_map_ge_neg1_5 = {
            -5: "主让2胜",
            -4.5: "主让2平",
            -4: "主让2平",
            -3.5: "主让1胜",
            -3: "主让1胜",
            -2.5: "主让1胜",
            -2: "主让1平",
            -1.5: "主让1平",
            -1: "双平偏让平",
            -0.5: "双平",
            0: "双平偏平",
            0.5: "平",
            1: "主让1负",
            1.5: "客让1平",
            2: "客让1平",
            2.5: "客让1胜",
            3: "客让1胜",
            3.5: "客让1胜",
            4: "客让2平",
            4.5: "客让2平",
            5: "客让2胜"
        }
        
        # 档距差 < -1.5 的映射关系
        rule_diff_map_lt_neg1_5 = {
            -5: "主让2胜",
            -4.5: "主让2平",
            -4: "主让2平",
            -3.5: "主让1胜",
            -3: "主让1胜",
            -2.5: "主让1胜",
            -2: "主让1平",
            -1.5: "主让1平",
            -1: "主受1胜",
            -0.5: "平",
            0: "双平偏平",
            0.5: "双平",
            1: "双平偏让平",
            1.5: "客让1平",
            2: "客让1平",
            2.5: "客让1胜",
            3: "客让1胜",
            3.5: "客让1胜",
            4: "客让2平",
            4.5: "客让2平",
            5: "客让2胜"
        }
        
        # 基于档距差选择映射表
        if has_valid_gap_diff:
            if gap_diff >= -1.5:
                return rule_diff_map_ge_neg1_5.get(rule_diff, "未知")
            else:
                return rule_diff_map_lt_neg1_5.get(rule_diff, "未知")
        else:
            # 如果没有有效的档距差，默认使用档距差≥-1.5的映射规则
            return rule_diff_map_ge_neg1_5.get(rule_diff, "未知")
    
    def get_risk_level(self, rule_value):
        """
        根据规则号获取风险等级
        
        Args:
            rule_value: 规则号
        
        Returns:
            str: 风险等级 (危险区/高风险区/警示区/安全区)
        """
        try:
            if rule_value is None:
                return "未知区间"
            
            rule_value = float(rule_value)
            
            # 按规则号分类风险等级
            if rule_value in [5.5, 5]:
                return "危险区"
            elif rule_value == 4.5:
                return "高风险区"
            elif rule_value in [3, 3.5, 4]:
                return "警示区"
            elif rule_value in [0.5, 1, 1.5, 2, 2.5]:
                return "安全区"
            else:
                return "未知区间"
                
        except (ValueError, TypeError):
            return "未知区间"
```

### 4.3 泊松分析模块 (analysis/poisson_module.py)

#### 4.3.1 泊松分布比分预测
```python
from scipy.stats import poisson
import pandas as pd

class PoissonAnalyzer:
    def __init__(self):
        self.db_manager = database_manager
    
    def calculate_team_stats(self, team_name, league_name, recent_matches=10):
        """
        计算球队统计数据
        """
        # 获取最近N场比赛
        matches = self.get_recent_matches(team_name, league_name, recent_matches)
        
        if not matches:
            return None
        
        # 计算平均进球和失球
        goals_scored = []
        goals_conceded = []
        
        for match in matches:
            if match['home_team'] == team_name:
                goals_scored.append(match['home_score'])
                goals_conceded.append(match['away_score'])
            else:
                goals_scored.append(match['away_score'])
                goals_conceded.append(match['home_score'])
        
        return {
            'avg_goals_scored': sum(goals_scored) / len(goals_scored),
            'avg_goals_conceded': sum(goals_conceded) / len(goals_conceded),
            'matches_count': len(matches)
        }
    
    def predict_match_score(self, home_team, away_team, league_name):
        """
        使用泊松分布预测比赛比分
        """
        # 获取球队统计数据
        home_stats = self.calculate_team_stats(home_team, league_name)
        away_stats = self.calculate_team_stats(away_team, league_name)
        
        if not home_stats or not away_stats:
            return None
        
        # 获取联赛平均进球数
        league_avg = self.get_league_average_goals(league_name)
        
        # 计算期望进球数
        home_expected = (home_stats['avg_goals_scored'] * 
                        away_stats['avg_goals_conceded'] / 
                        league_avg)
        
        away_expected = (away_stats['avg_goals_scored'] * 
                        home_stats['avg_goals_conceded'] / 
                        league_avg)
        
        # 计算各比分概率
        score_probabilities = []
        for home_goals in range(6):
            for away_goals in range(6):
                prob = (poisson.pmf(home_goals, home_expected) * 
                       poisson.pmf(away_goals, away_expected))
                
                score_probabilities.append({
                    'home_goals': home_goals,
                    'away_goals': away_goals,
                    'probability': prob
                })
        
        # 按概率排序
        score_probabilities.sort(key=lambda x: x['probability'], reverse=True)
        
        return {
            'home_expected_goals': home_expected,
            'away_expected_goals': away_expected,
            'most_likely_scores': score_probabilities[:10]
        }
```

### 4.4 基本面分析模块 (analysis/fundamental_analysis.py)

#### 4.4.1 AI驱动的基本面分析
```python
import requests
import json

class FundamentalAnalyzer:
    def __init__(self):
        self.api_key = config_manager.get('api.deepseek.api_key')
        self.api_url = config_manager.get('api.deepseek.api_url')
        self.model = config_manager.get('api.deepseek.model')
    
    def analyze_match_fundamentals(self, home_team, away_team, league_name):
        """
        进行AI驱动的基本面分析
        """
        if not self.api_key:
            return {"error": "DeepSeek API密钥未配置"}
        
        # 收集比赛基础数据
        match_data = self.collect_match_data(home_team, away_team, league_name)
        
        # 构建分析提示
        prompt = self.build_analysis_prompt(match_data)
        
        # 调用DeepSeek API
        try:
            response = self.call_deepseek_api(prompt)
            return {
                "success": True,
                "analysis": response,
                "match_data": match_data
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def call_deepseek_api(self, prompt):
        """
        调用DeepSeek API进行分析
        """
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': self.model,
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0.7,
            'max_tokens': 1500
        }
        
        response = requests.post(
            self.api_url, 
            headers=headers, 
            json=data, 
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            raise Exception(f"API调用失败: {response.status_code}")
    
    def build_analysis_prompt(self, match_data):
        """
        构建AI分析提示
        """
        prompt = f"""
        请分析以下足球比赛：
        
        比赛: {match_data['home_team']} vs {match_data['away_team']}
        联赛: {match_data['league_name']}
        
        主队近期表现: {match_data.get('home_recent_form', '数据不足')}
        客队近期表现: {match_data.get('away_recent_form', '数据不足')}
        
        历史交锋: {match_data.get('head_to_head', '数据不足')}
        
        请从以下角度分析:
        1. 球队实力对比
        2. 近期状态分析  
        3. 历史交锋记录
        4. 主客场因素
        5. 比赛预测建议
        
        请给出简洁专业的分析结论。
        """
        return prompt
```

### 4.5 广义实力分析模块 (analysis/guangyishili_analyzer.py)

#### 4.5.1 综合实力评分计算
```python
class GuangyishiliAnalyzer:
    def __init__(self, source_db_path, target_db_path):
        self.source_db = source_db_path
        self.target_db = target_db_path
    
    def calculate_team_comprehensive_strength(self, team_name, league_name):
        """
        计算球队综合实力评分
        """
        # 获取球队历史数据
        match_history = self.get_team_match_history(team_name, league_name)
        
        if not match_history:
            return None
        
        # 计算各项指标
        attack_strength = self.calculate_attack_strength(match_history)
        defense_strength = self.calculate_defense_strength(match_history)
        home_advantage = self.calculate_home_advantage(match_history)
        away_performance = self.calculate_away_performance(match_history)
        recent_form = self.calculate_recent_form(match_history)
        
        # 综合评分（加权平均）
        comprehensive_strength = (
            attack_strength * 0.3 +
            defense_strength * 0.3 +
            home_advantage * 0.15 +
            away_performance * 0.15 +
            recent_form * 0.1
        )
        
        return {
            'team_name': team_name,
            'league_name': league_name,
            'attack_strength': attack_strength,
            'defense_strength': defense_strength,
            'home_advantage': home_advantage,
            'away_performance': away_performance,
            'recent_form': recent_form,
            'comprehensive_strength': round(comprehensive_strength, 2)
        }
    
    def save_strength_data(self, strength_data):
        """
        保存实力数据到数据库
        """
        with sqlite3.connect(self.target_db) as conn:
            cursor = conn.cursor()
            
            # 创建表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS team_strength (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    team_name TEXT NOT NULL,
                    league_name TEXT NOT NULL,
                    attack_strength REAL,
                    defense_strength REAL,
                    home_advantage REAL,
                    away_performance REAL,
                    recent_form REAL,
                    comprehensive_strength REAL,
                    calculated_at TEXT,
                    UNIQUE(team_name, league_name)
                )
            """)
            
            # 插入数据
            cursor.execute("""
                INSERT OR REPLACE INTO team_strength 
                VALUES (NULL, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                strength_data['team_name'],
                strength_data['league_name'],
                strength_data['attack_strength'],
                strength_data['defense_strength'],
                strength_data['home_advantage'],
                strength_data['away_performance'],
                strength_data['recent_form'],
                strength_data['comprehensive_strength'],
                datetime.now().isoformat()
            ))
            
            conn.commit()
```

## 5. 数据爬取模块

### 5.1 比赛数据爬虫 (scrapers/match_scraper.py)

#### 5.1.1 从titan007.com爬取比赛数据
```python
import requests
import re
from datetime import datetime

class MatchScraper:
    def __init__(self):
        self.base_url = "https://livestatic.titan007.com/vbsxml/bfdata_ut.js"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Referer': 'https://live.titan007.com/'
        })
    
    def fetch_matches(self):
        """
        获取比赛数据
        """
        timestamp = int(datetime.now().timestamp() * 1000)
        url = f"{self.base_url}?r={timestamp}"
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            matches = self.parse_match_data(response.text)
            return matches
            
        except requests.RequestException as e:
            raise Exception(f"获取比赛数据失败: {e}")
    
    def parse_match_data(self, content):
        """
        解析比赛数据
        """
        matches = []
        
        # 解析JavaScript数组格式的数据
        pattern = r'A\[(\d+)\]="([^"]+)"'
        
        for match_id, data_string in re.findall(pattern, content):
            fields = data_string.split('^')
            
            if len(fields) >= 8:
                match = {
                    'match_id': match_id,
                    'league_name': fields[2] if len(fields) > 2 else '',
                    'home_team': fields[6] if len(fields) > 6 else '',
                    'away_team': fields[7] if len(fields) > 7 else '',
                    'start_time': fields[3] if len(fields) > 3 else '',
                    'match_status': fields[1] if len(fields) > 1 else '',
                    'scrape_time': datetime.now().isoformat()
                }
                
                # 如果有比分数据
                if len(fields) > 9:
                    match['home_score'] = self.safe_int(fields[8])
                    match['away_score'] = self.safe_int(fields[9])
                
                matches.append(match)
        
        return matches
    
    def safe_int(self, value):
        """
        安全转换为整数
        """
        try:
            return int(value) if value and value != '-' else None
        except ValueError:
            return None
```

### 5.2 赔率数据爬虫 (scrapers/odds_scraper.py)

#### 5.2.1 多博彩公司赔率爬取
```python
class OddsScraper:
    def __init__(self):
        self.target_companies = [
            '威廉希尔', '易胜博', '香港马会', '澳门',
            'BWIN', '伟德', 'Nordicbet', '利记',
            '18BET', 'BetISn', 'iddaa'
        ]
        self.session = requests.Session()
    
    def fetch_match_odds(self, match_id):
        """
        获取指定比赛的赔率数据
        """
        timestamp = int(datetime.now().timestamp() * 1000)
        url = f"https://1x2d.titan007.com/{match_id}.js?r={timestamp}"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': '*/*',
            'Referer': f'https://op1.titan007.com/oddslist/{match_id}.htm'
        }
        
        try:
            response = self.session.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            odds_data = self.parse_odds_data(response.text, match_id)
            return odds_data
            
        except requests.RequestException as e:
            raise Exception(f"获取赔率数据失败: {e}")
    
    def parse_odds_data(self, content, match_id):
        """
        解析赔率数据
        """
        odds_records = []
        
        # 解析JavaScript格式的赔率数据
        # 实际解析逻辑会根据titan007.com的数据格式调整
        lines = content.split('\n')
        
        for line in lines:
            if 'game=' in line:
                # 解析赔率行数据
                odds_info = self.extract_odds_from_line(line, match_id)
                if odds_info:
                    odds_records.append(odds_info)
        
        return odds_records
    
    def extract_odds_from_line(self, line, match_id):
        """
        从数据行中提取赔率信息
        """
        # 根据实际数据格式实现解析逻辑
        # 这里是示例结构
        pattern = r"game\[(\d+)\]\['(\d+)'\]=Array\('([^']+)','([^']+)','([^']+)'.*\)"
        
        match = re.search(pattern, line)
        if match:
            company_id, odds_id, company_name, odds_data, time_data = match.groups()
            
            if company_name in self.target_companies:
                odds_parts = odds_data.split('|')
                
                if len(odds_parts) >= 6:
                    return {
                        'match_id': match_id,
                        'bookmaker': company_name,
                        'initial_home_win': self.safe_float(odds_parts[0]),
                        'initial_draw': self.safe_float(odds_parts[1]),
                        'initial_away_win': self.safe_float(odds_parts[2]),
                        'instant_home_win': self.safe_float(odds_parts[3]),
                        'instant_draw': self.safe_float(odds_parts[4]),
                        'instant_away_win': self.safe_float(odds_parts[5]),
                        'scrape_time': datetime.now().isoformat()
                    }
        
        return None
    
    def safe_float(self, value):
        """
        安全转换为浮点数
        """
        try:
            return float(value) if value and value != '-' else None
        except ValueError:
            return None
```

### 5.3 联赛名称标准化 (scrapers/league_mapping.py)

#### 5.3.1 多层映射机制
```python
class LeagueMapper:
    def __init__(self):
        # 标准联赛名称映射
        self.standard_mappings = {
            "英超": "英格兰超级联赛",
            "西甲": "西班牙甲级联赛",
            "德甲": "德国甲级联赛",
            "意甲": "意大利甲级联赛",
            "法甲": "法国甲级联赛",
            "中超": "中国超级联赛"
        }
        
        # 别名映射
        self.alias_mappings = {
            "Premier League": "英格兰超级联赛",
            "La Liga": "西班牙甲级联赛",
            "Bundesliga": "德国甲级联赛",
            "Serie A": "意大利甲级联赛",
            "Ligue 1": "法国甲级联赛",
            "EPL": "英格兰超级联赛"
        }
        
        # 正则表达式映射
        self.regex_mappings = [
            (r"英格兰.*超", "英格兰超级联赛"),
            (r"西班牙.*甲", "西班牙甲级联赛"),
            (r"德国.*甲", "德国甲级联赛"),
            (r"意大利.*甲", "意大利甲级联赛"),
            (r"法国.*甲", "法国甲级联赛")
        ]
    
    def normalize_league_name(self, raw_name):
        """
        标准化联赛名称
        """
        if not raw_name:
            return raw_name
        
        # 清理空白字符
        raw_name = raw_name.strip()
        
        # 1. 直接映射
        if raw_name in self.standard_mappings:
            return self.standard_mappings[raw_name]
        
        # 2. 别名映射
        if raw_name in self.alias_mappings:
            return self.alias_mappings[raw_name]
        
        # 3. 正则表达式映射
        for pattern, standard_name in self.regex_mappings:
            if re.search(pattern, raw_name, re.IGNORECASE):
                return standard_name
        
        # 4. 返回原名称
        return raw_name
```

## 6. 用户界面设计

### 6.1 双版本界面架构

#### 6.1.1 传统版本 (ui/app.py)
```python
class FootballAnalysisApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("足球比赛分析系统 Plus")
        self.root.geometry("1400x900")
        
        # 创建标签页
        self.create_notebook()
        self.setup_styles()
    
    def create_notebook(self):
        """创建标签页"""
        self.notebook = ttk.Notebook(self.root)
        
        # 基本信息
        self.basic_info_tab = BasicInfoTab(self.notebook)
        self.notebook.add(self.basic_info_tab, text="📊 基本信息")
        
        # 赔率分析
        self.odds_tab = OddsTab(self.notebook)
        self.notebook.add(self.odds_tab, text="💰 赔率分析")
        
        # 基本面分析
        self.fundamental_tab = FundamentalTab(self.notebook)
        self.notebook.add(self.fundamental_tab, text="🎯 基本面分析")
        
        # 区间分析
        self.interval_tab = IntervalTab(self.notebook)
        self.notebook.add(self.interval_tab, text="📈 区间分析")
        
        # 泊松分析
        self.poisson_tab = PoissonTab(self.notebook)
        self.notebook.add(self.poisson_tab, text="🔬 泊松分析")
        
        # 数据管理
        self.data_management_tab = DataManagementTab(self.notebook)
        self.notebook.add(self.data_management_tab, text="🗂️ 数据管理")
        
        # 预测记录
        self.prediction_records_tab = PredictionRecordsTab(self.notebook)
        self.notebook.add(self.prediction_records_tab, text="📝 预测记录")
        
        # 数据更新
        self.data_update_tab = DataUpdateTab(self.notebook)
        self.notebook.add(self.data_update_tab, text="🔄 数据更新")
        
        # 赔率法则
        self.odds_rules_tab = OddsRulesTab(self.notebook)
        self.notebook.add(self.odds_rules_tab, text="📋 赔率法则")
        
        # 信心规则
        self.confidence_rules_tab = ConfidenceRulesTab(self.notebook)
        self.notebook.add(self.confidence_rules_tab, text="🎮 信心规则")
        
        self.notebook.pack(fill=tk.BOTH, expand=True)
```

#### 6.1.2 现代版本 (ui/modern_app.py)
```python
class ModernFootballApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_modern_ui()
    
    def setup_window(self):
        """设置现代化窗口"""
        self.root.title("足球比赛分析系统 Plus v4.0 现代版")
        self.root.geometry("1400x900")
        self.root.configure(bg="#f5f5f7")
        
        # 设置现代化样式
        self.setup_modern_styles()
    
    def setup_modern_styles(self):
        """设置现代化样式"""
        style = ttk.Style()
        
        # 配置现代化主题
        style.theme_use('clam')
        
        # 自定义样式
        style.configure("Modern.TNotebook", 
                       background="#f5f5f7",
                       borderwidth=0)
        
        style.configure("Modern.TNotebook.Tab",
                       padding=[20, 10],
                       font=("Segoe UI", 10, "bold"))
        
        style.configure("Card.TFrame",
                       background="white",
                       relief="flat",
                       borderwidth=1)
```

### 6.2 核心标签页实现

#### 6.2.1 赔率分析标签页 (ui/tabs/odds_tab.py)
```python
class OddsTab(tk.Frame):
    def __init__(self, parent):
        super().__init__(parent, bg=COLOR_BG)
        self.odds_analyzer = OddsAnalyzer()
        self.create_widgets()
    
    def create_widgets(self):
        """创建赔率分析界面"""
        # 比赛选择区域
        self.create_match_selection()
        
        # 满水赔率显示区域
        self.create_true_odds_display()
        
        # 档距分析区域
        self.create_range_analysis()
        
        # 平赔区间分析区域
        self.create_draw_interval_analysis()
    
    def create_true_odds_display(self):
        """创建满水赔率显示区域"""
        frame = ttk.LabelFrame(self, text="满水赔率计算", style="Card.TLabelframe")
        frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 创建表格显示满水赔率
        columns = ("博彩公司", "原始主胜", "原始平局", "原始客胜", 
                  "满水主胜", "满水平局", "满水客胜", "返还率")
        
        self.true_odds_tree = ttk.Treeview(frame, columns=columns, show='headings')
        
        for col in columns:
            self.true_odds_tree.heading(col, text=col)
            self.true_odds_tree.column(col, width=100, anchor='center')
        
        self.true_odds_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def analyze_match_odds(self, match_id):
        """分析比赛赔率"""
        try:
            # 获取赔率数据
            odds_data = self.get_match_odds_data(match_id)
            
            # 计算满水赔率
            true_odds_data = self.odds_analyzer.calculate_all_true_odds(odds_data)
            
            # 更新显示
            self.update_true_odds_display(odds_data, true_odds_data)
            
            # 进行档距分析
            self.perform_range_analysis(true_odds_data)
            
            # 进行平赔区间分析
            self.perform_draw_interval_analysis(true_odds_data)
            
        except Exception as e:
            messagebox.showerror("错误", f"赔率分析失败: {str(e)}")
```

#### 6.2.2 基本面分析标签页 (ui/tabs/fundamental_tab.py)
```python
class FundamentalTab(tk.Frame):
    def __init__(self, parent):
        super().__init__(parent, bg=COLOR_BG)
        self.fundamental_analyzer = FundamentalAnalyzer()
        self.create_widgets()
    
    def create_widgets(self):
        """创建基本面分析界面"""
        # 比赛选择区域
        self.create_match_selection()
        
        # AI分析按钮
        self.create_analysis_controls()
        
        # 分析结果显示区域
        self.create_analysis_display()
    
    def create_analysis_display(self):
        """创建分析结果显示区域"""
        frame = ttk.LabelFrame(self, text="AI基本面分析结果", style="Card.TLabelframe")
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.analysis_text = tk.Text(
            frame,
            wrap=tk.WORD,
            bg="white",
            fg=COLOR_TEXT,
            font=("Segoe UI", 11),
            padx=10,
            pady=10
        )
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=scrollbar.set)
        
        self.analysis_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def perform_ai_analysis(self):
        """执行AI基本面分析"""
        home_team = self.home_team_var.get()
        away_team = self.away_team_var.get()
        league = self.league_var.get()
        
        if not all([home_team, away_team, league]):
            messagebox.showwarning("警告", "请先选择比赛队伍")
            return
        
        # 显示分析进度
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(tk.END, "正在进行AI分析，请稍候...\n\n")
        self.analysis_text.update()
        
        try:
            # 调用AI分析
            result = self.fundamental_analyzer.analyze_match_fundamentals(
                home_team, away_team, league
            )
            
            if result["success"]:
                self.display_analysis_result(result["analysis"])
            else:
                self.analysis_text.delete(1.0, tk.END)
                self.analysis_text.insert(tk.END, f"分析失败: {result['error']}")
                
        except Exception as e:
            messagebox.showerror("错误", f"AI分析失败: {str(e)}")
    
    def display_analysis_result(self, analysis_text):
        """显示分析结果"""
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(tk.END, analysis_text)
```

## 7. 配置和部署

### 7.1 统一配置管理 (config.py)

#### 7.1.1 配置文件结构
```python
from football_analysis_system.core.config_manager import config_manager

# UI配置
FONT_NORMAL = ("Segoe UI", 10)
FONT_LARGE = ("Segoe UI", 12, "bold")
FONT_HEADER = ("Segoe UI", 18, "bold")

# 色彩方案
_ui_colors = config_manager.get('ui.colors', {})
COLOR_PRIMARY = _ui_colors.get('primary', "#1a237e")
COLOR_SECONDARY = _ui_colors.get('secondary', "#3949ab")
COLOR_ACCENT = _ui_colors.get('accent', "#00c853")
COLOR_BG = _ui_colors.get('background', "#f5f5f7")

# 数据库路径配置
PROJECT_ROOT_SIMPLE = os.path.dirname(os.path.abspath(__file__))
DATA_DIR_SIMPLE = os.path.join(PROJECT_ROOT_SIMPLE, 'data')

DB_MATCHES = os.path.join(DATA_DIR_SIMPLE, 'matches_and_odds.db')
DB_GUANGYISHILI = os.path.join(DATA_DIR_SIMPLE, 'guangyishili.db')
DB_STANDARD_ODDS = os.path.join(DATA_DIR_SIMPLE, 'StandardOdds.db')

# 爬虫配置
DATA_URL_TEMPLATE = "https://livestatic.titan007.com/vbsxml/bfdata_ut.js?r={timestamp}"
ODDS_URL_TEMPLATE = "https://1x2d.titan007.com/{match_id}.js?r={timestamp}"

# 目标博彩公司
TARGET_COMPANIES = [
    '威廉希尔', '易胜博', '香港马会', '澳门',
    'BWIN', '伟德', 'Nordicbet', '利记',
    '18BET', 'BetISn', 'iddaa'
]

# API配置
DEEPSEEK_API_KEY = config_manager.get('api.deepseek.api_key')
DEEPSEEK_API_URL = config_manager.get('api.deepseek.api_url')
DEEPSEEK_MODEL = config_manager.get('api.deepseek.model')
```

### 7.2 配置文件 (config/config.json)
```json
{
  "app": {
    "title": "足球比赛分析系统 Plus",
    "version": "v4.0 现代版",
    "debug": false
  },
  "paths": {
    "project_root": "I:\\football_analysis_system 0428\\football_analysis_system\\football_analysis_system",
    "data_dir": "I:\\football_analysis_system 0428\\football_analysis_system\\football_analysis_system\\data",
    "logs_dir": "I:\\football_analysis_system 0428\\football_analysis_system\\football_analysis_system\\logs"
  },
  "database": {
    "matches": "matches_and_odds.db",
    "guangyishili": "guangyishili.db",
    "standard_odds": "StandardOdds.db",
    "max_connections": 10,
    "connection_timeout": 30.0
  },
  "crawler": {
    "max_workers": 10,
    "request_timeout": 30,
    "retry_times": 3,
    "min_delay": 0.5,
    "max_delay": 2.0
  },
  "ui": {
    "theme": "modern",
    "font_family": "Segoe UI",
    "window_size": "1400x900",
    "colors": {
      "primary": "#1a237e",
      "secondary": "#3949ab",
      "accent": "#00c853",
      "background": "#f5f5f7",
      "text": "#212121"
    }
  },
  "api": {
    "deepseek": {
      "api_key": "",
      "api_url": "https://api.deepseek.com/v1/chat/completions",
      "model": "deepseek-reasoner"
    }
  },
  "companies": {
    "115": {"en": "William Hill", "cn": "威廉希尔"},
    "90": {"en": "Easybets", "cn": "易胜博"},
    "432": {"en": "HKJC", "cn": "香港马会"},
    "80": {"en": "Macau", "cn": "澳门"}
  }
}
```

### 7.3 环境依赖
```
Python >= 3.8
tkinter (通常随Python安装)
requests >= 2.25.0
pandas >= 1.3.0
numpy >= 1.21.0
scipy >= 1.7.0
sqlite3 (Python内置)
```

## 8. 启动和运行

### 8.1 启动脚本

#### 8.1.1 传统版本启动
```bash
python main.py
```

#### 8.1.2 现代版本启动
```bash
python run_modern_app.py
```

### 8.2 功能模块使用流程

1. **数据更新**: 进入"数据更新"标签页，点击"开始更新"获取最新比赛和赔率数据
2. **赔率分析**: 在"赔率分析"标签页选择比赛，查看满水赔率、档距分析和平赔区间
3. **基本面分析**: 在"基本面分析"标签页使用AI进行深度分析（需配置DeepSeek API密钥）
4. **区间分析**: 基于球队实力档距进行综合分析
5. **泊松分析**: 使用泊松分布预测比赛比分
6. **数据管理**: 管理数据库和查看系统状态

## 9. 总结

本技术规格说明书基于实际代码分析编写，准确反映了足球比赛分析系统的真实架构和功能。系统的核心特点包括：

### 9.1 核心功能
- **数据爬取**: 从titan007.com实时获取比赛和赔率数据
- **赔率分析**: 满水赔率计算、档距分析、11个平赔区间分析
- **AI分析**: 基于DeepSeek API的基本面分析
- **统计分析**: 泊松分布比分预测、球队实力评估
- **双版本UI**: 传统版和现代版两种界面风格

### 9.2 技术优势
- **模块化架构**: 清晰的代码组织和模块分离
- **多数据库支持**: 8个SQLite数据库分类存储不同类型数据
- **联赛标准化**: 多层映射机制处理联赛名称统一
- **并发爬取**: 多线程数据采集提高效率
- **配置统一管理**: 支持JSON配置和环境变量

### 9.3 实际应用价值
该系统为足球数据分析提供了完整的解决方案，从数据采集到深度分析，适用于足球数据研究、投注参考、比赛预测等多种应用场景。通过本技术文档，开发者可以准确理解和复现系统的全部功能。