"""
自定义日志处理和设置
"""
import logging
import os
import sys
import tkinter as tk
from datetime import datetime
from queue import Queue

# 在导入时设置编码
try:
    if sys.platform == 'win32':
        # 尝试设置Windows控制台代码页
        import subprocess
        subprocess.run(['chcp', '65001'], shell=True, check=False)
        # 设置sys.stdout和sys.stderr的编码
        sys.stdout.reconfigure(encoding='utf-8') if hasattr(sys.stdout, 'reconfigure') else None
        sys.stderr.reconfigure(encoding='utf-8') if hasattr(sys.stderr, 'reconfigure') else None
        print("日志系统已设置控制台编码为UTF-8")
except Exception as e:
    print(f"设置控制台编码失败: {e}")

class TextWidgetHandler(logging.Handler):
    """自定义日志处理器，将日志消息发送到Tkinter文本控件"""

    def __init__(self, message_queue, text_widget=None):
        """
        初始化处理器

        Args:
            message_queue: 消息队列
            text_widget: Tkinter文本控件（可选）
        """
        super().__init__()
        self.message_queue = message_queue
        self.text_widget = text_widget

        # 日志类型映射
        self.log_type_map = {
            logging.DEBUG: "debug",
            logging.INFO: "info",
            logging.WARNING: "warning",
            logging.ERROR: "error",
            logging.CRITICAL: "error",
        }

    def emit(self, record):
        """
        发送日志记录

        Args:
            record: 日志记录
        """
        try:
            # 过滤掉所有与"not found"相关的错误消息
            if record.levelno == logging.ERROR:
                error_msg = record.getMessage()
                # 过滤掉Item ID not found的错误
                if "Item ID" in error_msg and "not found" in error_msg:
                    return
                # 过滤掉删除树视图项时的错误
                if "删除树视图项时出错" in error_msg and "not found" in error_msg:
                    return
                # 过滤掉任何其他包含"not found"的错误消息
                if "not found" in error_msg and "树视图" in error_msg:
                    return

            # 获取格式化的消息
            msg = self.format(record)

            # 确定日志类型
            log_type = self.log_type_map.get(record.levelno, "info")

            # 对于进度更新的特殊消息，发送特殊的元组格式
            if "进度更新:" in msg:
                try:
                    progress_value = int(msg.split("进度更新:")[1].strip())
                    self.message_queue.put(("update_progress", progress_value))
                    return
                except (ValueError, IndexError):
                    pass

            # 添加时间戳
            current_time = datetime.now().strftime("%H:%M:%S")
            formatted_message = f"[{current_time}] {msg}"

            # 放入队列
            self.message_queue.put((formatted_message, log_type))

            # 打印到控制台，确保日志可见
            try:
                print(f"{log_type.upper()}: {formatted_message}")
            except UnicodeEncodeError:
                # 如果编码错误，尝试使用不同的编码
                try:
                    print(f"{log_type.upper()}: {formatted_message}".encode('utf-8').decode(sys.stdout.encoding, errors='replace'))
                except:
                    # 最后的尝试：只打印ASCII字符
                    print(f"{log_type.upper()}: 日志消息包含无法显示的字符")

            # 如果有文本控件，直接更新（仅用于测试）
            if self.text_widget:
                try:
                    self.text_widget.config(state="normal")
                    self.text_widget.insert("end", formatted_message + "\n", log_type)
                    self.text_widget.see("end")
                    self.text_widget.config(state="disabled")
                except Exception as e:
                    print(f"更新日志文本控件时出错: {e}")
        except Exception as e:
            print(f"处理日志记录时出错: {e}")

def setup_logger(name="football_analysis", log_level=logging.INFO, log_file=None, message_queue=None, text_widget=None):
    """
    设置日志记录器

    Args:
        name: 日志记录器名称
        log_level: 日志级别
        log_file: 日志文件路径（可选）
        message_queue: 消息队列（可选）
        text_widget: 文本控件（可选）

    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(log_level)

    # 清空现有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    # 设置控制台处理器编码
    if hasattr(console_handler, 'setStream'):
        try:
            console_handler.setStream(sys.stdout)
        except:
            pass
    logger.addHandler(console_handler)

    # 文件处理器（如果提供了日志文件路径）
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    # Tkinter文本控件处理器（如果提供了消息队列）
    if message_queue:
        tk_handler = TextWidgetHandler(message_queue, text_widget)
        tk_handler.setFormatter(formatter)
        logger.addHandler(tk_handler)

    return logger

def process_message_queue(queue, text_widget):
    """
    处理日志消息队列

    Args:
        queue: 日志消息队列
        text_widget: 目标文本控件
    """
    try:
        # 检查队列中是否有消息
        while not queue.empty():
            message = queue.get_nowait()

            # 处理特殊消息类型（如进度更新）
            if isinstance(message, tuple) and len(message) >= 2:
                if message[0] == "update_progress" and len(message) >= 2:
                    # 这是一个进度更新消息
                    continue

                msg, log_type = message
            else:
                # 默认为普通信息
                msg = str(message)
                log_type = "info"

            # 更新文本控件
            try:
                text_widget.config(state="normal")
                text_widget.insert("end", msg + "\n", log_type)
                text_widget.see("end")
                text_widget.config(state="disabled")
            except Exception as e:
                print(f"处理日志消息时出错: {e}")
    except Exception as e:
        print(f"处理日志消息时出错: {e}")