import tkinter as tk
from tkinter import ttk
from datetime import datetime
import time
from PIL import Image, ImageTk, ImageDraw

class ModernMatchList(ttk.Frame):
    """现代化比赛列表组件"""
    
    def __init__(self, parent, on_select=None, **kwargs):
        super().__init__(parent, style="Card.TFrame", **kwargs)
        
        # 回调函数
        self.on_select = on_select
        
        # 数据存储
        self.matches = []
        self.filtered_matches = []
        self.selected_index = None
        
        # 创建组件
        self._create_widgets()
        
        # 标记颜色
        self._configure_tags()
    
    def _create_widgets(self):
        """创建列表组件"""
        # 主容器
        main_container = ttk.Frame(self, style="Card.TFrame")
        main_container.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
        
        # 标题区域
        header_frame = ttk.Frame(main_container, style="CardHeader.TFrame")
        header_frame.pack(fill=tk.X, pady=0)
        
        header_content = ttk.Frame(header_frame, style="CardHeader.TFrame")
        header_content.pack(fill=tk.X, padx=15, pady=10)
        
        title_label = ttk.Label(
            header_content, 
            text="比赛列表", 
            font=("Segoe UI", 14, "bold"),
            style="CardHeader.TLabel"
        )
        title_label.pack(side=tk.LEFT)
        
        self.count_label = ttk.Label(
            header_content,
            text="0场比赛",
            style="CardHeader.TLabel"
        )
        self.count_label.pack(side=tk.RIGHT)
        
        # 搜索框 (未实现，可扩展)
        
        # 列表区域
        list_frame = ttk.Frame(main_container, style="Card.TFrame")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        # 创建Treeview和滚动条
        self.tree_scroll = ttk.Scrollbar(list_frame, orient="vertical")
        self.tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建树形视图
        self.tree = ttk.Treeview(
            list_frame,
            columns=("league", "match", "time", "status"),
            show="headings",
            yscrollcommand=self.tree_scroll.set,
            height=15,
            style="Treeview"
        )
        
        # 配置树形视图列
        self.tree.heading("league", text="联赛", anchor="center")
        self.tree.heading("match", text="比赛", anchor="center")
        self.tree.heading("time", text="时间", anchor="center")
        self.tree.heading("status", text="状态", anchor="center")
        
        self.tree.column("league", width=100, anchor="center")
        self.tree.column("match", width=200, anchor="center")
        self.tree.column("time", width=100, anchor="center")
        self.tree.column("status", width=80, anchor="center")
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.tree_scroll.config(command=self.tree.yview)
        
        # 绑定事件
        self.tree.bind("<Double-1>", self._on_item_double_click)
        self.tree.bind("<Return>", self._on_item_double_click)
        self.tree.bind("<<TreeviewSelect>>", self._on_item_select)
    
    def _configure_tags(self):
        """配置树形视图标签样式"""
        # 有马会数据：绿色
        self.tree.tag_configure('has_hkjc', 
                              background='#dcfce7', 
                              foreground='#166534')
        
        # 有数据无马会：黄色
        self.tree.tag_configure('has_odds_no_hkjc', 
                              background='#fef3c7', 
                              foreground='#92400e')
        
        # 无数据：红色
        self.tree.tag_configure('no_odds', 
                              background='#fee2e2', 
                              foreground='#991b1b')
        
        # 选中行样式
        self.tree.tag_configure('selected',
                              background='#60a5fa',
                              foreground='#ffffff')
    
    def _on_item_double_click(self, event):
        """双击事件处理"""
        self._handle_selection()
    
    def _on_item_select(self, event):
        """选择事件处理"""
        # 可以在这里添加单击选择的处理逻辑
        pass
    
    def _handle_selection(self):
        """处理选择"""
        selection = self.tree.selection()
        if selection and self.on_select:
            item = self.tree.item(selection[0])
            # 从标签中获取索引
            tags = item['tags']
            if tags and len(tags) > 0:
                try:
                    # 第一个tag是索引
                    index = int(tags[0])
                    self.selected_index = index
                    self.on_select(index)
                except (ValueError, IndexError):
                    pass
    
    def update_matches(self, matches):
        """更新比赛列表数据"""
        self.matches = matches
        self.filtered_matches = matches
        
        # 清空现有项
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加新项目
        for i, match in enumerate(matches):
            # 确定状态和标签
            has_odds = match.get('has_odds', False)
            has_hkjc = match.get('has_hkjc', False)
            
            if has_hkjc:
                status_icon = "✅"  # 有马会数据
                tag = 'has_hkjc'
            elif has_odds:
                status_icon = "⚠️"  # 有赔率无马会
                tag = 'has_odds_no_hkjc'
            else:
                status_icon = "❌"  # 无数据
                tag = 'no_odds'
            
            # 格式化时间
            match_time = match.get('time', '')
            formatted_time = match_time
            if match_time:
                try:
                    dt = datetime.strptime(match_time, '%Y-%m-%d %H:%M:%S')
                    formatted_time = dt.strftime('%H:%M')
                except:
                    formatted_time = match_time
            
            # 插入项目
            self.tree.insert(
                "",
                tk.END,
                values=(
                    match.get('league', ''),
                    match.get('match_name', ''),
                    formatted_time,
                    status_icon
                ),
                tags=(str(i), tag)
            )
        
        # 更新计数
        self.count_label.configure(text=f"{len(matches)}场比赛")
    
    def filter_matches(self, filter_func=None):
        """根据过滤函数筛选比赛"""
        if filter_func:
            self.filtered_matches = [m for m in self.matches if filter_func(m)]
        else:
            self.filtered_matches = self.matches
        
        # 更新显示
        self.update_matches(self.filtered_matches)
    
    def get_selected_match(self):
        """获取当前选中的比赛"""
        if self.selected_index is not None and self.selected_index < len(self.filtered_matches):
            return self.filtered_matches[self.selected_index]
        return None
    
    def select_match_by_index(self, index):
        """通过索引选择比赛"""
        if index >= 0 and index < len(self.filtered_matches):
            # 找到对应的树项
            children = self.tree.get_children()
            if index < len(children):
                self.tree.selection_set(children[index])
                self.tree.see(children[index])
                self.selected_index = index
    
    def select_first_match(self):
        """选择第一个比赛"""
        children = self.tree.get_children()
        if children:
            self.tree.selection_set(children[0])
            self.tree.see(children[0])
            self.selected_index = 0
            if self.on_select:
                self.on_select(0)


class MatchList(ttk.Frame):
    """比赛列表 (旧版本 - 兼容保留)"""
    
    def __init__(self, parent, on_select=None):
        super().__init__(parent)
        self.on_select = on_select
        
        # 创建标题和树视图
        self._create_widgets()
    
    def _create_widgets(self):
        # 创建标题
        self.title_label = ttk.Label(self, text="比赛列表", style="Header.TLabel")
        self.title_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        # 创建树形视图
        columns = ("league", "match", "time", "status")
        self.tree = ttk.Treeview(self, columns=columns, show="headings", height=20)
        
        # 配置列
        self.tree.heading("league", text="联赛")
        self.tree.heading("match", text="比赛")
        self.tree.heading("time", text="时间")
        self.tree.heading("status", text="状态")
        
        self.tree.column("league", width=100)
        self.tree.column("match", width=200)
        self.tree.column("time", width=100)
        self.tree.column("status", width=80)
        
        # 创建滚动条
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=self.scrollbar.set)
        
        # 布局
        self.scrollbar.pack(side="right", fill="y")
        self.tree.pack(side="left", fill="both", expand=True)
        
        # 配置标签
        self.tree.tag_configure('has_hkjc', background='#d1e7dd', foreground='#0a3622')
        self.tree.tag_configure('has_odds_no_hkjc', background='#fff3cd', foreground='#664d03')
        self.tree.tag_configure('no_odds', background='#f8d7da', foreground='#58151c')
        
        # 绑定事件
        self.tree.bind("<Double-1>", self._on_select)
        self.tree.bind("<Return>", self._on_select)
    
    def _on_select(self, event):
        selection = self.tree.selection()
        if selection and self.on_select:
            item = self.tree.item(selection[0])
            tags = item['tags']
            if tags:
                try:
                    index = int(tags[0])
                    self.on_select(index)
                except:
                    pass
    
    def update_matches(self, matches):
        # 清空列表
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加新项目
        for i, match in enumerate(matches):
            has_odds = match.get('has_odds', False)
            has_hkjc = match.get('has_hkjc', False)
            
            if not has_odds:
                status = "无数据"
                tag = 'no_odds'
            elif has_hkjc:
                status = "完整"
                tag = 'has_hkjc'
            else:
                status = "部分"
                tag = 'has_odds_no_hkjc'
            
            self.tree.insert("", tk.END, values=(
                match.get('league', ''),
                match.get('match_name', ''),
                match.get('time', ''),
                status
            ), tags=(str(i), tag)) 