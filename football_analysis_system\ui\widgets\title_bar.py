import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk, ImageDraw, ImageFilter, ImageEnhance
import os

from football_analysis_system.config import APP_TITLE, APP_VERSION, COLOR_PRIMARY, COLOR_SECONDARY, COLOR_BG, COLOR_TEXT

class ModernTitleBar(ttk.Frame):
    """现代化标题栏组件"""
    
    def __init__(self, parent, on_scrape=None, on_cancel=None, **kwargs):
        super().__init__(parent, **kwargs)
        self.parent = parent
        self.on_scrape = on_scrape
        self.on_cancel = on_cancel
        self.scraping_active = False
        
        # 配置标题栏样式
        self.configure(style="Card.TFrame")
        
        # 创建标题栏内容
        self._create_widgets()
        
    def _create_widgets(self):
        """创建标题栏组件"""
        # 主容器使用水平布局
        main_frame = ttk.Frame(self, style="Card.TFrame")
        main_frame.pack(fill=tk.X, padx=20, pady=(15, 12))
        
        # 左侧：应用图标和标题
        left_frame = ttk.Frame(main_frame, style="Card.TFrame")
        left_frame.pack(side=tk.LEFT)
        
        # 创建足球图标
        self.app_icon = self._create_football_icon(40)
        icon_label = ttk.Label(left_frame, image=self.app_icon, style="Card.TLabel")
        icon_label.pack(side=tk.LEFT, padx=(0, 15))
        
        # 标题文本区域
        title_frame = ttk.Frame(left_frame, style="Card.TFrame")
        title_frame.pack(side=tk.LEFT)
        
        # 主标题
        title_label = ttk.Label(title_frame, 
                             text="足球数据分析中心", 
                             font=("Segoe UI", 18, "bold"),
                             foreground="#3B82F6",
                             background="white")
        title_label.pack(anchor="w")
        
        # 版本号
        version_label = ttk.Label(title_frame, 
                               text=f"Pro v{APP_VERSION}", 
                               font=("Segoe UI", 9),
                               foreground="#64748B",
                               background="white")
        version_label.pack(anchor="w")
        
        # 右侧：控制按钮区域
        right_frame = ttk.Frame(main_frame, style="Card.TFrame")
        right_frame.pack(side=tk.RIGHT)
        
        # 创建主题切换按钮
        self.theme_button = ttk.Button(right_frame, 
                                    text="🌙 暗色模式", 
                                    style="Secondary.TButton",
                                    command=self._toggle_theme)
        self.theme_button.pack(side=tk.RIGHT, padx=(0, 10))
        
        # 数据更新按钮
        self.scrape_button = ttk.Button(right_frame, 
                                     text="📊 更新数据", 
                                     style="Primary.TButton",
                                     command=self._on_scrape_click)
        self.scrape_button.pack(side=tk.RIGHT, padx=(0, 10))
        
        # 取消按钮（初始隐藏）
        self.cancel_button = ttk.Button(right_frame, 
                                     text="⏹ 停止更新", 
                                     style="Error.TButton",
                                     command=self._on_cancel_click)
        # 不显示，需要时再显示
    
    def _toggle_theme(self):
        """切换暗色/亮色主题"""
        # 这里只是按钮样式改变，实际主题切换需要在应用层实现
        if self.theme_button.cget("text") == "🌙 暗色模式":
            self.theme_button.configure(text="☀️ 亮色模式")
        else:
            self.theme_button.configure(text="🌙 暗色模式")
            
        # 通知主应用切换主题 (通过事件或回调实现)
    
    def _on_scrape_click(self):
        """处理数据更新按钮点击"""
        if self.on_scrape:
            self.on_scrape()
    
    def _on_cancel_click(self):
        """处理取消按钮点击"""
        if self.on_cancel:
            self.on_cancel()
    
    def set_scraping_state(self, active: bool):
        """设置爬取状态"""
        self.scraping_active = active
        if active:
            # 切换到取消按钮
            self.scrape_button.pack_forget()
            self.cancel_button.pack(side=tk.RIGHT, padx=(0, 10))
            # 添加更新进行中的提示
            self.scrape_button.configure(text="⏳ 更新中...")
        else:
            # 恢复原状
            self.cancel_button.pack_forget()
            self.scrape_button.pack(side=tk.RIGHT, padx=(0, 10))
            self.scrape_button.configure(text="📊 更新数据")
    
    def _create_football_icon(self, size=48):
        """创建足球图标"""
        # 创建透明背景
        icon = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(icon)
        
        # 球的主体
        ball_color = "#333333"
        bg_color = "#4361EE"  # 蓝色背景
        
        # 绘制圆形背景
        draw.ellipse([2, 2, size-2, size-2], fill=bg_color)
        
        # 绘制球的图案 - 简化的足球图案
        ball_size = int(size * 0.65)
        offset = (size - ball_size) // 2
        draw.ellipse([offset, offset, offset+ball_size, offset+ball_size], 
                    fill="white", outline=ball_color, width=1)
        
        # 绘制五边形图案
        center_x, center_y = size // 2, size // 2
        radius = ball_size // 3
        
        for i in range(5):
            angle = i * (360/5)
            x1 = center_x + int(radius * 0.8 * (0.5 if i % 2 else 1))
            y1 = center_y + int(radius * 0.8 * (0.5 if i < 3 else -0.5))
            
            draw.regular_polygon(((x1, y1), 6), 5, rotation=angle,
                               fill=ball_color, outline=ball_color)
        
        return ImageTk.PhotoImage(icon)


class TitleBar(tk.Frame):
    """标题栏类（旧版-为兼容保留）"""
    
    def __init__(self, parent, on_scrape=None, on_cancel=None):
        super().__init__(parent, bg=COLOR_BG)
        
        # 配置
        self.parent = parent
        self.on_scrape = on_scrape
        self.on_cancel = on_cancel
        self.scraping_active = False
        
        # 创建组件
        self._create_widgets()
    
    def _create_widgets(self):
        # 主容器
        main_container = tk.Frame(self, bg=COLOR_BG, padx=10, pady=10)
        main_container.pack(fill=tk.X)
        
        # 应用标题
        title_label = tk.Label(main_container, 
                            text=APP_TITLE, 
                            font=("Helvetica", 18, "bold"),
                            bg=COLOR_BG, fg=COLOR_TEXT)
        title_label.pack(side=tk.LEFT)
        
        # 版本号
        version_label = tk.Label(main_container,
                              text=f"v{APP_VERSION}",
                              font=("Helvetica", 10),
                              bg=COLOR_BG, fg=COLOR_TEXT)
        version_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 控制按钮
        control_frame = tk.Frame(main_container, bg=COLOR_BG)
        control_frame.pack(side=tk.RIGHT)
        
        # 数据更新按钮
        self.scrape_button = ttk.Button(control_frame,
                                     text="更新数据",
                                     style="Primary.TButton",
                                     command=self._on_scrape_click)
        self.scrape_button.pack(side=tk.RIGHT, padx=5)
        
        # 取消按钮（初始不显示）
        self.cancel_button = ttk.Button(control_frame,
                                     text="停止",
                                     style="Error.TButton",
                                     command=self._on_cancel_click)
    
    def _on_scrape_click(self):
        if self.on_scrape:
            self.on_scrape()
    
    def _on_cancel_click(self):
        if self.on_cancel:
            self.on_cancel()
    
    def set_scraping_state(self, active):
        self.scraping_active = active
        if active:
            self.scrape_button.pack_forget()
            self.cancel_button.pack(side=tk.RIGHT, padx=5)
        else:
            self.cancel_button.pack_forget()
            self.scrape_button.pack(side=tk.RIGHT, padx=5)