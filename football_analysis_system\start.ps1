# PowerShell脚本，启动足球分析系统
# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8
$env:PYTHONIOENCODING = "utf-8"

Write-Host "正在启动足球分析系统..." -ForegroundColor Cyan

# 获取脚本所在目录
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptDir

# 设置Python路径（如果不在PATH中）
$pythonCmd = "python"

# 检查Python是否可用
try {
    $pythonVersion = & $pythonCmd --version 2>&1
    Write-Host "使用 $pythonVersion" -ForegroundColor Green
}
catch {
    Write-Host "Python未找到，请安装Python或设置正确的路径" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 启动Python脚本
try {
    Write-Host "正在启动程序..." -ForegroundColor Cyan
    & $pythonCmd main.py
    $exitCode = $LASTEXITCODE
    
    if ($exitCode -ne 0) {
        Write-Host "程序运行出错，错误代码: $exitCode" -ForegroundColor Red
    }
    else {
        Write-Host "程序已正常退出" -ForegroundColor Green
    }
}
catch {
    Write-Host "启动失败: $_" -ForegroundColor Red
}

Read-Host "按回车键关闭窗口" 