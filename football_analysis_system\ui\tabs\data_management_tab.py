import tkinter as tk
from tkinter import ttk, messagebox
import threading
import queue
from datetime import datetime
import os
import sys
import logging

# 添加父级目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from football_analysis_system.config import (
    COLOR_BG, COLOR_PRIMARY, COLOR_TEXT, COLOR_ACCENT, COLOR_SUCCESS,
    FONT_LARGE, FONT_NORMAL, FONT_SUBHEADER,
    DB_GUANGYISHILI, DATA_DIR
)

# 导入爬虫和广义实力计算模块，使用延迟导入方式
from football_analysis_system.scrapers.match_odds_History import get_football_crawler
from football_analysis_system.analysis.guangyishili_analyzer import main as calculate_guangyishili

class DataManagementTab(tk.Frame):
    """数据管理标签页"""

    def __init__(self, parent, message_queue):
        """
        初始化数据管理标签页

        Args:
            parent: 父容器
            message_queue: 消息队列，用于显示进度
        """
        super().__init__(parent, bg=COLOR_BG)

        self.message_queue = message_queue
        self.scraping_in_progress = False
        self.calculation_in_progress = False
        self.is_destroyed = False  # 添加销毁标记

        # 初始化数据库路径
        self.source_db_path = os.path.join(DATA_DIR, "football.db")
        self.target_db_path = DB_GUANGYISHILI

        # 创建自定义日志处理器
        self.log_handler = None

        self.create_widgets()

        # 设置自动刷新日志
        self.after(100, self.process_log_queue)

        # 检查数据库路径
        self._check_database_paths()

    def _check_database_paths(self):
        """检查数据库路径是否存在"""
        # 检查数据目录
        if not os.path.exists(DATA_DIR):
            os.makedirs(DATA_DIR)
            self.append_log(f"创建数据目录: {DATA_DIR}", "info")

        # 检查源数据库
        if not os.path.exists(self.source_db_path):
            self.append_log(f"警告: 源数据库不存在: {self.source_db_path}", "warning")
        else:
            self.append_log(f"源数据库检查正常: {self.source_db_path}", "info")

        # 检查目标数据库目录
        target_dir = os.path.dirname(self.target_db_path)
        if not os.path.exists(target_dir):
            os.makedirs(target_dir)
            self.append_log(f"创建目标数据库目录: {target_dir}", "info")

    def create_widgets(self):
        """创建标签页控件"""
        # 主容器
        main_container = ttk.Frame(self)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 数据更新区域
        self.create_update_section(main_container)

        # 分隔线
        separator = ttk.Separator(main_container, orient='horizontal')
        separator.pack(fill=tk.X, pady=20)

        # 广义实力计算区域
        self.create_calculation_section(main_container)

        # 日志区域
        self.create_log_section(main_container)

    def create_update_section(self, parent):
        """创建数据更新区域"""
        # 数据更新框架
        update_frame = ttk.LabelFrame(parent, text="联赛数据更新", style="TLabelframe")
        update_frame.pack(fill=tk.X, pady=(0, 10))

        # 更新说明
        update_info = ttk.Label(
            update_frame,
            text="更新所有联赛的最新比赛数据，包括比赛结果和赔率信息。",
            style="Info.TLabel"
        )
        update_info.pack(pady=10, padx=10)

        # 按钮和状态区域
        update_control = ttk.Frame(update_frame)
        update_control.pack(fill=tk.X, padx=10, pady=10)

        # 创建按钮框架，使按钮更加突出
        button_frame = ttk.Frame(update_control, style="Card.TFrame")
        button_frame.pack(side=tk.LEFT, padx=5)

        # 更新按钮 - 使用更明显的样式和图标
        self.update_button = ttk.Button(
            button_frame,
            text="🔄 开始更新",
            command=self.start_update,
            style="Action.TButton",
            width=15
        )
        self.update_button.pack(padx=5, pady=5)

        # 更新状态
        self.update_status = ttk.Label(
            update_control,
            text="就绪",
            style="Status.TLabel"
        )
        self.update_status.pack(side=tk.LEFT, padx=10)

        # 更新进度条
        self.update_progress = ttk.Progressbar(
            update_frame,
            orient="horizontal",
            mode="determinate",
            length=300
        )

    def create_calculation_section(self, parent):
        """创建广义实力计算区域"""
        # 计算框架
        calc_frame = ttk.LabelFrame(parent, text="广义实力计算", style="TLabelframe")
        calc_frame.pack(fill=tk.X, pady=(0, 10))

        # 计算说明
        calc_info = ttk.Label(
            calc_frame,
            text="基于最新的比赛数据计算各支球队的广义实力评分。",
            style="Info.TLabel"
        )
        calc_info.pack(pady=10, padx=10)

        # 上次计算时间
        self.last_calc_time = ttk.Label(
            calc_frame,
            text="上次计算时间：未知",
            style="Info.TLabel"
        )
        self.last_calc_time.pack(pady=(0, 10), padx=10)

        # 按钮和状态区域
        calc_control = ttk.Frame(calc_frame)
        calc_control.pack(fill=tk.X, padx=10, pady=10)

        # 创建按钮框架，使按钮更加突出
        button_frame = ttk.Frame(calc_control, style="Card.TFrame")
        button_frame.pack(side=tk.LEFT, padx=5)

        # 计算按钮 - 使用更明显的样式和图标
        self.calc_button = ttk.Button(
            button_frame,
            text="🧮 开始计算",
            command=self.start_calculation,
            style="Action.TButton",
            width=15
        )
        self.calc_button.pack(padx=5, pady=5)

        # 计算状态
        self.calc_status = ttk.Label(
            calc_control,
            text="就绪",
            style="Status.TLabel"
        )
        self.calc_status.pack(side=tk.LEFT, padx=10)

        # 计算进度条
        self.calc_progress = ttk.Progressbar(
            calc_frame,
            orient="horizontal",
            mode="determinate",
            length=300
        )

    def create_log_section(self, parent):
        """创建日志显示区域"""
        # 日志框架
        log_frame = ttk.LabelFrame(parent, text="操作日志", style="TLabelframe")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 日志文本框
        self.log_text = tk.Text(
            log_frame,
            wrap=tk.WORD,
            height=10,
            bg=COLOR_BG,
            fg=COLOR_TEXT,
            font=("Consolas", 10)
        )
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 滚动条
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        # 禁用文本框编辑
        self.log_text.config(state=tk.DISABLED)

        # 设置日志标签样式
        self.log_text.tag_configure("debug", foreground="gray")
        self.log_text.tag_configure("info", foreground=COLOR_TEXT)
        self.log_text.tag_configure("success", foreground=COLOR_SUCCESS)
        self.log_text.tag_configure("error", foreground="#e74c3c")
        self.log_text.tag_configure("warning", foreground="#f39c12")

    def setup_logger(self):
        """设置日志处理器"""
        # 创建自定义日志处理器
        class TextHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget

            def emit(self, record):
                msg = self.format(record)
                # 使用after方法确保在主线程中更新UI
                self.text_widget.after(0, self.append_log, msg, record.levelname)

            def append_log(self, msg, levelname):
                self.text_widget.config(state=tk.NORMAL)
                self.text_widget.insert(tk.END, msg + "\n")
                self.text_widget.see(tk.END)
                self.text_widget.config(state=tk.DISABLED)

        # 配置根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)

        # 创建并配置处理器
        handler = TextHandler(self.log_text)
        handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

        # 添加处理器到根日志记录器
        root_logger.addHandler(handler)

        # 保存处理器引用以便后续清理
        self.log_handler = handler

    def start_update(self):
        """开始更新数据"""
        if self.scraping_in_progress:
            messagebox.showwarning("提示", "数据更新正在进行中，请等待完成...")
            return

        self.scraping_in_progress = True
        self.update_button.config(state=tk.DISABLED)
        self.update_status.config(text="更新中...")
        self.update_progress.pack(fill=tk.X, padx=10, pady=(0, 10))
        self.update_progress.start(10)

        # 启动更新线程
        threading.Thread(target=self._run_update, daemon=True).start()

    def _run_update(self):
        """在后台线程中运行数据更新"""
        try:
            self.append_log("开始更新联赛数据...", "info")

            # 获取FootballCrawler类，然后创建实例并运行
            FootballCrawler = get_football_crawler()
            crawler = FootballCrawler()
            crawler.run()

            self.after(0, self._update_complete, True)

        except Exception as e:
            import traceback
            error_msg = f"错误详情:\n{traceback.format_exc()}"
            self.after(0, self._update_complete, False, error_msg)

    def _update_complete(self, success, error_message=None):
        """完成数据更新"""
        self.scraping_in_progress = False
        self.update_button.config(state=tk.NORMAL)
        self.update_progress.stop()
        self.update_progress.pack_forget()

        if success:
            self.update_status.config(text="更新完成")
            self.append_log("数据更新完成", "success")
        else:
            self.update_status.config(text="更新失败")
            self.append_log(f"数据更新失败: {error_message}", "error")

    def start_calculation(self):
        """开始计算广义实力"""
        if self.calculation_in_progress:
            messagebox.showwarning("提示", "广义实力计算正在进行中，请等待完成...")
            return

        self.calculation_in_progress = True
        self.calc_button.config(state=tk.DISABLED)
        self.calc_status.config(text="计算中...")
        self.calc_progress.pack(fill=tk.X, padx=10, pady=(0, 10))
        self.calc_progress.start(10)

        # 启动计算线程
        threading.Thread(target=self._run_calculation, daemon=True).start()

    def _run_calculation(self):
        """在后台线程中运行广义实力计算"""
        try:
            self.append_log("开始计算广义实力...", "info")

            # 调用广义实力计算方法
            calculate_guangyishili(
                source_db_path=self.source_db_path,
                target_db_path=self.target_db_path
            )

            self.after(0, self._calculation_complete, True)

        except Exception as e:
            import traceback
            error_msg = f"错误详情:\n{traceback.format_exc()}"
            self.after(0, self._calculation_complete, False, error_msg)

    def _calculation_complete(self, success, error_message=None):
        """完成广义实力计算"""
        self.calculation_in_progress = False
        self.calc_button.config(state=tk.NORMAL)
        self.calc_progress.stop()
        self.calc_progress.pack_forget()

        if success:
            self.calc_status.config(text="计算完成")
            self.last_calc_time.config(text=f"上次计算时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.append_log("广义实力计算完成", "success")
        else:
            self.calc_status.config(text="计算失败")
            self.append_log(f"广义实力计算失败: {error_message}", "error")

    def process_log_queue(self):
        """处理日志队列中的消息"""
        # 如果组件已被销毁，不再处理
        if self.is_destroyed:
            return

        try:
            while True:
                message = self.message_queue.get_nowait()
                # 检查消息类型并相应处理
                if isinstance(message, dict):
                    # 如果是字典，按原方式处理
                    self.append_log(message['message'], message.get('level', 'info'))
                elif isinstance(message, tuple) and len(message) >= 1:
                    # 如果是元组，使用第一个元素作为消息，默认level为info
                    msg = message[0]
                    level = message[1] if len(message) > 1 else 'info'
                    self.append_log(msg, level)
                else:
                    # 如果是其他类型，直接转换为字符串
                    self.append_log(str(message), 'info')
        except queue.Empty:
            pass
        except Exception as e:
            # 捕获所有可能的异常
            logging.error(f"处理日志队列时出错: {e}")
        finally:
            # 如果组件未被销毁，继续处理队列
            if not self.is_destroyed:
                self.after(100, self.process_log_queue)

    def append_log(self, message, level="info"):
        """添加日志消息"""
        # 检查组件是否已销毁
        if self.is_destroyed or not self.winfo_exists():
            return

        try:
            self.log_text.config(state=tk.NORMAL)
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.log_text.insert(tk.END, f"[{timestamp}] {message}\n", level)
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)
        except Exception as e:
            # 捕获可能的错误（如组件已销毁）
            logging.error(f"添加日志时出错: {e}")
            self.is_destroyed = True  # 如果出错，标记为已销毁

    def destroy(self):
        """清理资源"""
        # 标记为已销毁，防止定时器继续运行
        self.is_destroyed = True

        # 移除日志处理器
        if self.log_handler:
            logging.getLogger().removeHandler(self.log_handler)

        # 继续原有的销毁流程
        super().destroy()