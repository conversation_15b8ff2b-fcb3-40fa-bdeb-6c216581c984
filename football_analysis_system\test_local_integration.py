#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用本地数据测试集成的历史赔率功能
"""

import sqlite3
import logging
from scrapers.odds_scraper import OddsScraper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 模拟JS内容（从本地文件读取）
def get_local_js_content():
    """获取本地JS内容"""
    try:
        # 读取game数据
        with open("game_data.txt", "r", encoding="utf-8") as f:
            game_content = f.read().strip()
        
        # 构造完整的JS内容，包含gameDetail
        js_content = f"""
var game = Array({game_content});

var gameDetail = Array("144414641^1.75|4.2|3.4|08-03 13:58|0.93|0.91|0.86|2025;1.75|4|3.7|08-03 13:56|0.93|0.86|0.93|2025;1.7|4|3.8|08-03 13:46|0.90|0.86|0.96|2025;1.67|4|4|08-03 13:37|0.89|0.86|1.01|2025;1.67|4.4|3.7|08-03 12:57|0.89|0.95|0.93|2025;1.67|4.5|3.6|08-03 12:55|0.89|0.97|0.91|2025;1.67|4|4|08-03 12:28|0.89|0.86|1.01|2025;1.67|4.4|3.7|08-03 12:20|0.89|0.95|0.93|2025;1.67|4|4|08-03 12:12|0.89|0.86|1.01|2025;1.7|3.9|4|08-03 08:41|0.90|0.84|1.01|2025;1.75|3.8|3.8|08-03 07:05|0.93|0.82|0.96|2025;1.85|3.8|3.3|08-02 23:25|0.98|0.82|0.83|2025;1.8|3.8|3.6|08-02 23:13|0.96|0.82|0.91|2025;1.85|4|3.1|08-02 22:49|0.98|0.86|0.78|2025;1.67|4|4|08-02 20:06|0.89|0.86|1.01|2025;1.7|4|3.9|08-02 18:44|0.90|0.86|0.98|2025;1.65|4.33|3.9|08-02 17:37|0.88|0.93|0.98|2025","144418332^1.71|4|3.5|08-03 13:59|0.91|0.86|0.88|2025;1.65|4.1|3.75|08-03 13:48|0.88|0.88|0.94|2025;1.65|4.1|3.7|08-03 12:21|0.88|0.88|0.93|2025;1.65|4|3.75|08-03 12:17|0.88|0.86|0.94|2025;1.65|4.2|3.7|08-03 12:14|0.88|0.91|0.93|2025;1.63|4.2|3.6|08-03 12:12|0.87|0.91|0.91|2025;1.66|4.1|3.7|08-03 12:10|0.88|0.88|0.93|2025;1.68|4|3.7|08-03 11:44|0.89|0.86|0.93|2025;1.66|3.9|3.8|08-03 09:03|0.88|0.84|0.96|2025;1.68|3.8|3.75|08-03 08:03|0.89|0.82|0.94|2025;1.68|3.9|3.7|08-03 07:50|0.89|0.84|0.93|2025;1.7|4.1|3.5|08-03 05:58|0.90|0.88|0.88|2025;1.7|4.2|3.4|08-03 05:38|0.90|0.91|0.86|2025;1.8|4|3.2|08-03 00:18|0.96|0.86|0.81|2025;1.82|3.7|3.3|08-02 23:20|0.97|0.80|0.83|2025;1.82|3.8|3.2|08-02 23:08|0.97|0.82|0.81|2025;1.85|3.9|3.1|08-02 23:05|0.98|0.84|0.78|2025;1.82|3.9|3.2|08-02 22:43|0.97|0.84|0.81|2025;1.71|3.9|3.6|08-02 22:40|0.91|0.84|0.91|2025;1.63|3.9|4|08-02 22:03|0.87|0.84|1.01|2025","144421767^1.7|4.33|3.6|08-03 13:59|0.90|0.93|0.91|2025;1.67|4.33|3.8|08-03 13:56|0.89|0.93|0.96|2025;1.65|4.33|3.9|08-03 12:28|0.88|0.93|0.98|2025;1.67|4.33|3.8|08-03 12:24|0.89|0.93|0.96|2025;1.65|4.33|3.9|08-03 12:15|0.88|0.93|0.98|2025;1.65|4.2|4|08-03 12:12|0.88|0.91|1.01|2025;1.67|4.2|3.9|08-03 12:09|0.89|0.91|0.98|2025;1.65|4.2|4|08-03 12:06|0.88|0.91|1.01|2025;1.67|4.2|3.9|08-03 10:00|0.89|0.91|0.98|2025;1.65|4.2|4|08-03 09:54|0.88|0.91|1.01|2025;1.67|4.2|3.9|08-03 06:52|0.89|0.91|0.98|2025;1.7|4.1|3.8|08-03 06:04|0.90|0.88|0.96|2025;1.67|4.2|3.9|08-03 06:00|0.89|0.91|0.98|2025;1.73|4.1|3.7|08-03 05:32|0.92|0.88|0.93|2025;1.73|4.1|3.6|08-03 04:51|0.92|0.88|0.91|2025;1.73|4.1|3.7|08-03 04:45|0.92|0.88|0.93|2025;1.75|4.1|3.6|08-03 03:18|0.93|0.88|0.91|2025;1.73|4.1|3.7|08-03 02:47|0.92|0.88|0.93|2025;1.73|4|3.75|08-03 01:30|0.92|0.86|0.94|2025;1.75|3.9|3.7|08-03 01:12|0.93|0.84|0.93|2025;1.8|3.9|3.5|08-03 01:05|0.96|0.84|0.88|2025","144426703^1.7|4|3.5|08-03 13:56|0.90|0.86|0.88|2025;1.67|4.1|3.5|08-03 13:55|0.89|0.88|0.88|2025;1.67|4.1|3.6|08-03 12:59|0.89|0.88|0.91|2025;1.67|4.2|3.6|08-03 12:56|0.89|0.91|0.91|2025;1.65|4.2|3.7|08-03 12:32|0.88|0.91|0.93|2025;1.67|4|3.8|08-03 12:13|0.89|0.86|0.96|2025;1.65|4|3.8|08-03 12:08|0.88|0.86|0.96|2025;1.65|3.9|3.8|08-03 10:13|0.88|0.84|0.96|2025;1.67|3.9|3.9|08-03 08:17|0.89|0.84|0.98|2025;1.7|3.8|3.5|08-03 06:00|0.90|0.82|0.88|2025;1.8|3.8|3.4|08-03 00:00|0.96|0.82|0.86|2025");
"""
        return js_content
        
    except FileNotFoundError:
        print("❌ 未找到本地数据文件")
        return None

def test_local_integration():
    """测试本地集成功能"""
    
    print("🧪 使用本地数据测试集成历史赔率功能")
    print("=" * 60)
    
    # 1. 检查初始状态
    print("1️⃣ 检查初始数据库状态:")
    try:
        conn = sqlite3.connect("data/matches_and_odds.db")
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM history_odds;")
        initial_count = cursor.fetchone()[0]
        print(f"📊 初始记录数: {initial_count}")
        conn.close()
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return
    
    # 2. 初始化爬虫
    print("\n2️⃣ 初始化爬虫:")
    try:
        scraper = OddsScraper(
            url_template="https://1x2d.titan007.com/{match_id}.js?r={timestamp}",
            headers_template={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': '*/*',
                'Referer': 'https://op1.titan007.com/oddslist/{match_id}.htm',
            },
            target_companies=['澳门', '威廉希尔', '易胜博', '香港马会', 'BWIN', '伟德'],
            db_path="data/matches_and_odds.db"
        )
        print("✅ 爬虫初始化成功")
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {e}")
        return
    
    # 3. 测试历史赔率解析
    print("\n3️⃣ 测试历史赔率解析:")
    test_match_id = "2696009"
    
    try:
        # 获取本地JS内容
        js_content = get_local_js_content()
        if not js_content:
            print("❌ 无法获取本地JS内容")
            return
        
        print(f"正在解析比赛 {test_match_id} 的历史赔率...")
        
        # 直接调用历史赔率解析方法
        target_companies_history = scraper._parse_target_companies_history(js_content, test_match_id)
        
        if target_companies_history:
            print(f"✅ 成功解析 {len(target_companies_history)} 家目标公司的历史赔率")
            
            # 显示解析结果
            for target_name, data in target_companies_history.items():
                print(f"   {target_name}: {data['history_count']} 条历史记录")
            
            # 保存到数据库
            print("\n正在保存历史赔率到数据库...")
            scraper._save_history_odds_to_db(test_match_id, target_companies_history)
            
        else:
            print("❌ 未解析到任何历史赔率数据")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. 检查保存结果
    print("\n4️⃣ 检查保存结果:")
    try:
        conn = sqlite3.connect("data/matches_and_odds.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM history_odds;")
        final_count = cursor.fetchone()[0]
        print(f"📊 最终记录数: {final_count}")
        
        if final_count > initial_count:
            new_records = final_count - initial_count
            print(f"✅ 新增 {new_records} 条历史记录")
            
            # 显示新增的记录统计
            cursor.execute("""
                SELECT target_name, COUNT(*) as record_count
                FROM history_odds 
                WHERE match_id = ?
                GROUP BY target_name 
                ORDER BY record_count DESC
            """, (test_match_id,))
            
            stats = cursor.fetchall()
            print("\n各公司历史记录数:")
            for target_name, record_count in stats:
                print(f"  {target_name}: {record_count} 条记录")
        else:
            print("⚠️ 没有新增记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    test_local_integration() 