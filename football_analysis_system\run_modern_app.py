#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
足球分析系统 - 现代版
启动现代UI界面的主程序
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox
import logging

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

try:
    # 导入现代应用程序
    from football_analysis_system.ui.modern_app import ModernFootballAnalysisApp, run_modern_app

    def main():
        """程序入口点"""
        try:
            # 运行现代应用程序
            run_modern_app()
        except Exception as e:
            # 显示错误消息
            tk.messagebox.showerror(
                "启动错误",
                f"启动现代版应用程序时发生错误:\n\n{str(e)}\n\n"
                "请检查日志文件获取更多信息。"
            )

            # 记录错误
            logging.error(f"启动时发生错误: {str(e)}", exc_info=True)

            # 尝试启动旧版本应用
            try:
                from football_analysis_system.ui.app import FootballAnalysisApp

                root = tk.Tk()
                app = FootballAnalysisApp(root)
                root.mainloop()
            except Exception as e2:
                tk.messagebox.showerror(
                    "严重错误",
                    f"启动旧版本应用程序也失败:\n\n{str(e2)}\n\n"
                    "请检查安装并确保所有依赖项都已正确安装。"
                )
                logging.error(f"启动旧版本时也发生错误: {str(e2)}", exc_info=True)

    if __name__ == "__main__":
        # 配置基本日志
        logging.basicConfig(
            filename="football_analysis.log",
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )

        # 启动应用
        main()

except ImportError as e:
    # 如果找不到现代应用程序，尝试启动旧版本
    print(f"无法导入现代应用程序: {str(e)}")
    print("正在尝试启动旧版本...")

    try:
        from football_analysis_system.ui.app import FootballAnalysisApp

        def main_fallback():
            """后备启动函数"""
            root = tk.Tk()
            app = FootballAnalysisApp(root)
            root.mainloop()

        if __name__ == "__main__":
            main_fallback()

    except ImportError as e2:
        print(f"启动旧版本也失败: {str(e2)}")

        if __name__ == "__main__":
            tk.messagebox.showerror(
                "严重错误",
                "无法启动应用程序，请确保所有必要的文件和依赖项都已正确安装。"
            )