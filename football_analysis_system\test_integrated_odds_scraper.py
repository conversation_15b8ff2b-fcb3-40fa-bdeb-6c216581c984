#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试集成历史赔率功能的赔率爬虫
"""

import os
import sys
import sqlite3
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from football_analysis_system.scrapers.odds_scraper import OddsScraper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 测试配置
URL_TEMPLATE = "https://1x2d.titan007.com/{match_id}.js?r={timestamp}"
HEADERS_TEMPLATE = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
    'Referer': 'https://op1.titan007.com/oddslist/{match_id}.htm',
}

TARGET_COMPANIES = [
    '澳门', '威廉希尔', '易胜博', '香港马会', 'BWIN', '伟德', 
    'Nordicbet', '利记', 'BetISn', 'iddaa'
]

def check_history_odds_table(db_path):
    """检查历史赔率表"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='history_odds';")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ history_odds 表存在")
            
            # 获取记录数
            cursor.execute("SELECT COUNT(*) FROM history_odds;")
            count = cursor.fetchone()[0]
            print(f"📊 总记录数: {count}")
            
            if count > 0:
                # 按比赛分组显示
                cursor.execute("""
                    SELECT match_id, target_name, COUNT(*) as record_count
                    FROM history_odds 
                    GROUP BY match_id, target_name 
                    ORDER BY match_id, target_name
                """)
                match_stats = cursor.fetchall()
                
                print(f"\n=== 按比赛和公司分组统计 ===")
                for match_id, target_name, record_count in match_stats:
                    print(f"比赛 {match_id} - {target_name}: {record_count} 条历史记录")
                
                # 显示最新几条记录
                cursor.execute("""
                    SELECT match_id, target_name, company_name, home_odds, draw_odds, away_odds, 
                           update_time, scrape_time
                    FROM history_odds 
                    ORDER BY scrape_time DESC 
                    LIMIT 10
                """)
                
                recent_records = cursor.fetchall()
                print(f"\n=== 最新10条历史记录 ===")
                for record in recent_records:
                    match_id, target_name, company_name, home_odds, draw_odds, away_odds, update_time, scrape_time = record
                    print(f"{scrape_time} - 比赛{match_id} {target_name}: {home_odds:.2f}/{draw_odds:.2f}/{away_odds:.2f} ({update_time})")
        else:
            print("❌ history_odds 表不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        if 'conn' in locals():
            conn.close()

def test_integrated_scraper():
    """测试集成的赔率爬虫"""
    
    print("=" * 80)
    print("🧪 测试集成历史赔率功能的赔率爬虫")
    print("=" * 80)
    
    # 测试比赛ID
    test_match_id = "2696009"
    
    print(f"📋 配置的目标公司: {', '.join(TARGET_COMPANIES)}")
    print(f"🔍 测试比赛ID: {test_match_id}")
    
    # 1. 初始化爬虫
    print(f"\n1️⃣ 初始化赔率爬虫...")
    try:
        scraper = OddsScraper(
            url_template=URL_TEMPLATE,
            headers_template=HEADERS_TEMPLATE,
            target_companies=TARGET_COMPANIES,
            db_path="data/matches_and_odds.db"
        )
        print("✅ 爬虫初始化成功")
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {e}")
        return
    
    # 2. 检查数据库表
    print(f"\n2️⃣ 检查数据库表...")
    check_history_odds_table("data/matches_and_odds.db")
    
    # 3. 运行爬虫测试
    print(f"\n3️⃣ 运行爬虫测试...")
    try:
        # 启用详细日志
        logging.getLogger().setLevel(logging.DEBUG)
        
        # 调用集成的方法，会自动保存历史赔率
        odds_data = scraper.get_company_odds(
            match_id=test_match_id,
            save_record_ids=True,
            save_history_odds=True
        )
        
        if odds_data:
            print(f"✅ 成功获取赔率数据: {len(odds_data)} 家公司")
            for company, data in odds_data.items():
                print(f"   {company}: {data['Instant_W']}/{data['Instant_D']}/{data['Instant_L']}")
        else:
            print("❌ 未获取到赔率数据")
            
    except Exception as e:
        print(f"❌ 爬虫运行失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. 再次检查数据库
    print(f"\n4️⃣ 检查保存结果...")
    check_history_odds_table("data/matches_and_odds.db")
    
    print(f"\n🎉 测试完成！")

if __name__ == "__main__":
    test_integrated_scraper() 