#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强后的OddsScraper类
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_odds_scraper():
    """测试增强版赔率爬虫"""
    
    # 设置日志级别
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 导入配置和爬虫类
        from config import ODDS_URL_TEMPLATE, ODDS_HEADERS_TEMPLATE, TARGET_COMPANIES
        from scrapers.odds_scraper import OddsScraper
        
        print("=" * 60)
        print("🎯 测试增强版OddsScraper - 记录ID功能")
        print("=" * 60)
        
        # 创建爬虫实例（现在会自动创建记录ID表）
        scraper = OddsScraper(
            url_template=ODDS_URL_TEMPLATE,
            headers_template=ODDS_HEADERS_TEMPLATE,
            target_companies=TARGET_COMPANIES
        )
        
        # 测试比赛ID
        test_match_id = "2696009"
        
        print(f"测试比赛ID: {test_match_id}")
        print(f"目标公司: {TARGET_COMPANIES}")
        
        # 获取赔率数据（自动保存记录ID）
        print(f"\n=== 获取赔率数据 ===")
        odds_data = scraper.get_company_odds(test_match_id, save_record_ids=True)
        
        if odds_data:
            print(f"✅ 成功获取 {len(odds_data)} 家公司的赔率数据")
            
            # 显示赔率数据示例
            print(f"\n=== 赔率数据示例 ===")
            for company, data in list(odds_data.items())[:3]:
                print(f"{company}:")
                print(f"  初始赔率: {data['Initial_W']}/{data['Initial_D']}/{data['Initial_L']}")
                print(f"  即时赔率: {data['Instant_W']}/{data['Instant_D']}/{data['Instant_L']}")
                print(f"  返还率: {data['Payout_Rate']}%")
            
            # 验证记录ID是否已保存
            print(f"\n=== 验证记录ID保存 ===")
            from check_record_id_table import check_record_id_table
            check_record_id_table()
            
        else:
            print("❌ 未获取到赔率数据")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_odds_scraper() 