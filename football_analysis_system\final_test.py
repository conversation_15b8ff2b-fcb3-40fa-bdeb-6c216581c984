#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试 - 记录ID功能
"""

import time
import requests
import re
import sqlite3
from datetime import datetime

# 直接定义配置，避免循环导入
ODDS_URL_TEMPLATE = "https://1x2d.titan007.com/{match_id}.js?r={timestamp}"
ODDS_HEADERS_TEMPLATE = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
    'Referer': 'https://op1.titan007.com/oddslist/{match_id}.htm',
}
TARGET_COMPANIES = ['威廉希尔', '易胜博', '香港马会', '澳门', 'BWIN']

class SimpleOddsScraper:
    """简化版赔率爬虫，包含记录ID功能"""
    
    def __init__(self, db_path="data/matches_and_odds.db"):
        self.db_path = db_path
        self._create_record_id_table()
    
    def _create_record_id_table(self):
        """创建记录ID表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS odds_record_ids (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    match_id TEXT NOT NULL,
                    company_id TEXT NOT NULL,
                    company_name TEXT NOT NULL,
                    record_id TEXT NOT NULL,
                    update_timestamp TEXT NOT NULL,
                    scrape_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(match_id, company_id, record_id)
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ 记录ID表初始化成功")
            
        except Exception as e:
            print(f"❌ 创建记录ID表失败: {e}")
            if 'conn' in locals():
                conn.close()
    
    def get_timestamp_param(self):
        """生成时间戳参数"""
        timestamp_part = str(int(time.time() * 1000))
        if len(timestamp_part) > 13:
            timestamp_part = timestamp_part[-13:]
        else:
            timestamp_part = timestamp_part.ljust(13, '0')
        return "007" + timestamp_part
    
    def fetch_odds_js(self, match_id):
        """获取赔率JS文件"""
        if not match_id:
            print("❌ 需要比赛ID")
            return None
        
        timestamp = self.get_timestamp_param()
        url = ODDS_URL_TEMPLATE.format(match_id=match_id, timestamp=timestamp)
        
        headers = ODDS_HEADERS_TEMPLATE.copy()
        headers['Referer'] = headers['Referer'].format(match_id=match_id)
        
        try:
            response = requests.get(url, headers=headers, timeout=15)
            if response.status_code == 200:
                print(f"✅ 成功获取比赛 {match_id} 的JS数据")
                return response.text
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 请求出错: {e}")
            return None
    
    def parse_odds_with_record_ids(self, odds_js_content, match_id):
        """解析赔率和记录ID"""
        if not odds_js_content:
            return {}, {}
        
        # 查找game数组
        game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', odds_js_content, re.DOTALL)
        if not game_match:
            print("❌ 未找到game数组")
            return {}, {}
        
        game_data = game_match.group(1)
        company_records = re.findall(r'"([^"]*)"', game_data)
        
        odds_data = {}
        record_ids_data = {}
        
        for record in company_records:
            fields = record.split('|')
            if len(fields) >= 21:
                company_id = fields[0]
                record_id = fields[1]
                company_name = fields[2].strip()
                
                # 赔率数据
                initial_w = fields[3].strip()
                initial_d = fields[4].strip()
                initial_l = fields[5].strip()
                instant_w = fields[10].strip()
                instant_d = fields[11].strip()
                instant_l = fields[12].strip()
                initial_payout_rate = fields[9].strip() if len(fields) > 9 else ""
                payout_rate = fields[16].strip() if len(fields) > 16 else ""
                update_timestamp = fields[20].strip() if len(fields) > 20 else ""
                
                # 匹配目标公司
                matched_name = None
                if company_id == '115' or 'william hill' in company_name.lower():
                    matched_name = '威廉希尔'
                elif company_id == '90' or 'easybets' in company_name.lower():
                    matched_name = '易胜博'
                elif company_id == '432' or 'hk jockey club' in company_name.lower():
                    matched_name = '香港马会'
                elif company_id == '80' or 'macauslot' in company_name.lower():
                    matched_name = '澳门'
                elif company_id == '255' or 'bwin' in company_name.lower():
                    matched_name = 'BWIN'
                
                if matched_name:
                    odds_data[matched_name] = {
                        'Initial_W': initial_w,
                        'Initial_D': initial_d,
                        'Initial_L': initial_l,
                        'Instant_W': instant_w,
                        'Instant_D': instant_d,
                        'Instant_L': instant_l,
                        'Payout_Rate': payout_rate,
                        'Initial_Payout_Rate': initial_payout_rate,
                    }
                    
                    record_ids_data[matched_name] = {
                        'company_id': company_id,
                        'company_name': company_name,
                        'record_id': record_id,
                        'update_timestamp': update_timestamp
                    }
        
        print(f"✅ 解析完成，找到 {len(odds_data)} 家目标公司")
        return odds_data, record_ids_data
    
    def save_record_ids(self, match_id, record_ids_data):
        """保存记录ID到数据库"""
        if not record_ids_data:
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            saved_count = 0
            for company_name, data in record_ids_data.items():
                cursor.execute('''
                    INSERT OR REPLACE INTO odds_record_ids 
                    (match_id, company_id, company_name, record_id, update_timestamp, scrape_time)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    str(match_id),
                    data['company_id'],
                    data['company_name'],
                    data['record_id'],
                    data['update_timestamp'],
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ))
                saved_count += 1
                print(f"保存: {company_name} -> 记录ID: {data['record_id']}")
            
            conn.commit()
            conn.close()
            print(f"✅ 成功保存 {saved_count} 条记录ID")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            if 'conn' in locals():
                conn.close()
    
    def get_company_odds_with_record_ids(self, match_id):
        """获取赔率数据并保存记录ID"""
        print(f"=== 获取比赛 {match_id} 的数据 ===")
        
        # 1. 获取JS内容
        odds_js_content = self.fetch_odds_js(match_id)
        if not odds_js_content:
            return None
        
        # 2. 解析数据
        odds_data, record_ids_data = self.parse_odds_with_record_ids(odds_js_content, match_id)
        
        # 3. 保存记录ID
        if record_ids_data:
            self.save_record_ids(match_id, record_ids_data)
        
        return odds_data

def query_record_ids(match_id, db_path="data/matches_and_odds.db"):
    """查询记录ID"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT company_id, company_name, record_id, update_timestamp, scrape_time
            FROM odds_record_ids 
            WHERE match_id = ?
            ORDER BY company_name
        ''', (str(match_id),))
        
        results = cursor.fetchall()
        conn.close()
        
        print(f"\n=== 数据库中比赛 {match_id} 的记录ID ===")
        for row in results:
            company_id, company_name, record_id, update_timestamp, scrape_time = row
            print(f"{company_name} (ID: {company_id}): 记录ID={record_id}")
            print(f"  更新时间: {update_timestamp}")
            print(f"  爬取时间: {scrape_time}")
        
        return results
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return []

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 最终测试 - 增强版赔率爬虫记录ID功能")
    print("=" * 60)
    
    # 创建爬虫
    scraper = SimpleOddsScraper()
    
    # 测试比赛ID
    test_match_id = "2696009"
    
    # 获取数据
    odds_data = scraper.get_company_odds_with_record_ids(test_match_id)
    
    if odds_data:
        print(f"\n=== 赔率数据示例 ===")
        for company, data in odds_data.items():
            print(f"{company}:")
            print(f"  初始: {data['Initial_W']}/{data['Initial_D']}/{data['Initial_L']}")
            print(f"  即时: {data['Instant_W']}/{data['Instant_D']}/{data['Instant_L']}")
        
        # 验证数据库
        query_record_ids(test_match_id)
        
        print(f"\n✅ 测试完成！记录ID功能正常工作")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main() 