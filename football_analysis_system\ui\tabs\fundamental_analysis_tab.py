import tkinter as tk
import logging
import threading
import traceback
import tkinter.messagebox as messagebox

from .fundamental_analysis_ui import FundamentalAnalysisUI
from .fundamental_analysis_fetcher import MatchDataFetcher
from .fundamental_analysis_parser import MatchDataParser
from .fundamental_analysis_analyzer import MatchAnalyzer
from .fundamental_analysis_formatter import ResultFormatter

class FundamentalAnalysisTab(tk.Frame):
    """基本面分析标签页"""

    def __init__(self, parent):
        """
        初始化基本面分析标签页

        Args:
            parent: 父容器
        """
        super().__init__(parent)
        
        # 配置网格布局使标签页占据整个空间
        self.pack(fill=tk.BOTH, expand=True)
        
        # 比赛信息
        self.match_data = None
        self.league_name = ""
        self.home_team = ""
        self.away_team = ""
        self.match_id = None
        
        # API 调用状态
        self.api_in_progress = False
        self.api_thread = None
        self.analysis_thread = None  # 新增跟踪内部分析线程
        
        # 初始化各个模块
        self.ui = FundamentalAnalysisUI(self)
        self.fetcher = MatchDataFetcher()
        self.parser = MatchDataParser()
        self.analyzer = MatchAnalyzer()
        self.formatter = ResultFormatter()
        
        # 创建界面
        self.ui.create_widgets()
        
        # 设置分析按钮点击事件
        self.ui.analyze_button.config(command=self.start_analysis)
        
        # 添加停止按钮
        self.ui.add_stop_button(command=self.stop_analysis)
        
        # 设置格式化器的UI组件
        self.formatter.ui = self.ui
        
    def update_match_info(self, match_data, is_tab_switch=False):
        """
        更新比赛信息

        Args:
            match_data: 比赛数据
            is_tab_switch: 是否是标签页切换触发的更新，如果是则不恢复界面
        """
        # 只有在真正切换比赛而不是切换标签页时，才恢复所有控制面板
        if not is_tab_switch:
            # 首先确保所有控制面板都是可见的
            self.ui.show_control_panels()
        
        # 检查是否为同一场比赛
        is_same_match = (self.match_data is not None and
                         self.home_team == match_data.get('home_team', '') and
                         self.away_team == match_data.get('away_team', ''))
                         
        # 保存新的比赛数据
        self.match_data = match_data
        
        # 提取比赛信息
        self.league_name = match_data.get('league_name', '')
        self.home_team = match_data.get('home_team', '')
        self.away_team = match_data.get('away_team', '')
        
        # 提取match_id, 尝试多种可能的字段名
        self.match_id = match_data.get('match_id') or match_data.get('id') or match_data.get('schedule_id')
        
        # 更新UI
        self.ui.update_match_info(self.league_name, self.home_team, self.away_team)
        
        # 更新各个模块的队伍信息
        self.parser.home_team = self.home_team
        self.parser.away_team = self.away_team
        self.parser.league_name = self.league_name
        
        self.analyzer.set_teams(self.home_team, self.away_team)
        self.formatter.set_teams(self.home_team, self.away_team, self.league_name)
        
        # 只有在比赛变更且不是标签页切换时才清空分析结果
        if not is_same_match and not is_tab_switch:
            # 清空之前的分析结果
            self.clear_result()
            # 重置状态标签
            self.ui.show_status("点击\"开始基本面分析\"按钮获取实时分析")
            
            # 如果正在进行分析，中止它
            if self.api_in_progress:
                self.stop_analysis()
        
        # 启用分析按钮
        self.ui.enable_analyze_button()
        
    def start_analysis(self):
        """开始基本面分析"""
        if self.api_in_progress:
            logging.warning("已有分析任务正在进行中")
            return
            
        if not self.match_data or not self.home_team or not self.away_team or not self.match_id:
            self.ui.show_status("请先选择一场比赛")
            return
            
        # 更新UI状态
        self.api_in_progress = True
        self.ui.disable_analyze_button()
        self.ui.enable_stop_button()  # 启用停止按钮
        self.ui.show_status("正在获取实时分析数据...")
        self.ui.start_progress()
        
        # 清空当前结果
        self.clear_result()
        
        # 立即隐藏控制面板，这样用户就可以专注于结果
        self.ui.hide_control_panels()
        
        # 创建线程执行爬虫
        self.api_thread = threading.Thread(target=self.fetch_and_analyze)
        self.api_thread.daemon = True
        self.api_thread.start()
        
    def stop_analysis(self):
        """停止正在进行的分析"""
        if not self.api_in_progress:
            return
            
        logging.info("用户手动停止分析")
        self.ui.show_status("分析已停止")
        self.api_in_progress = False
        self.ui.stop_progress()
        self.ui.enable_analyze_button()
        self.ui.disable_stop_button()
        
    def fetch_and_analyze(self):
        """获取并分析比赛数据
        
        Args:
            url: 比赛URL
            league_name: 联赛名称，可选
        """
        if not self.match_id or self.match_id == "请输入球探网比赛URL":
            messagebox.showwarning("提示", "请输入有效的比赛URL")
            self.after(0, lambda: self.finish_analysis(False))  # 确保调用finish_analysis
            return
            
        # 清空结果区域
        self.clear_result()
        
        # 更新状态
        self.ui.show_status("正在分析，请稍候...")
        
        # 开始分析
        def analyze_task():
            try:
                # 获取HTML内容
                try:
                    html_content = self.fetcher.fetch_match_analysis(
                        self.match_id, 
                        callback_progress=lambda text: self.after(0, lambda: self.ui.show_status(text))
                    )
                except Exception as e:
                    logging.error(f"获取HTML内容失败: {e}")
                    self.after(0, lambda: self.ui.show_status(f"获取数据失败: {e}", is_error=True))
                    self.after(0, lambda: self.finish_analysis(False))  # 确保调用finish_analysis
                    return
                
                # 检查分析是否被用户停止
                if not self.api_in_progress:
                    return
                
                # 解析比赛数据
                try:
                    analysis_data = self.parser.parse_match_data(html_content)
                except Exception as e:
                    logging.error(f"解析数据失败: {e}")
                    analysis_data = {'parse_error': str(e)}
                
                # 检查分析是否被用户停止
                if not self.api_in_progress:
                    return
                    
                # 检查是否有解析错误
                if 'parse_error' in analysis_data:
                    # 捕获当前的analysis_data值
                    current_data = analysis_data
                    self.after(0, lambda data=current_data: self.formatter.format_and_display_result(data))
                    self.after(0, lambda: self.ui.show_status("分析完成，但存在解析错误", is_error=True))
                    self.after(0, lambda: self.finish_analysis(False))  # 确保调用finish_analysis
                    return
                
                # 计算评分
                ratings = self.analyzer.calculate_ratings(analysis_data)
                
                # 检查分析是否被用户停止
                if not self.api_in_progress:
                    return
                    
                # 格式化并显示结果 - 捕获当前的变量值
                current_data = analysis_data
                current_ratings = ratings
                
                # 在主线程中依次执行格式化和状态更新
                def update_ui():
                    try:
                        logging.info("开始在主线程中更新UI...")
                        
                        # 先格式化并显示结果
                        self.formatter.format_and_display_result(current_data, current_ratings)
                        
                        # 验证结果是否正确显示
                        if hasattr(self.ui, 'result_text') and self.ui.result_text.winfo_exists():
                            content = self.ui.result_text.get("1.0", "end-1c")
                            if content.strip():
                                logging.info(f"结果已成功显示，内容长度: {len(content)}")
                                self.ui.show_status("分析完成，已显示最新结果")
                            else:
                                logging.warning("结果文本框存在但内容为空")
                                self.ui.show_status("分析完成，但结果显示可能有问题")
                        else:
                            logging.error("结果文本框未正确创建")
                            self.ui.show_status("分析完成，但结果显示失败")
                            
                        # 完成分析
                        self.finish_analysis(True)
                        
                    except Exception as e:
                        logging.error(f"UI更新过程出错: {e}")
                        logging.error(traceback.format_exc())
                        self.ui.show_status(f"结果显示出错: {e}", is_error=True)
                        self.finish_analysis(False)
                
                self.after(0, update_ui)
                
            except Exception as e:
                logging.error(f"分析过程出错: {e}")
                logging.error(traceback.format_exc())
                self.after(0, lambda: self.ui.show_status(f"分析出错: {e}", is_error=True))
                self.after(0, lambda: self.finish_analysis(False))  # 确保调用finish_analysis
        
        # 使用线程执行分析任务
        self.analysis_thread = threading.Thread(target=analyze_task)
        self.analysis_thread.daemon = True
        self.analysis_thread.start()
        
    def finish_analysis(self, success, restore_panels=False):
        """
        完成分析，更新UI状态

        Args:
            success: 分析是否成功
            restore_panels: 是否恢复显示控制面板
        """
        self.api_in_progress = False
        self.ui.stop_progress()
        self.ui.enable_analyze_button()
        self.ui.disable_stop_button()  # 禁用停止按钮
        
        if success:
            self.ui.show_status("分析完成，已显示最新结果")
        else:
            self.ui.show_status("分析请求失败，请重试")
            # 如果分析失败，恢复控制面板
            self.ui.show_control_panels()
            
        # 如果需要恢复控制面板，则调用show_control_panels方法
        if restore_panels:
            self.ui.show_control_panels()
        
    def append_result(self, text):
        """
        向结果文本框追加内容

        Args:
            text: 要追加的文本
        """
        self.ui.append_result(text)
        
    def append_colored_result(self, text, color=None, tag=None):
        """
        向结果文本框追加带颜色的内容

        Args:
            text: 要追加的文本
            color: 文本颜色
            tag: 标签名称（如果为None则自动生成）
        """
        self.ui.append_colored_result(text, color, tag)
        
    def clear_result(self):
        """清空分析结果"""
        self.ui.clear_result()
        # 恢复控制面板
        self.ui.show_control_panels()
        
    def clear(self):
        """清空所有数据"""
        self.match_data = None
        self.league_name = ""
        self.home_team = ""
        self.away_team = ""
        self.match_id = None
        
        # 更新UI
        self.ui.update_match_info("", "", "")
        self.ui.show_status("")
        
        # 清空分析结果
        self.clear_result()
        
        # 如果正在进行分析，停止进度条
        if self.api_in_progress:
            self.ui.stop_progress()
        
        # 重置状态
        self.api_in_progress = False
        self.ui.disable_analyze_button()

    def analyze(self, match_data, update_callback, should_stop_callback=None):
        """
        执行基本面分析，供其他标签页调用
        
        Args:
            match_data: 比赛数据
            update_callback: 更新结果的回调函数，接收两个参数(text, color)
            should_stop_callback: 检查是否应该停止的回调函数，不传则不检查
            
        Returns:
            bool: 分析是否成功
        """
        if not match_data or not 'home_team' in match_data or not 'away_team' in match_data:
            update_callback("无效的比赛数据", "#FF0000")
            return False
            
        # 保存比赛信息
        home_team = match_data.get('home_team', '')
        away_team = match_data.get('away_team', '')
        league_name = match_data.get('league_name', '')
        match_id = match_data.get('match_id') or match_data.get('id') or match_data.get('schedule_id')
        
        if not match_id:
            update_callback("缺少比赛ID，无法获取基本面数据", "#FF0000")
            return False
            
        # 更新分析器的队伍信息
        self.parser.home_team = home_team
        self.parser.away_team = away_team
        self.parser.league_name = league_name
        self.analyzer.set_teams(home_team, away_team)
        self.formatter.set_teams(home_team, away_team, league_name)
        
        # 显示分析开始信息
        update_callback(f"开始分析 {league_name} 比赛: {home_team} vs {away_team}\n\n")
        
        try:
            # 获取HTML内容
            try:
                update_callback("正在获取比赛基本面数据...\n")
                html_content = self.fetcher.fetch_match_analysis(match_id)
                
                # 检查是否需要停止
                if should_stop_callback and should_stop_callback():
                    update_callback("\n分析已停止\n")
                    return False
                    
            except Exception as e:
                logging.error(f"获取HTML内容失败: {e}")
                update_callback(f"\n获取数据失败: {str(e)}\n", "#FF0000")
                return False
            
            # 解析比赛数据
            try:
                update_callback("正在解析比赛数据...\n")
                analysis_data = self.parser.parse_match_data(html_content)
                
                # 检查是否需要停止
                if should_stop_callback and should_stop_callback():
                    update_callback("\n分析已停止\n")
                    return False
                    
            except Exception as e:
                logging.error(f"解析数据失败: {e}")
                update_callback(f"\n解析数据失败: {str(e)}\n", "#FF0000")
                return False
            
            # 检查是否有解析错误
            if 'parse_error' in analysis_data:
                update_callback(f"\n解析数据出错: {analysis_data['parse_error']}\n", "#FF0000")
                return False
            
            # 计算评分
            update_callback("正在计算评分...\n")
            ratings = self.analyzer.calculate_ratings(analysis_data)
            
            # 检查是否需要停止
            if should_stop_callback and should_stop_callback():
                update_callback("\n分析已停止\n")
                return False
            
            # 格式化结果
            update_callback("正在生成分析报告...\n")
            
            # 使用自定义输出回调替代UI直接输出
            class CallbackWrapper:
                def __init__(self, callback):
                    self.callback = callback
                    
                def append_result(self, text):
                    self.callback(text, None)
                    
                def append_colored_result(self, text, color=None, tag=None):
                    self.callback(text, color)
            
            wrapper = CallbackWrapper(update_callback)
            
            # 存储原始UI引用
            original_ui = self.formatter.ui
            
            try:
                # 使用包装器替代UI
                self.formatter.ui = wrapper
                
                # 格式化并输出结果
                self.formatter.format_and_display_result(analysis_data, ratings)
                
                # 添加完成信息
                update_callback("\n基本面分析完成\n")
                return True
                
            finally:
                # 恢复原始UI引用
                self.formatter.ui = original_ui
                
        except Exception as e:
            logging.error(f"分析过程出错: {e}")
            logging.error(traceback.format_exc())
            update_callback(f"\n分析过程出错: {str(e)}\n", "#FF0000")
            return False