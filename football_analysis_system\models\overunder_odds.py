class OverUnderOdds:
    """大小球赔率数据模型类"""
    
    def __init__(self, bookmaker=None, initial_over=None, initial_line=None, initial_under=None,
                 instant_over=None, instant_line=None, instant_under=None, 
                 line_numeric=None, company_id=None, update_time=None):
        """
        初始化大小球赔率对象
        
        Args:
            bookmaker: 博彩公司名称
            initial_over: 初始大球赔率
            initial_line: 初始盘口（如2.5, 2.5/3等）
            initial_under: 初始小球赔率
            instant_over: 即时大球赔率
            instant_line: 即时盘口
            instant_under: 即时小球赔率
            line_numeric: 盘口数值形式（如2.5, 2.75等）
            company_id: 博彩公司ID
            update_time: 更新时间
        """
        self.bookmaker = bookmaker
        self.initial_over = initial_over
        self.initial_line = initial_line
        self.initial_under = initial_under
        self.instant_over = instant_over
        self.instant_line = instant_line
        self.instant_under = instant_under
        self.line_numeric = line_numeric
        self.company_id = company_id
        self.update_time = update_time
    
    def to_dict(self):
        """
        将大小球赔率对象转换为字典
        """
        return {
            'bookmaker': self.bookmaker,
            'initial_over': self.initial_over,
            'initial_line': self.initial_line,
            'initial_under': self.initial_under,
            'instant_over': self.instant_over,
            'instant_line': self.instant_line,
            'instant_under': self.instant_under,
            'line_numeric': self.line_numeric,
            'company_id': self.company_id,
            'update_time': self.update_time
        }
    
    @classmethod
    def from_dict(cls, data):
        """
        从字典创建大小球赔率对象
        
        Args:
            data: 包含大小球赔率数据的字典
            
        Returns:
            OverUnderOdds: 大小球赔率对象
        """
        return cls(
            bookmaker=data.get('bookmaker'),
            initial_over=data.get('initial_over'),
            initial_line=data.get('initial_line'),
            initial_under=data.get('initial_under'),
            instant_over=data.get('instant_over'),
            instant_line=data.get('instant_line'),
            instant_under=data.get('instant_under'),
            line_numeric=data.get('line_numeric'),
            company_id=data.get('company_id'),
            update_time=data.get('update_time')
        )
    
    @staticmethod
    def convert_line_to_numeric(line_str):
        """
        将盘口字符串转换为数值
        
        Args:
            line_str: 盘口字符串（如"2.5", "2.5/3", "2/2.5"）
            
        Returns:
            float: 盘口数值
        """
        if not line_str:
            return None
            
        line_str = str(line_str).strip()
        
        # 处理分数形式的盘口（如2.5/3, 2/2.5）
        if '/' in line_str:
            parts = line_str.split('/')
            try:
                return (float(parts[0]) + float(parts[1])) / 2
            except (ValueError, IndexError):
                return None
        
        # 处理简单数值
        try:
            return float(line_str)
        except ValueError:
            return None
    
    def __str__(self):
        """字符串表示"""
        return f"{self.bookmaker}: 大球 {self.instant_over} 盘口 {self.instant_line} 小球 {self.instant_under}" 