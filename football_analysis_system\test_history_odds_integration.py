#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试历史赔率集成功能
"""

import sqlite3
import logging
from scrapers.odds_scraper import OddsScraper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 测试配置
URL_TEMPLATE = "https://1x2d.titan007.com/{match_id}.js?r={timestamp}"
HEADERS_TEMPLATE = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
    'Referer': 'https://op1.titan007.com/oddslist/{match_id}.htm',
}

TARGET_COMPANIES = ['澳门', '威廉希尔', '易胜博', '香港马会', 'BWIN', '伟德']

def check_history_odds_table():
    """检查历史赔率表"""
    try:
        conn = sqlite3.connect("data/matches_and_odds.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM history_odds;")
        count = cursor.fetchone()[0]
        print(f"📊 history_odds表记录数: {count}")
        
        if count > 0:
            cursor.execute("""
                SELECT match_id, target_name, COUNT(*) as record_count
                FROM history_odds 
                GROUP BY match_id, target_name 
                ORDER BY match_id, target_name
                LIMIT 10
            """)
            stats = cursor.fetchall()
            
            print("最新统计:")
            for match_id, target_name, record_count in stats:
                print(f"  比赛 {match_id} - {target_name}: {record_count} 条记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    print("🧪 测试历史赔率集成功能")
    print("=" * 50)
    
    # 1. 检查初始状态
    print("1️⃣ 检查初始数据库状态:")
    check_history_odds_table()
    
    # 2. 初始化爬虫
    print("\n2️⃣ 初始化爬虫:")
    try:
        scraper = OddsScraper(
            url_template=URL_TEMPLATE,
            headers_template=HEADERS_TEMPLATE,
            target_companies=TARGET_COMPANIES,
            db_path="data/matches_and_odds.db"
        )
        print("✅ 爬虫初始化成功")
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {e}")
        return
    
    # 3. 测试历史赔率功能
    print("\n3️⃣ 测试历史赔率功能:")
    test_match_id = "2696009"
    
    try:
        print(f"正在处理比赛 {test_match_id}...")
        
        # 调用集成的方法
        odds_data = scraper.get_company_odds(
            match_id=test_match_id,
            save_record_ids=True,
            save_history_odds=True
        )
        
        if odds_data:
            print(f"✅ 成功获取 {len(odds_data)} 家公司的赔率数据")
        else:
            print("⚠️ 未获取到当前赔率数据，但历史赔率可能已保存")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. 检查结果
    print("\n4️⃣ 检查保存结果:")
    check_history_odds_table()
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main() 