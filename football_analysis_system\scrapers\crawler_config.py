"""
爬虫配置管理器
集中管理所有爬虫相关的配置选项
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class NetworkConfig:
    """网络相关配置"""
    timeout: int = 30
    max_retries: int = 3
    backoff_factor: float = 1.5
    requests_per_second: float = 2.0
    burst_size: int = 10
    connection_pool_size: int = 20
    retry_on_status: List[int] = None
    
    def __post_init__(self):
        if self.retry_on_status is None:
            self.retry_on_status = [429, 500, 502, 503, 504]


@dataclass 
class ConcurrencyConfig:
    """并发相关配置"""
    max_workers: int = 8
    batch_size: int = 20
    delay_between_batches: float = 3.0
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: int = 60


@dataclass
class DatabaseConfig:
    """数据库相关配置"""
    max_connections: int = 10
    batch_insert_size: int = 100
    transaction_timeout: int = 30
    vacuum_interval: int = 7  # 天


@dataclass
class LoggingConfig:
    """日志相关配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "logs/crawler.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    console_output: bool = True


@dataclass
class CacheConfig:
    """缓存相关配置"""
    enable_cache: bool = True
    max_cache_size: int = 1000
    cache_ttl: int = 3600  # 秒
    auto_clear_interval: int = 1800  # 秒


@dataclass
class ProxyConfig:
    """代理相关配置"""
    enable_proxy: bool = False
    proxy_list: List[str] = None
    proxy_rotation: bool = True
    proxy_timeout: int = 10
    
    def __post_init__(self):
        if self.proxy_list is None:
            self.proxy_list = []


@dataclass
class UserAgentConfig:
    """User-Agent相关配置"""
    rotation_enabled: bool = True
    custom_agents: List[str] = None
    
    def __post_init__(self):
        if self.custom_agents is None:
            self.custom_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            ]


class CrawlerConfigManager:
    """爬虫配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config/crawler_config.json"
        self.config_dir = os.path.dirname(self.config_file)
        
        # 默认配置
        self.network = NetworkConfig()
        self.concurrency = ConcurrencyConfig()
        self.database = DatabaseConfig()
        self.logging = LoggingConfig()
        self.cache = CacheConfig()
        self.proxy = ProxyConfig()
        self.user_agent = UserAgentConfig()
        
        # 环境特定配置
        self.environment = "production"  # development, testing, production
        
        # 加载配置
        self.load_config()
        
        # 设置日志
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志配置"""
        # 确保日志目录存在
        log_dir = os.path.dirname(self.logging.file_path)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=getattr(logging, self.logging.level.upper()),
            format=self.logging.format,
            handlers=self._get_log_handlers()
        )
    
    def _get_log_handlers(self):
        """获取日志处理器"""
        from logging.handlers import RotatingFileHandler
        
        handlers = []
        
        # 文件处理器
        if self.logging.file_path:
            file_handler = RotatingFileHandler(
                self.logging.file_path,
                maxBytes=self.logging.max_file_size,
                backupCount=self.logging.backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(logging.Formatter(self.logging.format))
            handlers.append(file_handler)
        
        # 控制台处理器
        if self.logging.console_output:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(logging.Formatter(self.logging.format))
            handlers.append(console_handler)
        
        return handlers
    
    def load_config(self):
        """从文件加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新各个配置模块
                if 'network' in config_data:
                    self._update_config(self.network, config_data['network'])
                
                if 'concurrency' in config_data:
                    self._update_config(self.concurrency, config_data['concurrency'])
                
                if 'database' in config_data:
                    self._update_config(self.database, config_data['database'])
                
                if 'logging' in config_data:
                    self._update_config(self.logging, config_data['logging'])
                
                if 'cache' in config_data:
                    self._update_config(self.cache, config_data['cache'])
                
                if 'proxy' in config_data:
                    self._update_config(self.proxy, config_data['proxy'])
                
                if 'user_agent' in config_data:
                    self._update_config(self.user_agent, config_data['user_agent'])
                
                if 'environment' in config_data:
                    self.environment = config_data['environment']
                
                logging.info(f"配置已从 {self.config_file} 加载")
                
            except Exception as e:
                logging.warning(f"加载配置文件失败: {e}，使用默认配置")
        else:
            logging.info("配置文件不存在，使用默认配置")
            # 创建默认配置文件
            self.save_config()
    
    def _update_config(self, config_obj, config_dict):
        """更新配置对象"""
        for key, value in config_dict.items():
            if hasattr(config_obj, key):
                setattr(config_obj, key, value)
    
    def save_config(self):
        """保存配置到文件"""
        try:
            # 确保配置目录存在
            if self.config_dir and not os.path.exists(self.config_dir):
                os.makedirs(self.config_dir, exist_ok=True)
            
            config_data = {
                'network': asdict(self.network),
                'concurrency': asdict(self.concurrency),
                'database': asdict(self.database),
                'logging': asdict(self.logging),
                'cache': asdict(self.cache),
                'proxy': asdict(self.proxy),
                'user_agent': asdict(self.user_agent),
                'environment': self.environment
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"配置已保存到 {self.config_file}")
            
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
    
    def get_environment_config(self) -> Dict[str, Any]:
        """获取环境特定配置"""
        base_config = {
            'network': asdict(self.network),
            'concurrency': asdict(self.concurrency),
            'database': asdict(self.database),
            'cache': asdict(self.cache),
            'proxy': asdict(self.proxy),
            'user_agent': asdict(self.user_agent)
        }
        
        # 根据环境调整配置
        if self.environment == "development":
            # 开发环境：更详细的日志，更小的批次
            base_config['concurrency']['max_workers'] = 3
            base_config['concurrency']['batch_size'] = 5
            base_config['network']['requests_per_second'] = 1.0
            
        elif self.environment == "testing":
            # 测试环境：快速失败，小批次
            base_config['network']['max_retries'] = 2
            base_config['network']['timeout'] = 15
            base_config['concurrency']['max_workers'] = 2
            base_config['concurrency']['batch_size'] = 3
            
        elif self.environment == "production":
            # 生产环境：保持默认的稳定配置
            pass
        
        return base_config
    
    def update_from_dict(self, config_dict: Dict[str, Any]):
        """从字典更新配置"""
        for section, values in config_dict.items():
            if hasattr(self, section):
                config_obj = getattr(self, section)
                self._update_config(config_obj, values)
    
    def get_network_headers(self) -> Dict[str, str]:
        """获取默认的网络请求头"""
        return {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
        }
    
    def get_odds_headers(self) -> Dict[str, str]:
        """获取赔率请求专用头"""
        return {
            'Accept': 'text/javascript, application/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache'
        }
    
    def validate_config(self) -> List[str]:
        """验证配置的有效性"""
        errors = []
        
        # 验证网络配置
        if self.network.timeout <= 0:
            errors.append("网络超时时间必须大于0")
        
        if self.network.max_retries < 0:
            errors.append("最大重试次数不能为负数")
        
        if self.network.requests_per_second <= 0:
            errors.append("每秒请求数必须大于0")
        
        # 验证并发配置
        if self.concurrency.max_workers <= 0:
            errors.append("最大工作线程数必须大于0")
        
        if self.concurrency.batch_size <= 0:
            errors.append("批次大小必须大于0")
        
        # 验证数据库配置
        if self.database.max_connections <= 0:
            errors.append("数据库最大连接数必须大于0")
        
        # 验证缓存配置
        if self.cache.max_cache_size < 0:
            errors.append("缓存大小不能为负数")
        
        return errors
    
    def __str__(self) -> str:
        """配置的字符串表示"""
        return f"""
CrawlerConfig({self.environment}):
  Network: timeout={self.network.timeout}s, retries={self.network.max_retries}, rps={self.network.requests_per_second}
  Concurrency: workers={self.concurrency.max_workers}, batch={self.concurrency.batch_size}
  Database: connections={self.database.max_connections}, batch_size={self.database.batch_insert_size}
  Cache: enabled={self.cache.enable_cache}, size={self.cache.max_cache_size}
  Proxy: enabled={self.proxy.enable_proxy}, count={len(self.proxy.proxy_list)}
        """.strip()


# 全局配置实例
config_manager = CrawlerConfigManager()


def get_config() -> CrawlerConfigManager:
    """获取配置管理器实例"""
    return config_manager


def reload_config():
    """重新加载配置"""
    global config_manager
    config_manager.load_config()
    return config_manager 