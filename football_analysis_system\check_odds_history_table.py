#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查赔率历史表
"""

import sqlite3

def check_odds_history_table():
    """检查赔率历史表"""
    
    db_path = "data/matches_and_odds.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='odds_history';")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ odds_history 表存在")
            
            # 获取表结构
            cursor.execute("PRAGMA table_info(odds_history);")
            columns = cursor.fetchall()
            
            print("\n=== 表结构 ===")
            for col_info in columns:
                cid, name, data_type, notnull, default_value, pk = col_info
                pk_str = " (主键)" if pk else ""
                notnull_str = " NOT NULL" if notnull else ""
                default_str = f" DEFAULT {default_value}" if default_value else ""
                print(f"  {name}: {data_type}{notnull_str}{default_str}{pk_str}")
            
            # 获取记录数
            cursor.execute("SELECT COUNT(*) FROM odds_history;")
            count = cursor.fetchone()[0]
            print(f"\n记录数: {count}")
            
            # 显示前10条数据
            if count > 0:
                cursor.execute("SELECT * FROM odds_history ORDER BY scrape_time DESC LIMIT 10;")
                records = cursor.fetchall()
                
                print("\n=== 最新10条记录 ===")
                for i, record in enumerate(records, 1):
                    print(f"记录 {i}: {record}")
                    
                # 按比赛分组显示
                cursor.execute("""
                    SELECT match_id, COUNT(*) as record_count, 
                           COUNT(DISTINCT company_id) as company_count
                    FROM odds_history 
                    GROUP BY match_id 
                    ORDER BY match_id
                """)
                match_stats = cursor.fetchall()
                
                print(f"\n=== 按比赛分组统计 ===")
                for match_id, record_count, company_count in match_stats:
                    print(f"比赛 {match_id}: {record_count} 条历史记录, {company_count} 家公司")
                
                # 显示赔率变化最大的记录
                cursor.execute("""
                    SELECT company_name, match_id, 
                           MIN(home_odds) as min_home, MAX(home_odds) as max_home,
                           MIN(draw_odds) as min_draw, MAX(draw_odds) as max_draw,
                           MIN(away_odds) as min_away, MAX(away_odds) as max_away,
                           COUNT(*) as history_count
                    FROM odds_history 
                    WHERE match_id = '2696009'
                    GROUP BY company_name, match_id
                    HAVING history_count > 5
                    ORDER BY (max_home - min_home + max_draw - min_draw + max_away - min_away) DESC
                    LIMIT 5
                """)
                
                change_stats = cursor.fetchall()
                print(f"\n=== 赔率变化最大的公司 (比赛2696009) ===")
                for company_name, match_id, min_home, max_home, min_draw, max_draw, min_away, max_away, history_count in change_stats:
                    home_change = max_home - min_home
                    draw_change = max_draw - min_draw
                    away_change = max_away - min_away
                    total_change = home_change + draw_change + away_change
                    print(f"{company_name}: {history_count}条记录, 总变化幅度 {total_change:.3f}")
                    print(f"  主胜: {min_home:.2f} -> {max_home:.2f} (变化{home_change:+.3f})")
                    print(f"  平局: {min_draw:.2f} -> {max_draw:.2f} (变化{draw_change:+.3f})")
                    print(f"  客胜: {min_away:.2f} -> {max_away:.2f} (变化{away_change:+.3f})")
            
        else:
            print("❌ odds_history 表不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_odds_history_table() 