import sqlite3
from datetime import datetime
import logging
from tkinter import messagebox

class DataManager:
    """数据管理器，负责处理所有数据库操作"""
    
    def __init__(self, db_path):
        """
        初始化数据管理器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.ensure_predictions_table()
    
    def ensure_predictions_table(self):
        """确保预测结果表存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建预测结果表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS match_predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT,
                league_name TEXT,
                home_team TEXT,
                away_team TEXT,
                match_time TEXT,
                bookmaker TEXT,
                odds_type TEXT,
                team_type TEXT,
                gap_diff REAL,
                interval_type TEXT,
                rule_value TEXT,
                rule_diff TEXT,
                first_choice TEXT,
                standard_draw TEXT,
                actual_draw TEXT,
                draw_interval TEXT,
                prediction_time TEXT
            )
            ''')
            
            conn.commit()
            conn.close()
            logging.info("预测结果表创建成功")
        except Exception as e:
            logging.error(f"创建预测结果表时出错: {e}")
    
    def save_predictions(self, match_data, interval_tree, draw_interval_tree):
        """
        保存选中的预测结果到数据库
        
        Args:
            match_data: 比赛数据
            interval_tree: 区间分析表格
            draw_interval_tree: 平赔区间分析表格
        """
        if not match_data:
            messagebox.showwarning("提示", "当前没有比赛数据可保存")
            return
            
        # 获取已选中的项目
        selected_interval_items = []
        selected_draw_items = []
        
        # 检查区间分析表格中的选中项
        for item in interval_tree.get_children():
            values = interval_tree.item(item, "values")
            if values and values[0] == "✓":
                selected_interval_items.append(values)
                
        # 检查平赔区间分析表格中的选中项
        if draw_interval_tree is not None:  # 添加对None的检查
            for item in draw_interval_tree.get_children():
                values = draw_interval_tree.item(item, "values")
                if values and values[0] == "✓":
                    selected_draw_items.append(values)
                
        # 如果没有选中任何项目
        if not selected_interval_items and not selected_draw_items:
            messagebox.showwarning("提示", "请至少选择一条记录进行保存")
            return
            
        # 保存到数据库
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取比赛基本信息
            match_id = match_data.get('match_id', '')
            league_name = match_data.get('league_name', '未知联赛')
            home_team = match_data.get('home_team', '未知主队')
            away_team = match_data.get('away_team', '未知客队')
            match_time = match_data.get('match_time', '')
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 保存区间分析选中项
            for values in selected_interval_items:
                # 跳过分隔行或组标题行
                if not values[1] and not values[2]:
                    continue
                    
                # 准备数据
                odds_type = values[1] or ""
                company = values[2] or ""
                team_type = values[3] or ""
                gap_diff = values[4] or ""
                interval_type = values[5] or ""
                rule_value = values[6] or ""
                rule_diff = values[7] or ""
                first_choice = values[8] or ""
                
                # 插入数据库
                cursor.execute('''
                INSERT INTO match_predictions 
                (match_id, league_name, home_team, away_team, match_time, 
                bookmaker, odds_type, team_type, gap_diff, interval_type, 
                rule_value, rule_diff, first_choice, prediction_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (match_id, league_name, home_team, away_team, match_time, 
                    company, odds_type, team_type, gap_diff, interval_type, 
                    rule_value, rule_diff, first_choice, current_time))
            
            # 保存平赔区间分析选中项
            for values in selected_draw_items:
                company = values[1] or ""
                standard_draw = values[2] or ""
                actual_draw = values[3] or ""
                draw_interval = values[4] or ""
                
                # 插入数据库
                cursor.execute('''
                INSERT INTO match_predictions 
                (match_id, league_name, home_team, away_team, match_time, 
                bookmaker, standard_draw, actual_draw, draw_interval, prediction_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (match_id, league_name, home_team, away_team, match_time, 
                    company, standard_draw, actual_draw, draw_interval, current_time))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("成功", f"已成功保存 {len(selected_interval_items) + len(selected_draw_items)} 条预测记录")
            
        except Exception as e:
            logging.error(f"保存预测结果时出错: {e}")
            messagebox.showerror("错误", f"保存失败: {e}")