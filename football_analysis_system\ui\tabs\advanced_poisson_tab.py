import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import logging
import sqlite3
import os
import sys
import threading
import queue
from datetime import datetime
import json

# 添加高级泊松系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from football_analysis_system.config import COLOR_BG, COLOR_PRIMARY, COLOR_TEXT, DATA_DIR
from football_analysis_system.config import FONT_LARGE, FONT_NORMAL, FONT_SUBHEADER, TARGET_COMPANIES, ODDS_URL_TEMPLATE, ODDS_HEADERS_TEMPLATE

from football_analysis_system.scrapers.odds_scraper import OddsScraper
from football_analysis_system.scrapers.overunder_scraper import OverUnderScraper
from football_analysis_system.db.match_db import MatchDatabase

# 导入高级泊松系统
try:
    from advanced_poisson_system import DataManager, MarketAnalyzer, HistoricalAnalyzer, HybridPredictor, AdvancedPoissonEngine
    ADVANCED_POISSON_AVAILABLE = True
except ImportError as e:
    logging.error(f"无法导入高级泊松系统: {e}")
    ADVANCED_POISSON_AVAILABLE = False

# 分析策略映射
STRATEGY_MAPPING = {
    "自动选择": "auto",
    "进攻平衡": "offensive_balance",
    "进攻主导": "offensive_dominance",
    "防守平衡": "defensive_balance",
    "防守主导": "defensive_dominance"
}

class AdvancedPoissonTab(tk.Frame):
    """高级泊松分布分析标签页"""

    def __init__(self, parent):
        """
        初始化高级泊松分布分析标签页

        Args:
            parent: 父容器
        """
        super().__init__(parent, bg=COLOR_BG)

        # 检查高级泊松系统是否可用
        if not ADVANCED_POISSON_AVAILABLE:
            self.create_error_interface()
            return

        # 初始化变量
        self.current_match = None
        self.analysis_result = None
        self.scraping_active = False
        self.available_lines = []  # 存储可用的大小球盘口

        # 消息队列用于线程通信
        self.message_queue = queue.Queue()

        # 初始化数据库管理器
        from config import DB_MATCHES
        self.match_db = MatchDatabase(DB_MATCHES)

        # 初始化高级泊松系统
        self.init_advanced_system()

        # 初始化爬虫
        self.overunder_scraper = OverUnderScraper()

        # 创建界面
        self.create_widgets()

        # 启动消息处理
        self.process_messages()

    def create_error_interface(self):
        """创建错误界面"""
        error_label = tk.Label(self,
                              text="❌ 高级泊松系统不可用\n请检查advanced_poisson_system模块",
                              font=FONT_LARGE,
                              fg="red",
                              bg=COLOR_BG)
        error_label.pack(expand=True)

    def init_advanced_system(self):
        """初始化高级泊松系统"""
        try:
            self.data_manager = DataManager()
            self.market_analyzer = MarketAnalyzer()
            self.historical_analyzer = HistoricalAnalyzer(self.data_manager)
            self.hybrid_predictor = HybridPredictor(self.historical_analyzer, self.market_analyzer)
            self.poisson_engine = AdvancedPoissonEngine(self.hybrid_predictor)
            logging.info("✅ 高级泊松系统初始化成功")
        except Exception as e:
            logging.error(f"❌ 高级泊松系统初始化失败: {e}")
            self.poisson_engine = None

    def create_widgets(self):
        """创建界面控件"""
        # 创建主滚动区域
        self.create_scrollable_content()

        # 创建各个部分
        self.create_match_info_section()
        self.create_data_preparation_section()
        self.create_analysis_controls()
        self.create_status_section()
        self.create_results_section()

    def create_scrollable_content(self):
        """创建可滚动的内容区域"""
        # 创建画布和滚动条
        self.canvas = tk.Canvas(self, bg=COLOR_BG)
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg=COLOR_BG)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

    def create_match_info_section(self):
        """创建比赛信息区域"""
        info_frame = ttk.LabelFrame(self.scrollable_frame, text="🏆 比赛信息")
        info_frame.pack(fill=tk.X, padx=15, pady=15)

        # 比赛显示标签
        self.match_label = ttk.Label(info_frame,
                                    text="未选择比赛",
                                    font=FONT_LARGE,
                                    foreground=COLOR_PRIMARY)
        self.match_label.pack(padx=15, pady=15)

        # 比赛详情
        self.match_details_label = ttk.Label(info_frame,
                                           text="请在左侧比赛列表中选择一场比赛",
                                           font=FONT_NORMAL,
                                           foreground=COLOR_TEXT)
        self.match_details_label.pack(padx=15, pady=(0, 15))

    def create_data_preparation_section(self):
        """创建数据准备区域"""
        data_frame = ttk.LabelFrame(self.scrollable_frame, text="📊 数据准备")
        data_frame.pack(fill=tk.X, padx=15, pady=10)

        # 1x2赔率状态
        odds_status_frame = tk.Frame(data_frame, bg=COLOR_BG)
        odds_status_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(odds_status_frame, text="1X2赔率:").pack(side=tk.LEFT, padx=(0, 10))
        self.odds_status_label = ttk.Label(odds_status_frame, text="未检查", foreground="orange")
        self.odds_status_label.pack(side=tk.LEFT)

        # 大小球赔率操作
        overunder_frame = tk.Frame(data_frame, bg=COLOR_BG)
        overunder_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(overunder_frame, text="大小球赔率:").pack(side=tk.LEFT, padx=(0, 10))
        self.overunder_status_label = ttk.Label(overunder_frame, text="未爬取", foreground="orange")
        self.overunder_status_label.pack(side=tk.LEFT, padx=(0, 10))

        self.scrape_overunder_button = ttk.Button(overunder_frame,
                                                 text="🕷️ 爬取大小球赔率",
                                                 command=self.scrape_overunder_odds,
                                                 state="disabled")
        self.scrape_overunder_button.pack(side=tk.LEFT)

    def create_analysis_controls(self):
        """创建分析控制区域"""
        controls_frame = ttk.LabelFrame(self.scrollable_frame, text="🔬 分析控制")
        controls_frame.pack(fill=tk.X, padx=15, pady=10)

        # 策略选择
        strategy_frame = tk.Frame(controls_frame, bg=COLOR_BG)
        strategy_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(strategy_frame, text="融合策略:").pack(side=tk.LEFT, padx=(0, 10))

        # 策略映射 - 中文显示
        self.strategy_mapping = {
            "智能模式": "auto",
            "保守策略": "conservative",
            "平衡策略": "balanced",
            "激进策略": "aggressive"
        }

        self.strategy_var = tk.StringVar(value="智能模式")
        strategy_combo = ttk.Combobox(strategy_frame,
                                     textvariable=self.strategy_var,
                                     values=list(self.strategy_mapping.keys()),
                                     state="readonly",
                                     width=15)
        strategy_combo.pack(side=tk.LEFT, padx=(0, 20))

        # 大小球盘口选择
        ttk.Label(strategy_frame, text="大小球盘口:").pack(side=tk.LEFT, padx=(0, 10))

        self.line_var = tk.StringVar(value="2.5")
        self.line_combo = ttk.Combobox(strategy_frame,
                                      textvariable=self.line_var,
                                      values=["请先爬取大小球赔率"],
                                      state="readonly",
                                      width=15)
        self.line_combo.pack(side=tk.LEFT)

        # 添加盘口选择变化监听
        self.line_combo.bind("<<ComboboxSelected>>", self.on_line_selection_changed)

        # 策略说明
        strategy_help_frame = tk.Frame(controls_frame, bg=COLOR_BG)
        strategy_help_frame.pack(fill=tk.X, padx=10, pady=(5, 10))

        strategy_help_text = ("💡 策略说明：\n"
                             "• 智能模式：根据数据质量自动调整权重配比\n"
                             "• 保守策略：更依赖历史数据 (历史70% + 市场30%)\n"
                             "• 平衡策略：历史和市场数据均衡权重 (各50%)\n"
                             "• 激进策略：更依赖市场赔率 (历史30% + 市场70%)")

        strategy_help_label = ttk.Label(strategy_help_frame,
                                       text=strategy_help_text,
                                       font=("Arial", 9),
                                       foreground=COLOR_TEXT,
                                       justify=tk.LEFT)
        strategy_help_label.pack(anchor="w")

        # 按钮区域
        buttons_frame = tk.Frame(controls_frame, bg=COLOR_BG)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        self.analyze_button = ttk.Button(buttons_frame,
                                        text="🚀 开始智能分析",
                                        command=self.start_analysis,
                                        style="Accent.TButton",
                                        state="disabled")
        self.analyze_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(buttons_frame,
                                     text="🛑 停止分析",
                                     command=self.stop_analysis,
                                     state="disabled")
        self.stop_button.pack(side=tk.LEFT)

        # 说明
        help_text = ("💡 工作流程：\n"
                    "1. 选择比赛后，系统自动检查1X2赔率\n"
                    "2. 点击'爬取大小球赔率'获取最新大小球数据\n"
                    "3. 选择融合策略和大小球盘口\n"
                    "4. 点击'智能分析'获取预测结果")

        help_label = ttk.Label(controls_frame,
                              text=help_text,
                              font=("Arial", 9),
                              foreground=COLOR_TEXT,
                              justify=tk.LEFT)
        help_label.pack(padx=10, pady=(0, 10), anchor="w")

    def create_status_section(self):
        """创建状态显示区域"""
        status_frame = ttk.LabelFrame(self.scrollable_frame, text="📊 分析状态")
        status_frame.pack(fill=tk.X, padx=15, pady=10)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame,
                                           variable=self.progress_var,
                                           mode='determinate')
        self.progress_bar.pack(fill=tk.X, padx=10, pady=10)

        # 状态标签
        self.status_label = ttk.Label(status_frame,
                                     text="准备就绪",
                                     font=FONT_NORMAL)
        self.status_label.pack(padx=10, pady=(0, 10))

        # 日志区域
        log_frame = tk.Frame(status_frame, bg=COLOR_BG)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Label(log_frame, text="分析日志:").pack(anchor="w")

        self.log_text = scrolledtext.ScrolledText(log_frame,
                                                 height=6,
                                                 font=("Consolas", 9))
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

    def create_results_section(self):
        """创建结果显示区域"""
        self.results_frame = ttk.LabelFrame(self.scrollable_frame, text="🎯 分析结果")
        self.results_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 初始提示
        self.no_results_label = ttk.Label(self.results_frame,
                                         text="暂无分析结果\n请按照工作流程完成数据准备和分析",
                                         font=FONT_NORMAL,
                                         foreground=COLOR_TEXT,
                                         justify=tk.CENTER)
        self.no_results_label.pack(expand=True, pady=50)

    def update_match_info(self, match_data, is_tab_switch=False):
        """
        更新比赛信息

        Args:
            match_data: 比赛数据字典
            is_tab_switch: 是否是标签页切换触发的更新，如果是则不恢复界面
        """
        # 只有在真正切换比赛而不是切换标签页时，才恢复所有控制面板
        if not is_tab_switch:
            # 首先恢复所有控制面板的显示，确保界面处于初始状态
            self.show_control_panels()

            # 清空之前的分析结果
            self.clear_results()

        self.current_match = match_data
        if match_data:
            # 更新比赛标题
            match_title = f"{match_data['home_team']} vs {match_data['away_team']}"
            self.match_label.config(text=match_title)

            # 更新比赛详情
            details = []
            if 'league_name' in match_data:
                details.append(f"🏆 联赛: {match_data['league_name']}")
            if 'start_time' in match_data:
                details.append(f"⏰ 时间: {match_data['start_time']}")
            if 'match_id' in match_data:
                details.append(f"🆔 ID: {match_data['match_id']}")

            self.match_details_label.config(text=" | ".join(details))

            # 检查1X2赔率数据
            self.check_1x2_odds()

            # 启用大小球爬取按钮
            self.scrape_overunder_button.config(state="normal")

            # 只有在非标签页切换时清空之前比赛的大小球数据
            if not is_tab_switch:
                self.clear_overunder_data(status_text="未爬取", status_color="orange", log_message=False)

            self.log_message(f"✅ 已选择比赛: {match_title}")
        else:
            self.match_label.config(text="未选择比赛")
            self.match_details_label.config(text="请在左侧比赛列表中选择一场比赛")
            self.scrape_overunder_button.config(state="disabled")
            self.analyze_button.config(state="disabled")

    def check_1x2_odds(self):
        """检查1X2赔率数据是否存在"""
        if not self.current_match:
            return

        try:
            match_id = self.current_match.get('match_id')
            if not match_id:
                self.odds_status_label.config(text="缺少Match ID", foreground="red")
                return

            # 直接从matches_and_odds数据库查询1X2赔率
            from config import DB_MATCHES
            import sqlite3

            conn = sqlite3.connect(DB_MATCHES)
            cursor = conn.cursor()

            # 查询该比赛的赔率数据
            cursor.execute("SELECT DISTINCT bookmaker FROM odds WHERE match_id = ?", (match_id,))
            bookmakers = cursor.fetchall()
            conn.close()

            if bookmakers:
                bookmaker_count = len(bookmakers)
                self.odds_status_label.config(
                    text=f"✅ 已有 {bookmaker_count} 家博彩公司数据",
                    foreground="green"
                )
                self.log_message(f"📊 1X2赔率: {bookmaker_count} 家博彩公司")
            else:
                self.odds_status_label.config(text="❌ 无赔率数据", foreground="red")
                self.log_message("⚠️ 未找到1X2赔率数据")

        except Exception as e:
            self.odds_status_label.config(text="❌ 检查失败", foreground="red")
            self.log_message(f"❌ 1X2赔率检查失败: {str(e)}")

    def scrape_overunder_odds(self):
        """爬取大小球赔率"""
        if not self.current_match:
            messagebox.showwarning("错误", "请先选择一场比赛")
            return

        if self.scraping_active:
            messagebox.showwarning("提示", "爬虫正在运行中")
            return

        # 清空之前的大小球数据内容
        self.clear_overunder_data()

        # 启动爬取线程
        self.scraping_active = True
        self.scrape_overunder_button.config(state="disabled")
        self.overunder_status_label.config(text="爬取中...", foreground="blue")

        scrape_thread = threading.Thread(target=self.run_overunder_scrape)
        scrape_thread.daemon = True
        scrape_thread.start()

    def clear_overunder_data(self, status_text="未获取", status_color="gray", log_message=True):
        """清空大小球相关数据

        Args:
            status_text: 状态标签显示的文本
            status_color: 状态标签的颜色
            log_message: 是否记录日志消息
        """
        try:
            # 清空可用盘口列表
            self.available_lines = []

            # 重置盘口选择下拉框
            self.line_combo['values'] = ["请先爬取大小球赔率"]
            self.line_combo.set("请先爬取大小球赔率")

            # 重置状态标签
            self.overunder_status_label.config(text=status_text, foreground=status_color)

            # 禁用分析按钮
            self.analyze_button.config(state="disabled")

            # 清空分析结果
            self.clear_results()

            if log_message:
                self.log_message("🧹 已清空之前的大小球数据")

        except Exception as e:
            self.log_message(f"❌ 清空大小球数据失败: {str(e)}")
            logging.error(f"清空大小球数据失败: {e}")

    def run_overunder_scrape(self):
        """执行大小球爬取的线程函数"""
        try:
            match_id = self.current_match.get('match_id')
            self.log_message(f"🕷️ 开始爬取比赛 {match_id} 的大小球赔率...")

            # 清空该比赛的旧大小球赔率数据
            self.log_message(f"🧹 清空比赛 {match_id} 的旧大小球赔率数据...")
            clear_success = self.overunder_scraper.clear_database_for_match(str(match_id))
            if clear_success:
                self.log_message(f"✅ 成功清空旧数据")
            else:
                self.log_message(f"⚠️ 清空旧数据失败，继续爬取新数据")

            # 爬取大小球赔率
            overunder_odds = self.overunder_scraper.fetch_overunder_odds(str(match_id))

            if overunder_odds:
                # 提取可用的盘口 - 修正属性名
                lines = set()
                for odds in overunder_odds:
                    # 使用line_numeric属性，如果没有则通过convert_line_to_numeric转换
                    if hasattr(odds, 'line_numeric') and odds.line_numeric is not None:
                        lines.add(odds.line_numeric)
                    elif odds.instant_line:
                        # 转换即时盘口
                        numeric_line = odds.convert_line_to_numeric(odds.instant_line)
                        if numeric_line is not None:
                            lines.add(numeric_line)
                    elif odds.initial_line:
                        # 转换初始盘口
                        numeric_line = odds.convert_line_to_numeric(odds.initial_line)
                        if numeric_line is not None:
                            lines.add(numeric_line)

                self.available_lines = sorted(list(lines))

                # 保存到数据库
                self.save_overunder_to_database(match_id, overunder_odds)

                # 更新UI - 修正root属性问题
                self.after(0, self.update_overunder_success, len(overunder_odds))
            else:
                self.after(0, self.update_overunder_failed)

        except Exception as e:
            self.log_message(f"❌ 大小球爬取失败: {str(e)}")
            self.after(0, self.update_overunder_failed)
        finally:
            self.scraping_active = False

    def save_overunder_to_database(self, match_id, overunder_odds):
        """保存大小球赔率到数据库"""
        try:
            # 保存大小球赔率到数据库
            self.match_db.save_overunder_odds(match_id, overunder_odds)
            self.log_message("💾 大小球赔率已保存到数据库")
        except Exception as e:
            self.log_message(f"❌ 保存大小球赔率失败: {str(e)}")

    def update_overunder_success(self, odds_count):
        """更新大小球爬取成功状态"""
        self.overunder_status_label.config(
            text=f"✅ 已获取 {odds_count} 条赔率",
            foreground="green"
        )
        self.scrape_overunder_button.config(state="normal")

        # 从数据库获取可用盘口
        if self.current_match:
            match_id = self.current_match.get('match_id')
            if match_id:
                try:
                    # 使用数据管理器获取可用盘口
                    from advanced_poisson_system.data_manager import DataManager
                    data_manager = DataManager()
                    available_lines = data_manager.get_available_overunder_lines(match_id)

                    if available_lines:
                        # 更新盘口选择
                        line_options = [str(line) for line in available_lines]
                        self.line_combo['values'] = line_options

                        # 保存当前用户选择
                        current_selection = self.line_combo.get()

                        # 只有在没有有效选择时才设置默认值
                        # 如果用户已经选择了有效的盘口，就保持不变
                        if (current_selection in ["请先爬取大小球赔率", "无可用盘口", "", "未选择比赛"]):
                            # 首次设置默认盘口：优先选择2.5，其次选择第一个可用
                            if "2.5" in line_options:
                                self.line_combo.set("2.5")
                                self.log_message(f"🎯 自动选择默认盘口: 2.5")
                            elif line_options:
                                self.line_combo.set(line_options[0])
                                self.log_message(f"🎯 自动选择默认盘口: {line_options[0]}")
                        elif current_selection in line_options:
                            # 用户选择的盘口在可用选项中，保持不变
                            self.log_message(f"🎯 保持用户选择的盘口: {current_selection}")
                        else:
                            # 用户选择的盘口不在可用选项中，需要重新选择
                            self.log_message(f"⚠️ 用户选择的盘口 {current_selection} 不可用")
                            if "2.5" in line_options:
                                self.line_combo.set("2.5")
                                self.log_message(f"🎯 重置为默认盘口: 2.5")
                            elif line_options:
                                self.line_combo.set(line_options[0])
                                self.log_message(f"🎯 重置为默认盘口: {line_options[0]}")

                        # 启用分析按钮
                        self.analyze_button.config(state="normal")

                        self.log_message(f"📊 可用盘口: {', '.join(line_options)}")

                        # 提醒用户选择正确的盘口
                        if len(available_lines) > 1:
                            self.log_message(f"💡 请在下拉框中选择您需要分析的盘口")
                    else:
                        self.line_combo['values'] = ["无可用盘口"]
                        self.line_combo.set("无可用盘口")
                        self.log_message("❌ 没有找到任何可用的大小球盘口")

                except Exception as e:
                    self.log_message(f"❌ 获取可用盘口失败: {str(e)}")
                    # 回退到旧的逻辑
                    if hasattr(self, 'available_lines') and self.available_lines:
                        line_options = [str(line) for line in self.available_lines]
                        self.line_combo['values'] = line_options

                        # 保存当前用户选择
                        current_selection = self.line_combo.get()
                        if (current_selection in ["请先爬取大小球赔率", "无可用盘口", "", "未选择比赛"] or
                            current_selection not in line_options):
                            if "2.5" in line_options:
                                self.line_combo.set("2.5")
                            else:
                                self.line_combo.set(line_options[0])

                        self.analyze_button.config(state="normal")
        else:
            self.line_combo['values'] = ["未选择比赛"]
            self.line_combo.set("未选择比赛")

    def update_overunder_failed(self):
        """更新大小球爬取失败状态"""
        self.overunder_status_label.config(text="❌ 爬取失败", foreground="red")
        self.scrape_overunder_button.config(state="normal")

    def hide_control_panels(self):
        """分析开始后隐藏控制和状态区域，让结果区域占据更多空间"""
        # 获取滚动区域中的所有帧
        all_frames = [child for child in self.scrollable_frame.winfo_children() if isinstance(child, ttk.LabelFrame)]

        # 只保留结果区域显示
        for frame in all_frames:
            # 检查frame的text属性是否为结果区域的文本
            if hasattr(frame, 'cget') and frame.cget('text') != "🎯 分析结果":
                frame.pack_forget()

        # 如果有结果框架，调整它的位置到顶部
        if self.results_frame:
            self.results_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

    def show_control_panels(self):
        """恢复显示控制和状态区域"""
        # 重新布局所有帧
        for child in self.scrollable_frame.winfo_children():
            if isinstance(child, ttk.LabelFrame):
                child.pack_forget()

        # 按照原始顺序重新添加帧
        for frame_index, frame_name in enumerate([
            "🏆 比赛信息", "📊 数据准备", "🔬 分析控制", "📊 分析状态", "🎯 分析结果"
        ]):
            for child in self.scrollable_frame.winfo_children():
                if isinstance(child, ttk.LabelFrame) and hasattr(child, 'cget') and child.cget('text') == frame_name:
                    if frame_name == "🎯 分析结果":
                        child.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
                    else:
                        child.pack(fill=tk.X, padx=15, pady=10)

    def start_analysis(self):
        """开始分析"""
        if not self.current_match:
            messagebox.showwarning("错误", "请先选择一场比赛")
            return

        if not self.poisson_engine:
            messagebox.showerror("错误", "高级泊松系统不可用")
            return

        if self.scraping_active:
            messagebox.showwarning("提示", "分析正在进行中")
            return

        # 检查数据准备情况
        if self.line_combo.get() in ["请先爬取大小球赔率", "无可用盘口"]:
            messagebox.showwarning("错误", "请先爬取大小球赔率")
            return

        # 更新UI状态
        self.scraping_active = True
        self.analyze_button.config(state="disabled")
        self.stop_button.config(state="normal")
        self.progress_var.set(0)
        self.status_label.config(text="开始分析...")

        # 清空之前的结果
        self.clear_results()

        # 隐藏控制面板，只显示结果区域
        self.hide_control_panels()

        # 开始分析线程
        analysis_thread = threading.Thread(target=self.run_analysis_thread)
        analysis_thread.daemon = True
        analysis_thread.start()

    def stop_analysis(self):
        """停止分析"""
        self.scraping_active = False
        self.log_message("⚠️ 用户取消分析")

    def run_analysis_thread(self):
        """分析线程主函数"""
        try:
            match_id = self.current_match.get('match_id')
            home_team = self.current_match.get('home_team')
            away_team = self.current_match.get('away_team')

            if not match_id or not home_team or not away_team:
                self.log_message("❌ 比赛信息不完整")
                return

            self.log_message(f"🚀 开始分析比赛: {home_team} vs {away_team} (ID: {match_id})")

            # 步骤1: 数据可用性评估 (20%)
            self.update_progress(20, "📊 评估数据可用性...")
            assessment = self.data_manager.assess_data_availability(match_id, home_team, away_team)
            self.log_message(f"📊 数据质量评分: {assessment['data_quality_percentage']:.1f}%")

            if not self.scraping_active:
                return

            # 步骤2: 执行高级分析 (80%)
            self.update_progress(80, "🧠 执行高级泊松分析...")
            chinese_strategy = self.strategy_var.get()
            strategy = self.strategy_mapping.get(chinese_strategy, "auto")  # 转换为英文策略名

            # 修复：直接从下拉框获取盘口值，确保获取到用户的实际选择
            line_string = self.line_combo.get()  # 直接从下拉框获取，而不是从StringVar
            self.log_message(f"🔍 UI获取到的盘口字符串: '{line_string}'")
            self.log_message(f"🔍 盘口下拉框当前值: '{self.line_combo.get()}'")
            self.log_message(f"🔍 盘口StringVar值: '{self.line_var.get()}'")
            self.log_message(f"🔍 盘口下拉框可选值: {self.line_combo['values']}")

            try:
                line = float(line_string)
                self.log_message(f"🔍 转换后的盘口数值: {line}")
            except ValueError as e:
                self.log_message(f"❌ 盘口转换失败: {e}")
                self.log_message("🔄 使用默认盘口 2.5")
                line = 2.5

            self.log_message(f"📈 使用策略: {chinese_strategy} ({strategy})")
            self.log_message(f"🎯 最终传递盘口: {line}")

            analysis_result = self.poisson_engine.comprehensive_match_analysis(
                match_id, home_team, away_team, strategy, line
            )

            if analysis_result['success']:
                self.analysis_result = analysis_result
                self.log_message("✅ 分析完成！")
                self.update_progress(100, "✅ 分析完成")

                # 更新结果显示 - 修正root属性问题
                self.after(0, self.display_results)
            else:
                self.log_message("❌ 分析失败")

        except Exception as e:
            self.log_message(f"❌ 分析错误: {str(e)}")
            logging.error(f"分析错误: {e}")
        finally:
            # 恢复UI状态 - 修正root属性问题
            self.after(0, self.finish_analysis)

    def display_results(self):
        """显示分析结果"""
        try:
            if not self.analysis_result:
                self.log_message("❌ 无分析结果可显示")
                return

            self.log_message("🎨 开始显示分析结果...")

            # 检查分析结果结构
            try:
                self.log_message(f"🔍 分析结果键: {list(self.analysis_result.keys())}")
                if 'poisson_predictions' in self.analysis_result:
                    pred_keys = list(self.analysis_result['poisson_predictions'].keys())
                    self.log_message(f"🔍 预测数据键: {pred_keys}")
                if 'fusion_analysis' in self.analysis_result:
                    fusion_keys = list(self.analysis_result['fusion_analysis'].keys())
                    self.log_message(f"🔍 融合分析键: {fusion_keys}")
            except Exception as e:
                self.log_message(f"⚠️ 结构检查失败: {str(e)}")

            # 清空现有结果
            for widget in self.results_frame.winfo_children():
                widget.destroy()

            # 创建结果标签页
            self.results_notebook = ttk.Notebook(self.results_frame)
            self.results_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 1. 综合报告标签页
            self.log_message("📋 创建综合报告标签页...")
            self.create_summary_tab()

            # 2. 预测详情标签页
            self.log_message("🎯 创建预测详情标签页...")
            self.create_prediction_tab()

            # 3. 技术细节标签页
            self.log_message("🔬 创建技术细节标签页...")
            self.create_technical_tab()

            # 4. 原始数据标签页
            self.log_message("📊 创建原始数据标签页...")
            self.create_data_tab()

            # 5. 添加测试标签页确保显示功能正常
            self.log_message("🧪 创建测试标签页...")
            test_frame = tk.Frame(self.results_notebook, bg=COLOR_BG)
            self.results_notebook.add(test_frame, text="🧪 测试")
            test_label = ttk.Label(test_frame,
                                  text="✅ 测试标签页\n如果您能看到这个文本，说明标签页显示功能正常\n\n分析已完成，请查看其他标签页的内容",
                                  font=FONT_NORMAL,
                                  justify=tk.CENTER)
            test_label.pack(expand=True, pady=50)
            self.log_message("🧪 测试标签页创建完成")

            # 验证结果是否真正显示
            tab_count = len(self.results_notebook.tabs())
            self.log_message(f"🔍 验证：Notebook共有 {tab_count} 个标签页")

            if tab_count > 0:
                # 强制更新界面
                self.results_notebook.update_idletasks()
                self.results_frame.update_idletasks()
                self.update_idletasks()

                # 默认选择第一个标签页（综合报告）
                self.results_notebook.select(0)

                # 确保滚动到顶部显示结果
                self.canvas.yview_moveto(0.0)

                self.log_message("🎯 已选择第一个标签页，结果应该可见")
            else:
                self.log_message("❌ 验证失败：没有创建任何标签页")
                # 创建一个简单的测试显示
                test_label = ttk.Label(self.results_frame,
                                      text="❌ 标签页创建失败\n分析已完成但显示出错\n请查看日志了解详情",
                                      font=FONT_LARGE,
                                      foreground="red",
                                      justify=tk.CENTER)
                test_label.pack(expand=True, pady=50)

            self.log_message("✅ 分析结果显示完成！")

        except Exception as e:
            self.log_message(f"❌ 显示结果时出错: {str(e)}")
            logging.error(f"显示结果错误: {e}")
            import traceback
            logging.error(traceback.format_exc())

    def create_summary_tab(self):
        """创建综合报告标签页"""
        try:
            self.log_message("📋 开始创建综合报告标签页框架...")
            summary_frame = tk.Frame(self.results_notebook, bg=COLOR_BG)
            self.results_notebook.add(summary_frame, text="📋 综合报告")

            self.log_message("📝 开始生成详细报告...")
            # 生成详细报告
            report_text = self.poisson_engine.generate_detailed_report(self.analysis_result)

            # 检查报告内容
            if report_text:
                self.log_message(f"📄 报告生成成功，长度: {len(report_text)} 字符")
                # 显示报告前几行作为验证
                preview = report_text[:100].replace('\n', ' ')
                self.log_message(f"📄 报告预览: {preview}...")
            else:
                self.log_message("❌ 报告内容为空！")
                report_text = "❌ 报告生成失败\n分析结果存在但报告生成出错"

            self.log_message("🖥️ 创建报告显示区域...")
            # 显示报告
            report_display = scrolledtext.ScrolledText(summary_frame,
                                                      font=("Consolas", 10),
                                                      wrap=tk.WORD)
            report_display.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            self.log_message("📝 插入报告文本...")
            report_display.insert(tk.END, report_text)
            report_display.config(state=tk.DISABLED)

            self.log_message("✅ 综合报告标签页创建完成")

        except Exception as e:
            self.log_message(f"❌ 创建综合报告标签页失败: {str(e)}")
            logging.error(f"创建综合报告标签页错误: {e}")
            import traceback
            logging.error(traceback.format_exc())

            # 创建错误显示
            try:
                error_frame = tk.Frame(self.results_notebook, bg=COLOR_BG)
                self.results_notebook.add(error_frame, text="❌ 报告错误")
                error_label = ttk.Label(error_frame,
                                       text=f"报告生成失败:\n{str(e)}",
                                       font=FONT_NORMAL,
                                       foreground="red",
                                       justify=tk.CENTER)
                error_label.pack(expand=True, pady=50)
            except:
                pass

    def create_prediction_tab(self):
        """创建预测详情标签页"""
        try:
            self.log_message("📈 开始创建预测详情标签页框架...")
            pred_frame = tk.Frame(self.results_notebook, bg=COLOR_BG)
            self.results_notebook.add(pred_frame, text="🎯 预测详情")

            # 创建滚动框架
            canvas = tk.Canvas(pred_frame, bg=COLOR_BG)
            scrollbar = ttk.Scrollbar(pred_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=COLOR_BG)

            scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            predictions = self.analysis_result['poisson_predictions']
            self.log_message("📊 获取预测数据成功，开始创建参数卡片...")

            # λ参数卡片
            lambda_frame = ttk.LabelFrame(scrollable_frame, text="🎯 泊松参数 (λ)")
            lambda_frame.pack(fill=tk.X, padx=10, pady=10)

            lambdas = predictions['lambda_parameters']
            lambda_info = [
                f"主队进球期望: {lambdas['home']:.3f}",
                f"客队进球期望: {lambdas['away']:.3f}",
                f"总进球期望: {lambdas['total']:.3f}"
            ]

            for info in lambda_info:
                ttk.Label(lambda_frame, text=info, font=FONT_NORMAL).pack(anchor="w", padx=10, pady=2)

            self.log_message("🏁 开始创建比赛结果卡片...")
            # 比赛结果概率卡片
            outcomes_frame = ttk.LabelFrame(scrollable_frame, text="🏁 比赛结果")
            outcomes_frame.pack(fill=tk.X, padx=10, pady=10)

            outcomes = predictions['match_outcomes']
            for outcome, data in outcomes.items():
                outcome_text = {"home_win": "主队获胜", "draw": "平局", "away_win": "客队获胜"}[outcome]
                text = f"{outcome_text}: {data['percentage']:.1f}% (赔率 {data['odds']:.2f})"
                ttk.Label(outcomes_frame, text=text, font=FONT_NORMAL).pack(anchor="w", padx=10, pady=2)

            self.log_message("🎯 开始创建最可能比分表格...")
            # 最可能比分
            scores_frame = ttk.LabelFrame(scrollable_frame, text="🎯 最可能比分")
            scores_frame.pack(fill=tk.X, padx=10, pady=10)

            scores_tree = ttk.Treeview(scores_frame, columns=("rank", "score", "prob", "odds"), show="headings", height=8)
            scores_tree.heading("rank", text="排名")
            scores_tree.heading("score", text="比分")
            scores_tree.heading("prob", text="概率")
            scores_tree.heading("odds", text="赔率")

            scores_tree.column("rank", width=50, anchor="center")
            scores_tree.column("score", width=80, anchor="center")
            scores_tree.column("prob", width=80, anchor="center")
            scores_tree.column("odds", width=80, anchor="center")

            most_likely = predictions['most_likely_scores'][:10]
            for i, score in enumerate(most_likely, 1):
                scores_tree.insert("", "end", values=(
                    i, score['score_string'],
                    f"{score['percentage']:.1f}%",
                    f"{score['odds']:.1f}"
                ))

            scores_tree.pack(fill=tk.X, padx=10, pady=10)

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            self.log_message("✅ 预测详情标签页创建完成")

        except Exception as e:
            self.log_message(f"❌ 创建预测详情标签页失败: {str(e)}")
            logging.error(f"创建预测详情标签页错误: {e}")
            import traceback
            logging.error(traceback.format_exc())

    def create_technical_tab(self):
        """创建技术细节标签页"""
        try:
            self.log_message("🔬 开始创建技术细节标签页...")
            tech_frame = tk.Frame(self.results_notebook, bg=COLOR_BG)
            self.results_notebook.add(tech_frame, text="🔬 技术细节")

            # 融合分析详情
            fusion_info = self.analysis_result['fusion_analysis']

            # 方法比较
            comparison_frame = ttk.LabelFrame(tech_frame, text="📊 方法比较")
            comparison_frame.pack(fill=tk.X, padx=10, pady=10)

            method_tree = ttk.Treeview(comparison_frame, columns=("method", "home", "away", "total"), show="headings", height=4)
            method_tree.heading("method", text="方法")
            method_tree.heading("home", text="主队λ")
            method_tree.heading("away", text="客队λ")
            method_tree.heading("total", text="总λ")

            # 添加比较数据
            hist_lambdas = fusion_info['method_comparison']['historical']
            market_lambdas = fusion_info['method_comparison']['market']
            final_lambdas = fusion_info['lambda_estimates']

            method_tree.insert("", "end", values=(
                "历史分析", f"{hist_lambdas['home']:.3f}",
                f"{hist_lambdas['away']:.3f}", f"{hist_lambdas['total']:.3f}"
            ))
            method_tree.insert("", "end", values=(
                "市场分析", f"{market_lambdas['home']:.3f}",
                f"{market_lambdas['away']:.3f}", f"{market_lambdas['total']:.3f}"
            ))
            method_tree.insert("", "end", values=(
                "融合结果", f"{final_lambdas['home']:.3f}",
                f"{final_lambdas['away']:.3f}", f"{final_lambdas['total']:.3f}"
            ))

            method_tree.pack(fill=tk.X, padx=10, pady=10)

            # 融合权重和置信度
            weights_frame = ttk.LabelFrame(tech_frame, text="⚖️ 融合配置")
            weights_frame.pack(fill=tk.X, padx=10, pady=10)

            weights = fusion_info['fusion_weights']
            assessment = fusion_info['comprehensive_assessment']

            # 将英文策略名转换为中文显示
            english_strategy = weights['strategy_used']
            chinese_strategy_name = None
            for chinese, english in self.strategy_mapping.items():
                if english == english_strategy:
                    chinese_strategy_name = chinese
                    break
            strategy_display = chinese_strategy_name or english_strategy

            weight_info = [
                f"历史数据权重: {weights['historical']:.1%}",
                f"市场数据权重: {weights['market']:.1%}",
                f"使用策略: {strategy_display}",
                f"方法一致性: {final_lambdas['method_consistency']['assessment']} ({final_lambdas['method_consistency']['score']:.1%})",
                f"综合置信度: {assessment['confidence_level']}",
                f"使用建议: {assessment['recommendation']}"
            ]

            for info in weight_info:
                ttk.Label(weights_frame, text=info, font=FONT_NORMAL).pack(anchor="w", padx=10, pady=2)

            # 庄家利润剔除方法信息
            margin_frame = ttk.LabelFrame(tech_frame, text="💰 庄家利润剔除")
            margin_frame.pack(fill=tk.X, padx=10, pady=10)

            # 检查是否有市场分析数据
            market_analysis = fusion_info.get('method_comparison', {}).get('market_analysis', {})
            if market_analysis:
                # 方法映射 - 中文显示
                margin_method_names = {
                    'proportional': '比例法(标准化)',
                    'shin': '辛氏调整法',
                    'odds_ratio': '对数几率法',
                    'additive': '加法法',
                    'multiplicative': '乘法法'
                }

                # 显示选择的剔除方法
                selected_methods = market_analysis.get('selected_methods', {})
                if selected_methods:
                    ou_method = selected_methods.get('ou_method', 'proportional')
                    x2_method = selected_methods.get('x2_method', 'proportional')

                    ou_method_name = margin_method_names.get(ou_method, ou_method)
                    x2_method_name = margin_method_names.get(x2_method, x2_method)

                    margin_info = [
                        f"大小球利润剔除: {ou_method_name}",
                        f"胜平负利润剔除: {x2_method_name}"
                    ]

                    # 显示偏差检测结果
                    margin_analysis_data = market_analysis.get('margin_analysis', {})
                    if margin_analysis_data:
                        ou_bias = margin_analysis_data.get('overunder_bias', {})
                        x2_bias = margin_analysis_data.get('1x2_bias', {})

                        if ou_bias.get('bias_detected'):
                            bias_type = ou_bias.get('bias_type', '未知')
                            bias_names = {
                                'longshot_bias': '热门-冷门偏差',
                                'extreme_odds': '极端赔率差异',
                                'general_bias': '一般偏差'
                            }
                            bias_name = bias_names.get(bias_type, bias_type)
                            margin_info.append(f"大小球偏差: {bias_name}")
                        else:
                            margin_info.append("大小球偏差: 无偏差")

                        if x2_bias.get('bias_detected'):
                            bias_type = x2_bias.get('bias_type', '未知')
                            bias_names = {
                                'longshot_bias': '热门-冷门偏差',
                                'extreme_odds': '极端赔率差异',
                                'general_bias': '一般偏差'
                            }
                            bias_name = bias_names.get(bias_type, bias_type)
                            margin_info.append(f"胜平负偏差: {bias_name}")
                        else:
                            margin_info.append("胜平负偏差: 无偏差")

                    for info in margin_info:
                        ttk.Label(margin_frame, text=info, font=FONT_NORMAL).pack(anchor="w", padx=10, pady=2)
                else:
                    ttk.Label(margin_frame, text="使用默认比例法(标准化)剔除庄家利润", font=FONT_NORMAL).pack(anchor="w", padx=10, pady=2)
            else:
                ttk.Label(margin_frame, text="使用默认比例法(标准化)剔除庄家利润", font=FONT_NORMAL).pack(anchor="w", padx=10, pady=2)

            self.log_message("✅ 技术细节标签页创建完成")

        except Exception as e:
            self.log_message(f"❌ 创建技术细节标签页失败: {str(e)}")
            logging.error(f"创建技术细节标签页错误: {e}")
            import traceback
            logging.error(traceback.format_exc())

    def create_data_tab(self):
        """创建原始数据标签页"""
        try:
            self.log_message("📊 开始创建原始数据标签页...")
            data_frame = tk.Frame(self.results_notebook, bg=COLOR_BG)
            self.results_notebook.add(data_frame, text="📊 原始数据")

            # 显示原始数据JSON
            import json

            data_text = scrolledtext.ScrolledText(data_frame, font=("Consolas", 9))
            data_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 格式化显示分析结果
            formatted_data = json.dumps(self.analysis_result, indent=2, ensure_ascii=False, default=str)
            data_text.insert(tk.END, formatted_data)
            data_text.config(state=tk.DISABLED)
            # 滚动到顶部
            data_text.see("1.0")

            self.log_message("✅ 原始数据标签页创建完成")

        except Exception as e:
            self.log_message(f"❌ 创建原始数据标签页失败: {str(e)}")
            logging.error(f"创建原始数据标签页错误: {e}")
            import traceback
            logging.error(traceback.format_exc())

    def clear_results(self):
        """清空结果显示"""
        for widget in self.results_frame.winfo_children():
            widget.destroy()

        self.no_results_label = ttk.Label(self.results_frame,
                                         text="分析中...\n请稍候",
                                         font=FONT_NORMAL,
                                         foreground=COLOR_TEXT,
                                         justify=tk.CENTER)
        self.no_results_label.pack(expand=True, pady=50)

    def finish_analysis(self):
        """完成分析，恢复UI状态"""
        self.scraping_active = False
        self.analyze_button.config(state="normal")
        self.stop_button.config(state="disabled")

        # 注意：不自动恢复控制面板，以便用户可以专注于查看结果

    def update_progress(self, value, status_text):
        """更新进度和状态"""
        self.message_queue.put(("progress", value, status_text))

    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.message_queue.put(("log", formatted_message))

    def process_messages(self):
        """处理消息队列"""
        try:
            while not self.message_queue.empty():
                message = self.message_queue.get_nowait()

                if message[0] == "progress":
                    _, value, status_text = message
                    self.progress_var.set(value)
                    self.status_label.config(text=status_text)

                elif message[0] == "log":
                    _, log_text = message
                    self.log_text.insert(tk.END, log_text + "\n")
                    self.log_text.see(tk.END)

        except Exception as e:
            logging.error(f"处理消息队列错误: {e}")
        finally:
            # 每100ms检查一次
            self.after(100, self.process_messages)

    def on_line_selection_changed(self, event):
        """处理盘口选择变化事件"""
        selected_line = self.line_combo.get()
        self.log_message(f"🎯 用户手动选择盘口: {selected_line}")

        # 检查是否需要启用分析按钮
        if selected_line not in ["请先爬取大小球赔率", "无可用盘口"]:
            self.analyze_button.config(state="normal")