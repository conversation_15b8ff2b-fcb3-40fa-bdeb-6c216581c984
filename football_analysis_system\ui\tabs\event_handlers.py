import logging
from tkinter import messagebox

class EventHandlers:
    """处理用户界面事件的类"""
    
    def __init__(self, interval_tree, draw_interval_tree):
        """
        初始化事件处理器
        
        Args:
            interval_tree: 区间分析表格
            draw_interval_tree: 平赔区间分析表格
        """
        self.interval_tree = interval_tree
        self.draw_interval_tree = draw_interval_tree
        self.selected_items = {}  # 存储选中的项目
        
    def on_interval_tree_click(self, event):
        """处理区间分析表格点击事件"""
        # 获取点击的列和项
        region = self.interval_tree.identify("region", event.x, event.y)
        column = self.interval_tree.identify_column(event.x)
        item = self.interval_tree.identify_row(event.y)
        
        # 添加调试日志
        logging.info(f"区间分析表格点击: region={region}, column={column}, item={item}")
        
        # 如果点击的是表格单元格并且是有效行
        if region == "cell" and item:
            try:
                values = self.interval_tree.item(item, "values")
                tags = self.interval_tree.item(item, "tags")
                
                # 记录原始值以便调试
                logging.info(f"原始值: {values}")
                
                # 确保不是分隔行或组标题行
                if "separator" in tags or "group_title" in tags:
                    return
                    
                # 确保有公司名称的行才可选择
                if not values[2]:  # 公司名称为空
                    return
                    
                # 切换选择状态
                current_state = "✓" if values[0] == "✓" else ""
                new_state = "" if current_state else "✓"
                
                # 更新项目值
                new_values = list(values)
                new_values[0] = new_state
                self.interval_tree.item(item, values=new_values)
                
                # 记录新值以便调试
                logging.info(f"更新后的值: {new_values}")
                
                # 保存选择状态
                self.selected_items[item] = new_state == "✓"
                
                # 提供反馈但不显示消息框，避免干扰用户
                logging.info(f"已{'' if new_state else '取消'}选择记录")
            except Exception as e:
                logging.error(f"处理点击事件时出错: {e}")
                messagebox.showerror("错误", f"选择操作失败: {e}")

    def on_draw_interval_tree_click(self, event):
        """处理平赔区间分析表格点击事件"""
        # 获取点击的列和项
        region = self.draw_interval_tree.identify("region", event.x, event.y)
        column = self.draw_interval_tree.identify_column(event.x)
        item = self.draw_interval_tree.identify_row(event.y)
        
        # 添加调试日志
        logging.info(f"平赔表格点击: region={region}, column={column}, item={item}")
        
        # 如果点击的是表格单元格并且是有效行
        if region == "cell" and item:
            try:
                values = self.draw_interval_tree.item(item, "values")
                
                # 记录原始值以便调试
                logging.info(f"原始值: {values}")
                
                # 确保有公司名称的行才可选择
                if not values[1]:  # 公司名称为空
                    return
                
                # 切换选择状态
                current_state = "✓" if values[0] == "✓" else ""
                new_state = "" if current_state else "✓"
                
                # 更新项目值
                new_values = list(values)
                new_values[0] = new_state
                self.draw_interval_tree.item(item, values=new_values)
                
                # 记录新值以便调试
                logging.info(f"更新后的值: {new_values}")
                
                # 保存选择状态
                self.selected_items[item] = new_state == "✓"
                
                # 提供反馈但不显示消息框，避免干扰用户
                logging.info(f"已{'' if new_state else '取消'}选择记录")
            except Exception as e:
                logging.error(f"处理点击事件时出错: {e}")
                messagebox.showerror("错误", f"选择操作失败: {e}")
                
    def clear_selections(self):
        """清除所有选择"""
        self.selected_items = {}