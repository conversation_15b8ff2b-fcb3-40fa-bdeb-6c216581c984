import tkinter as tk
from tkinter import ttk, messagebox
import logging
import sqlite3
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import seaborn as sns

from football_analysis_system.config import COLOR_BG, COLOR_PRIMARY, COLOR_TEXT, DATA_DIR
from football_analysis_system.config import FONT_LARGE, FONT_NORMAL, FONT_SUBHEADER

# 导入泊松分布核心计算模块
from football_analysis_system.analysis.poisson_module import (
    get_available_leagues,
    load_match_data,
    calculate_league_averages,
    calculate_team_stats,
    calculate_poisson_probabilities,
    create_heatmap_data,
    create_total_goals_data
)

class PoissonTab(tk.Frame):
    """泊松分布分析标签页"""

    def __init__(self, parent):
        """
        初始化泊松分布分析标签页

        Args:
            parent: 父容器
        """
        super().__init__(parent, bg=COLOR_BG)

        # 初始化变量
        self.db_path = self.get_database_path()
        self.match_data = None
        self.teams = []
        self.team_stats = {}
        self.league_avg_home = 0
        self.league_avg_away = 0
        self.current_match = None
        self.max_goals = 5
        self.recent_matches = 10
        self.result_data = None

        # 设置matplotlib中文字体支持
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong', 'SimSun']
        plt.rcParams['axes.unicode_minus'] = False

        self.create_widgets()

    def get_database_path(self):
        """
        获取数据库文件的路径，指向项目根目录的data文件夹

        Returns:
            str: 数据库文件的绝对路径
        """
        # 首先获取当前模块文件的绝对路径
        current_file = os.path.abspath(__file__)

        # ui/tabs/poisson_tab.py -> ui -> football_analysis_system
        ui_dir = os.path.dirname(os.path.dirname(current_file))
        football_analysis_dir = os.path.dirname(ui_dir)

        # 直接使用项目目录下的data文件夹
        data_dir = os.path.join(football_analysis_dir, "data")

        # 确保数据目录存在
        if not os.path.exists(data_dir):
            os.makedirs(data_dir, exist_ok=True)
            logging.warning(f"创建了数据目录: {data_dir}")

        # 构建数据库文件路径
        db_path = os.path.join(data_dir, "football.db")

        logging.info(f"数据库路径: {db_path}")
        return db_path

    def create_widgets(self):
        """创建标签页控件"""
        # 创建界面元素
        self.create_info_section()
        self.create_settings_section()
        self.create_results_section()

    def create_info_section(self):
        """创建比赛信息区域"""
        # 信息框架 - 使用现代化的卡片式设计
        self.info_frame = ttk.LabelFrame(self, text="当前选中比赛",
                                      style="TLabelframe")
        self.info_frame.pack(fill=tk.X, padx=15, pady=15)

        # 显示比赛信息 - 使用更现代的样式
        self.match_label = ttk.Label(self.info_frame, text="未选择比赛",
                                 font=FONT_LARGE,
                                 foreground=COLOR_TEXT,
                                 background=COLOR_BG,
                                 style="MatchInfo.TLabel")
        self.match_label.pack(padx=15, pady=15)

        # 添加泊松分析按钮
        self.analyze_button = ttk.Button(self, text="计算泊松分布分析",
                                      command=self.run_poisson_analysis,
                                      style="Accent.TButton")
        self.analyze_button.pack(padx=15, pady=15)
        self.analyze_button.config(state="disabled")  # 初始状态为禁用

        # 说明信息
        info_text = """
        泊松分布分析可以基于历史数据预测比赛可能的比分和结果。
        请选择比赛后点击分析按钮。
        """
        self.info_label = ttk.Label(self, text=info_text,
                                 foreground=COLOR_TEXT,
                                 background=COLOR_BG,
                                 justify=tk.LEFT, wraplength=650)
        self.info_label.pack(padx=15, pady=10, fill=tk.X)

    def create_settings_section(self):
        """创建设置区域"""
        # 设置框架 - 使用现代化的卡片式设计
        settings_frame = ttk.LabelFrame(self, text="分析设置", style="TLabelframe")
        settings_frame.pack(fill=tk.X, padx=15, pady=10)

        # 内部容器
        inner_frame = ttk.Frame(settings_frame)
        inner_frame.pack(fill=tk.X, padx=10, pady=10)

        # 最大进球数设置 - 使用更现代的样式
        max_goals_label = ttk.Label(inner_frame, text="最大进球数:",
                                foreground=COLOR_TEXT,
                                background=COLOR_BG)
        max_goals_label.grid(row=0, column=0, padx=10, pady=10, sticky='w')

        self.max_goals_var = tk.StringVar(value=str(self.max_goals))
        max_goals_combo = ttk.Combobox(inner_frame, textvariable=self.max_goals_var,
                                   values=["3", "4", "5", "6", "7"], width=5)
        max_goals_combo.grid(row=0, column=1, padx=10, pady=10, sticky='w')
        max_goals_combo.bind("<<ComboboxSelected>>", self.on_settings_changed)

        # 近期比赛场次设置 - 使用更现代的样式
        recent_matches_label = ttk.Label(inner_frame, text="近期比赛场次:",
                                     foreground=COLOR_TEXT,
                                     background=COLOR_BG)
        recent_matches_label.grid(row=0, column=2, padx=10, pady=10, sticky='w')

        self.recent_matches_var = tk.StringVar(value=str(self.recent_matches))
        recent_matches_combo = ttk.Combobox(inner_frame, textvariable=self.recent_matches_var,
                                        values=["5", "8", "10", "15", "20"], width=5)
        recent_matches_combo.grid(row=0, column=3, padx=10, pady=10, sticky='w')
        recent_matches_combo.bind("<<ComboboxSelected>>", self.on_settings_changed)

    def create_results_section(self):
        """创建结果显示区域"""
        # 创建标签页控件用于展示不同结果 - 使用更现代的样式
        self.results_notebook = ttk.Notebook(self, style="TNotebook")

        # 摘要标签页 - 使用更现代的框架
        self.summary_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.summary_frame, text="结果摘要")

        # 热图标签页 - 使用更现代的框架
        self.heatmap_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.heatmap_frame, text="比分热图")

        # 图表标签页 - 使用更现代的框架
        self.charts_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.charts_frame, text="统计图表")

        # 球队统计标签页 - 使用更现代的框架
        self.stats_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.stats_frame, text="球队统计")

        # 初始时不显示结果区域
        # 结果区域将在计算完成后显示

    def update_match_info(self, match_data, is_tab_switch=False):
        """
        更新比赛信息

        Args:
            match_data: 比赛数据字典
            is_tab_switch: 是否是标签页切换触发的更新，如果是则不恢复界面
        """
        # 先检查是否切换了比赛
        is_new_match = (self.current_match is None or
                        self.current_match.get('home_team') != match_data.get('home_team') or
                        self.current_match.get('away_team') != match_data.get('away_team'))

        # 更新当前比赛信息
        self.current_match = match_data

        if match_data:
            self.match_label.config(text=f"{match_data['home_team']} vs {match_data['away_team']}")

            # 检查所选联赛是否支持泊松分析
            league_name = match_data.get('league_name', '')
            if league_name:
                try:
                    # 连接数据库
                    conn = sqlite3.connect(self.db_path)

                    # 获取可用联赛列表
                    available_leagues = get_available_leagues(conn)
                    conn.close()

                    if league_name not in available_leagues:
                        self.match_label.config(text=f"{match_data['home_team']} vs {match_data['away_team']} (联赛不支持泊松分析)")
                        self.info_label.config(text=f"注意: {league_name} 在泊松分析数据库中没有历史数据，无法进行精确分析。\n您可以继续尝试分析，但结果可能不准确。")
                except Exception as e:
                    logging.error(f"检查联赛支持状态时出错: {e}")

            self.analyze_button.config(state="normal")

            # 如果是新比赛且不是标签页切换，清空上一次的分析结果并隐藏结果区域
            if is_new_match and not is_tab_switch:
                self.result_data = None

                # 隐藏结果标签页，等待用户再次点击分析按钮
                if hasattr(self, 'results_notebook') and self.results_notebook.winfo_ismapped():
                    self.results_notebook.pack_forget()
        else:
            self.match_label.config(text="未选择比赛")
            self.analyze_button.config(state="disabled")

            # 清空和隐藏结果，但只在非标签页切换时
            if not is_tab_switch:
                self.result_data = None
                if hasattr(self, 'results_notebook') and self.results_notebook.winfo_ismapped():
                    self.results_notebook.pack_forget()

    def on_settings_changed(self, event=None):
        """设置变更事件处理"""
        try:
            self.max_goals = int(self.max_goals_var.get())
            self.recent_matches = int(self.recent_matches_var.get())

            # 如果已有结果数据，重新计算
            if self.result_data:
                self.run_poisson_analysis()
        except ValueError:
            pass

    def load_league_data(self, league_name):
        """
        加载联赛数据

        Args:
            league_name: 联赛名称

        Returns:
            bool: 是否成功加载数据
        """
        try:
            # 连接数据库
            conn = sqlite3.connect(self.db_path)

            # 加载比赛数据
            df = load_match_data(conn, league_name)
            conn.close()

            if len(df) == 0:
                messagebox.showwarning("数据加载", f"未找到 {league_name} 的比赛数据")
                return False

            # 保存数据
            self.match_data = df

            # 获取唯一的球队
            home_teams = df['HomeTeam'].unique()
            away_teams = df['AwayTeam'].unique()
            self.teams = sorted(list(set(home_teams) | set(away_teams)))

            # 计算联赛平均值
            self.league_avg_home, self.league_avg_away = calculate_league_averages(df)

            # 计算球队统计数据
            self.team_stats = calculate_team_stats(df, self.teams, self.recent_matches)

            return True
        except Exception as e:
            messagebox.showerror("数据加载错误", f"加载联赛数据时出错: {str(e)}")
            logging.error(f"加载联赛数据出错: {e}")
            return False

    def run_poisson_analysis(self):
        """执行泊松分布分析"""
        if not self.current_match:
            messagebox.showwarning("分析错误", "请先选择一个比赛")
            return

        try:
            # 获取比赛信息
            league = self.current_match['league_name']
            home_team = self.current_match['home_team']
            away_team = self.current_match['away_team']

            # 需要重新加载数据的情况：
            # 1. 没有数据
            # 2. 联赛变更（如果有match_data且至少有一行数据，检查league_name是否相同）
            current_league = None
            if self.match_data is not None and len(self.match_data) > 0 and 'league_name' in self.match_data.columns:
                # 获取当前加载数据的联赛名称
                if len(self.match_data['league_name'].unique()) > 0:
                    current_league = self.match_data['league_name'].unique()[0]

            # 如果当前没有数据或者联赛已经变更，则重新加载数据
            if self.match_data is None or len(self.match_data) == 0 or current_league != league:
                logging.info(f"需要加载新联赛数据: {league}")
                if not self.load_league_data(league):
                    return

            # 更新设置
            try:
                self.max_goals = int(self.max_goals_var.get())
                self.recent_matches = int(self.recent_matches_var.get())
            except ValueError:
                pass

            # 重新计算球队统计（以防settings已变化）
            self.team_stats = calculate_team_stats(self.match_data, self.teams, self.recent_matches)

            # 检查球队是否在当前加载的数据中
            if home_team not in self.teams or away_team not in self.teams:
                messagebox.showwarning("分析错误", f"在当前联赛数据中找不到球队: {home_team} 或 {away_team}")
                return

            # 计算泊松分布
            result = calculate_poisson_probabilities(
                home_team,
                away_team,
                self.team_stats,
                self.league_avg_home,
                self.league_avg_away,
                self.max_goals
            )

            if not result:
                messagebox.showwarning("分析错误", f"无法计算 {home_team} vs {away_team} 的泊松分布")
                return

            # 保存结果
            self.result_data = result

            # 更新UI显示
            self.update_results_display()

            # 显示结果区域
            self.results_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        except Exception as e:
            messagebox.showerror("分析错误", f"执行泊松分布分析时出错: {str(e)}")
            logging.error(f"泊松分布分析出错: {e}")

    def update_results_display(self):
        """更新结果显示"""
        if not self.result_data:
            return

        # 清空当前显示
        for widget in self.summary_frame.winfo_children():
            widget.destroy()
        for widget in self.heatmap_frame.winfo_children():
            widget.destroy()
        for widget in self.charts_frame.winfo_children():
            widget.destroy()
        for widget in self.stats_frame.winfo_children():
            widget.destroy()

        # 更新摘要页
        self.update_summary_tab()

        # 更新热图页
        self.update_heatmap_tab()

        # 更新图表页
        self.update_charts_tab()

        # 更新球队统计页
        self.update_stats_tab()

    def update_summary_tab(self):
        """更新摘要标签页"""
        result = self.result_data
        home_team = result['home_team']
        away_team = result['away_team']
        outcomes = result['outcomes']
        exp_goals = result['expected_goals']
        most_likely = result['most_likely']

        # 创建三个卡片布局
        cards_frame = tk.Frame(self.summary_frame, bg=COLOR_BG)
        cards_frame.pack(fill=tk.X, padx=10, pady=10)

        # 预期进球卡片
        exp_goals_frame = tk.LabelFrame(cards_frame, text="预期进球",
                                      font=FONT_SUBHEADER,
                                      bg=COLOR_BG, fg=COLOR_PRIMARY)
        exp_goals_frame.pack(side=tk.LEFT, padx=10, fill=tk.BOTH, expand=True)

        home_exp = tk.Label(exp_goals_frame,
                          text=f"{home_team}: {exp_goals['home']:.2f}",
                          bg=COLOR_BG, fg=COLOR_TEXT, font=FONT_NORMAL)
        home_exp.pack(padx=5, pady=5)

        away_exp = tk.Label(exp_goals_frame,
                          text=f"{away_team}: {exp_goals['away']:.2f}",
                          bg=COLOR_BG, fg=COLOR_TEXT, font=FONT_NORMAL)
        away_exp.pack(padx=5, pady=5)

        # 比赛结果卡片
        outcomes_frame = tk.LabelFrame(cards_frame, text="比赛结果概率",
                                     font=FONT_SUBHEADER,
                                     bg=COLOR_BG, fg=COLOR_PRIMARY)
        outcomes_frame.pack(side=tk.LEFT, padx=10, fill=tk.BOTH, expand=True)

        home_win = tk.Label(outcomes_frame,
                          text=f"主胜: {outcomes['home_win']:.2f}%",
                          bg=COLOR_BG, fg=COLOR_TEXT, font=FONT_NORMAL)
        home_win.pack(padx=5, pady=5)

        draw = tk.Label(outcomes_frame,
                      text=f"平局: {outcomes['draw']:.2f}%",
                      bg=COLOR_BG, fg=COLOR_TEXT, font=FONT_NORMAL)
        draw.pack(padx=5, pady=5)

        away_win = tk.Label(outcomes_frame,
                          text=f"客胜: {outcomes['away_win']:.2f}%",
                          bg=COLOR_BG, fg=COLOR_TEXT, font=FONT_NORMAL)
        away_win.pack(padx=5, pady=5)

        # 最可能比分卡片
        score_frame = tk.LabelFrame(cards_frame, text="最可能比分",
                                  font=FONT_SUBHEADER,
                                  bg=COLOR_BG, fg=COLOR_PRIMARY)
        score_frame.pack(side=tk.LEFT, padx=10, fill=tk.BOTH, expand=True)

        score = tk.Label(score_frame,
                       text=f"{most_likely['home_goals']}-{most_likely['away_goals']}",
                       bg=COLOR_BG, fg=COLOR_TEXT, font=FONT_LARGE)
        score.pack(padx=5, pady=5)

        prob = tk.Label(score_frame,
                      text=f"概率: {most_likely['percentage']:.2f}%",
                      bg=COLOR_BG, fg=COLOR_TEXT, font=FONT_NORMAL)
        prob.pack(padx=5, pady=5)

        # 比分概率表
        scores_frame = tk.LabelFrame(self.summary_frame, text="可能比分",
                                   font=FONT_SUBHEADER,
                                   bg=COLOR_BG, fg=COLOR_PRIMARY)
        scores_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建表格
        scores_tree = ttk.Treeview(scores_frame, columns=("score", "prob", "odds"),
                                 show="headings", height=10)
        scores_tree.heading("score", text="比分")
        scores_tree.heading("prob", text="概率")
        scores_tree.heading("odds", text="赔率")

        scores_tree.column("score", width=100, anchor="center")
        scores_tree.column("prob", width=100, anchor="center")
        scores_tree.column("odds", width=100, anchor="center")

        scores_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(scores_frame, orient="vertical", command=scores_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        scores_tree.configure(yscrollcommand=scrollbar.set)

        # 填充表格数据（前10个最可能的比分）
        for i, prob in enumerate(self.result_data['probabilities'][:10]):
            score_text = f"{prob['home_goals']}-{prob['away_goals']}"
            prob_text = f"{prob['percentage']:.2f}%"
            odds_text = f"{prob['odds']:.2f}" if prob['odds'] != float('inf') else "N/A"

            scores_tree.insert("", "end", values=(score_text, prob_text, odds_text))

    def update_heatmap_tab(self):
        """更新热图标签页"""
        # 获取数据
        home_team = self.result_data['home_team']
        away_team = self.result_data['away_team']

        # 创建热图数据
        heatmap_data = create_heatmap_data(self.result_data['probabilities'], self.max_goals)

        # 创建图形
        fig = Figure(figsize=(10, 8), dpi=100)
        ax = fig.add_subplot(111)

        # 绘制热图
        sns.heatmap(heatmap_data, annot=True, fmt='.1f', cmap='Blues',
                   ax=ax, linewidths=0.5)

        # 设置标签
        ax.set_xlabel(f'{home_team} 进球数')
        ax.set_ylabel(f'{away_team} 进球数')
        ax.set_title('比分概率热力图 (%)')

        # 确保y轴是正确顺序（从上到下）
        ax.invert_yaxis()

        # 添加到框架
        canvas = FigureCanvasTkAgg(fig, master=self.heatmap_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    def update_charts_tab(self):
        """更新图表标签页"""
        # 创建总进球分布数据
        total_goals_data = create_total_goals_data(self.result_data['probabilities'])

        # 创建总进球分布图
        total_goals_frame = tk.LabelFrame(self.charts_frame, text="总进球分布",
                                        font=FONT_SUBHEADER,
                                        bg=COLOR_BG, fg=COLOR_PRIMARY)
        total_goals_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        fig_goals = Figure(figsize=(10, 4), dpi=100)
        ax_goals = fig_goals.add_subplot(111)

        x = [item[0] for item in total_goals_data]
        y = [item[1] for item in total_goals_data]

        ax_goals.bar(x, y, color='skyblue', edgecolor='navy')
        ax_goals.set_xlabel('总进球数')
        ax_goals.set_ylabel('概率 (%)')
        ax_goals.set_title('总进球分布')

        # 在柱状图上方添加数值标签
        for i, v in enumerate(y):
            ax_goals.text(x[i], v + 0.5, f'{v:.1f}%',
                      ha='center', va='bottom', fontsize=8)

        ax_goals.set_xticks(x)

        # 添加到框架
        canvas_goals = FigureCanvasTkAgg(fig_goals, master=total_goals_frame)
        canvas_goals.draw()
        canvas_goals.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 创建球队实力对比图
        team_strength_frame = tk.LabelFrame(self.charts_frame, text="球队实力对比",
                                          font=FONT_SUBHEADER,
                                          bg=COLOR_BG, fg=COLOR_PRIMARY)
        team_strength_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 提取数据
        home_team = self.result_data['home_team']
        away_team = self.result_data['away_team']
        home_stats = self.result_data['team_stats']['home']
        away_stats = self.result_data['team_stats']['away']

        # 对比类别 - 移除了不可用的统计数据
        categories = [
            '主场进攻', '主场防守', '客场进攻', '客场防守'
        ]

        # 对比数值（防守值取反，使得较高值更好）
        home_values = [
            home_stats['home_attack'],
            2 - home_stats['home_defense'],  # 防守值取反
            home_stats['away_attack'],
            2 - home_stats['away_defense']  # 防守值取反
        ]

        away_values = [
            away_stats['home_attack'],
            2 - away_stats['home_defense'],  # 防守值取反
            away_stats['away_attack'],
            2 - away_stats['away_defense']  # 防守值取反
        ]

        # 添加半场进球统计（如果有）
        if 'avg_ht_home_goals' in home_stats:
            categories.append('半场进球能力')
            home_ht_ability = (home_stats['avg_ht_home_goals'] + home_stats['avg_ht_away_goals']) / 2
            away_ht_ability = (away_stats['avg_ht_home_goals'] + away_stats['avg_ht_away_goals']) / 2
            home_values.append(home_ht_ability * 2)  # 放大差异
            away_values.append(away_ht_ability * 2)  # 放大差异

        # 创建图形
        fig_strength = Figure(figsize=(10, 4), dpi=100)
        ax_strength = fig_strength.add_subplot(111)

        x = np.arange(len(categories))
        width = 0.35

        ax_strength.bar(x - width/2, home_values, width,
                     label=home_team, color='royalblue')
        ax_strength.bar(x + width/2, away_values, width,
                     label=away_team, color='forestgreen')

        ax_strength.set_ylabel('实力评分')
        ax_strength.set_title('球队实力对比')
        ax_strength.set_xticks(x)
        ax_strength.set_xticklabels(categories)
        ax_strength.legend()

        # 添加到框架
        canvas_strength = FigureCanvasTkAgg(fig_strength, master=team_strength_frame)
        canvas_strength.draw()
        canvas_strength.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def update_stats_tab(self):
        """更新球队统计标签页"""
        # 创建框架
        stats_container = tk.Frame(self.stats_frame, bg=COLOR_BG)
        stats_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 提取数据
        home_team = self.result_data['home_team']
        away_team = self.result_data['away_team']
        home_stats = self.result_data['team_stats']['home']
        away_stats = self.result_data['team_stats']['away']

        # 主队统计
        home_frame = tk.LabelFrame(stats_container, text=f"{home_team} 统计数据",
                                 font=FONT_SUBHEADER,
                                 bg=COLOR_BG, fg=COLOR_PRIMARY)
        home_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建主队统计表格
        self.create_stats_table(home_frame, home_stats)

        # 客队统计
        away_frame = tk.LabelFrame(stats_container, text=f"{away_team} 统计数据",
                                 font=FONT_SUBHEADER,
                                 bg=COLOR_BG, fg=COLOR_PRIMARY)
        away_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建客队统计表格
        self.create_stats_table(away_frame, away_stats)

    def create_stats_table(self, parent, stats):
        """创建球队统计表格"""
        # 创建表格框架
        table_frame = tk.Frame(parent, bg=COLOR_BG)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加统计行
        row = 0

        # 比赛场次
        self.add_stat_row(table_frame, row, "主场比赛:", f"{stats['home_games']}")
        row += 1

        self.add_stat_row(table_frame, row, "客场比赛:", f"{stats['away_games']}")
        row += 1

        # 进球数据
        avg_home_goals = stats['home_goals']/stats['home_games'] if stats['home_games'] > 0 else 0
        self.add_stat_row(table_frame, row, "主场进球:",
                       f"{stats['home_goals']} ({avg_home_goals:.2f}/场)")
        row += 1

        avg_away_goals = stats['away_goals']/stats['away_games'] if stats['away_games'] > 0 else 0
        self.add_stat_row(table_frame, row, "客场进球:",
                       f"{stats['away_goals']} ({avg_away_goals:.2f}/场)")
        row += 1

        # 失球数据
        avg_home_conceded = stats['home_conceded']/stats['home_games'] if stats['home_games'] > 0 else 0
        self.add_stat_row(table_frame, row, "主场失球:",
                       f"{stats['home_conceded']} ({avg_home_conceded:.2f}/场)")
        row += 1

        avg_away_conceded = stats['away_conceded']/stats['away_games'] if stats['away_games'] > 0 else 0
        self.add_stat_row(table_frame, row, "客场失球:",
                       f"{stats['away_conceded']} ({avg_away_conceded:.2f}/场)")
        row += 1

        # 添加半场进球统计（如果有）
        if 'ht_home_goals' in stats:
            avg_ht_home_goals = stats['ht_home_goals']/stats['home_games'] if stats['home_games'] > 0 else 0
            self.add_stat_row(table_frame, row, "半场主场进球:",
                           f"{stats['ht_home_goals']} ({avg_ht_home_goals:.2f}/场)")
            row += 1

            avg_ht_away_goals = stats['ht_away_goals']/stats['away_games'] if stats['away_games'] > 0 else 0
            self.add_stat_row(table_frame, row, "半场客场进球:",
                           f"{stats['ht_away_goals']} ({avg_ht_away_goals:.2f}/场)")
            row += 1

        # 攻防强度数据
        self.add_stat_row(table_frame, row, "主场进攻强度:", f"{stats['home_attack']:.2f}")
        row += 1

        self.add_stat_row(table_frame, row, "主场防守强度:", f"{stats['home_defense']:.2f}")
        row += 1

        self.add_stat_row(table_frame, row, "客场进攻强度:", f"{stats['away_attack']:.2f}")
        row += 1

        self.add_stat_row(table_frame, row, "客场防守强度:", f"{stats['away_defense']:.2f}")
        row += 1

        # 基于最近比赛数据
        self.add_stat_row(table_frame, row, f"基于近期:", f"{stats['total_games']} 场比赛")

    def add_stat_row(self, parent, row, label_text, value_text):
        """添加统计行"""
        label = tk.Label(parent, text=label_text,
                       bg=COLOR_BG, fg=COLOR_TEXT,
                       font=FONT_NORMAL)
        label.grid(row=row, column=0, padx=5, pady=2, sticky='w')

        value = tk.Label(parent, text=value_text,
                       bg=COLOR_BG, fg=COLOR_TEXT,
                       font=FONT_NORMAL)
        value.grid(row=row, column=1, padx=5, pady=2, sticky='w')