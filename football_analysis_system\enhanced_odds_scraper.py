#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版赔率爬虫 - 新增记录ID字段的解析和存储
"""

import time
import logging
import re
import sqlite3
import os
from datetime import datetime
from scrapers.odds_scraper import OddsScraper

class EnhancedOddsScraper(OddsScraper):
    """增强版赔率爬虫，支持记录ID字段的解析和存储"""
    
    def __init__(self, url_template, headers_template, target_companies, db_path="data/matches_and_odds.db"):
        """
        初始化增强版赔率爬虫
        
        Args:
            url_template: 请求URL模板
            headers_template: 请求头模板
            target_companies: 目标博彩公司列表
            db_path: 数据库路径
        """
        super().__init__(url_template, headers_template, target_companies)
        self.db_path = db_path
        self._create_record_id_table()
    
    def _create_record_id_table(self):
        """创建记录ID存储表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建记录ID表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS odds_record_ids (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    match_id TEXT NOT NULL,
                    company_id TEXT NOT NULL,
                    company_name TEXT NOT NULL,
                    record_id TEXT NOT NULL,
                    update_timestamp TEXT NOT NULL,
                    scrape_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(match_id, company_id, record_id)
                )
            ''')
            
            # 创建索引以提高查询性能
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_odds_record_ids_match_company 
                ON odds_record_ids(match_id, company_id)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_odds_record_ids_record_id 
                ON odds_record_ids(record_id)
            ''')
            
            conn.commit()
            conn.close()
            
            logging.info("✅ 记录ID表创建成功")
            
        except Exception as e:
            logging.error(f"❌ 创建记录ID表失败: {e}")
            if 'conn' in locals():
                conn.close()
    
    def parse_odds_js_enhanced(self, odds_js_content):
        """
        增强版赔率JS解析，同时提取赔率数据和记录ID
        
        Args:
            odds_js_content: JavaScript内容
            
        Returns:
            tuple: (odds_data, record_ids_data)
        """
        if not odds_js_content:
            logging.warning("没有赔率JS内容可解析")
            return {}, {}
        
        odds_data = {}
        record_ids_data = {}
        
        # 查找game数组
        game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', odds_js_content, re.DOTALL)
        if not game_match:
            logging.warning("在JS内容中未找到game数组")
            return {}, {}
        
        game_data = game_match.group(1)
        
        # 提取每个引号包围的字符串
        company_records = re.findall(r'"([^"]*)"', game_data)
        
        found_count = 0
        # 解析每个公司记录
        for record in company_records:
            try:
                fields = record.split('|')
                
                if len(fields) < 16:
                    continue  # 跳过字段不足的记录
                
                company_id = fields[0]
                record_id = fields[1]  # 新增：记录ID
                company_name = fields[2].strip()
                
                # 初始赔率 - 索引3,4,5
                initial_w = fields[3].strip()  # 主胜
                initial_d = fields[4].strip()  # 平
                initial_l = fields[5].strip()  # 客胜
                
                # 即时赔率 - 索引10,11,12
                instant_w = fields[10].strip()  # 主胜
                instant_d = fields[11].strip()  # 平
                instant_l = fields[12].strip()  # 客胜
                
                # 返还率 - 索引9为初始返还率，索引16为即时返还率
                initial_payout_rate = fields[9].strip() if len(fields) > 9 else ""
                payout_rate = fields[16].strip() if len(fields) > 16 else ""
                
                # 新增：更新时间戳 - 索引20
                update_timestamp = fields[20].strip() if len(fields) > 20 else ""
                
                # 匹配目标公司
                matched_target_name = None
                for target_name in self.target_companies:
                    normalized_target_name = target_name.lower().replace(' ','')
                    normalized_company_name = company_name.lower().replace(' ','')
                    
                    # 精确匹配
                    if normalized_target_name == normalized_company_name:
                        matched_target_name = target_name
                        break
                    
                    # 特殊匹配规则（保持原有逻辑）
                    if target_name == '威廉希尔' and (company_id == '115' or company_name.startswith('威') or 'william hill' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == '易胜博' and (company_id == '90' or company_name.startswith('易') or 'easybets' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == '香港马会' and (company_id == '432' or company_name.startswith('香港马') or 'hkjc' in normalized_company_name or 'hk jockey club' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == '澳门' and (company_id == '80' or company_name.startswith('澳') or 'macao' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == 'BWIN' and (company_id == '255' or company_name.startswith('Bwi')):
                        matched_target_name = target_name
                        break
                    if target_name == '伟德' and (company_id == '81' or company_name.startswith('伟')):
                        matched_target_name = target_name
                        break
                    if target_name == 'Nordicbet' and (company_id == '4' or company_name.startswith('Nordic')):
                        matched_target_name = target_name
                        break
                    if target_name == '利记' and (company_id == '474' or company_name.startswith('利')):
                        matched_target_name = target_name
                        break
                    if target_name == 'BetISn' and (company_id == '937' or 'betisn' in normalized_company_name):
                        matched_target_name = target_name
                        break
                    if target_name == 'iddaa' and (company_id == '657' or 'iddaa' in normalized_company_name):
                        matched_target_name = target_name
                        break
                
                if matched_target_name:
                    # 存储赔率数据（原有格式）
                    odds_data[matched_target_name] = {
                        'Initial_W': initial_w,
                        'Initial_D': initial_d,
                        'Initial_L': initial_l,
                        'Instant_W': instant_w,
                        'Instant_D': instant_d,
                        'Instant_L': instant_l,
                        'Payout_Rate': payout_rate,
                        'Initial_Payout_Rate': initial_payout_rate,
                    }
                    
                    # 新增：存储记录ID数据
                    record_ids_data[matched_target_name] = {
                        'company_id': company_id,
                        'company_name': company_name,
                        'record_id': record_id,
                        'update_timestamp': update_timestamp
                    }
                    
                    found_count += 1
                    
                    # 记录调试信息
                    logging.debug(f"匹配到公司 {matched_target_name}: ID={company_id}, 记录ID={record_id}, 更新时间={update_timestamp}")
                    
            except IndexError:
                continue
            except Exception as e:
                logging.error(f"处理记录时出错: {e}")
                continue
        
        logging.info(f"增强解析完成：找到 {found_count} 家目标公司的数据")
        return odds_data, record_ids_data
    
    def save_record_ids_to_db(self, match_id, record_ids_data):
        """
        将记录ID数据保存到数据库
        
        Args:
            match_id: 比赛ID
            record_ids_data: 记录ID数据字典
        """
        if not record_ids_data:
            logging.warning(f"比赛 {match_id} 没有记录ID数据需要保存")
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            saved_count = 0
            updated_count = 0
            
            for company_name, data in record_ids_data.items():
                try:
                    # 使用 INSERT OR REPLACE 来处理重复数据
                    cursor.execute('''
                        INSERT OR REPLACE INTO odds_record_ids 
                        (match_id, company_id, company_name, record_id, update_timestamp, scrape_time)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        str(match_id),
                        data['company_id'],
                        data['company_name'],
                        data['record_id'],
                        data['update_timestamp'],
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ))
                    
                    if cursor.rowcount > 0:
                        saved_count += 1
                        logging.debug(f"保存记录ID: {company_name} -> {data['record_id']}")
                    
                except Exception as e:
                    logging.error(f"保存公司 {company_name} 的记录ID失败: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            logging.info(f"✅ 比赛 {match_id} 的记录ID保存完成: {saved_count} 条记录")
            
        except Exception as e:
            logging.error(f"❌ 保存记录ID到数据库失败: {e}")
            if 'conn' in locals():
                conn.close()
    
    def get_company_odds_enhanced(self, match_id, max_retries=3):
        """
        增强版获取公司赔率数据（同时获取记录ID）
        
        Args:
            match_id: 比赛ID
            max_retries: 最大重试次数
            
        Returns:
            tuple: (odds_data, record_ids_data)
        """
        # 使用重试机制
        for retry in range(max_retries):
            try:
                odds_js_content = self.fetch_odds_js(match_id)
                
                if not odds_js_content:
                    logging.warning(f"获取比赛ID {match_id} 的赔率数据失败 (尝试 {retry+1}/{max_retries})")
                    if retry < max_retries - 1:
                        wait_time = 1.5 ** retry
                        logging.info(f"等待 {wait_time:.2f} 秒后重试...")
                        time.sleep(wait_time)
                    continue
                
                odds_data, record_ids_data = self.parse_odds_js_enhanced(odds_js_content)
                if odds_data:
                    logging.info(f"成功解析比赛ID {match_id} 的数据，找到 {len(odds_data)} 家公司")
                    
                    # 保存记录ID到数据库
                    self.save_record_ids_to_db(match_id, record_ids_data)
                    
                    return odds_data, record_ids_data
                else:
                    logging.warning(f"解析比赛ID {match_id} 的数据失败 (尝试 {retry+1}/{max_retries})")
                    if retry < max_retries - 1:
                        wait_time = 1.5 ** retry
                        logging.info(f"等待 {wait_time:.2f} 秒后重试...")
                        time.sleep(wait_time)
            except Exception as e:
                logging.error(f"处理比赛ID {match_id} 的数据时出错: {e}")
                if retry < max_retries - 1:
                    wait_time = 1.5 ** retry
                    logging.info(f"等待 {wait_time:.2f} 秒后重试...")
                    time.sleep(wait_time)
        
        logging.error(f"在 {max_retries} 次尝试后仍然无法获取比赛ID {match_id} 的数据")
        return {}, {}
    
    def get_record_ids_from_db(self, match_id, company_name=None):
        """
        从数据库获取记录ID数据
        
        Args:
            match_id: 比赛ID
            company_name: 公司名称（可选，不指定则获取所有）
            
        Returns:
            list: 记录ID数据列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if company_name:
                cursor.execute('''
                    SELECT match_id, company_id, company_name, record_id, update_timestamp, scrape_time
                    FROM odds_record_ids 
                    WHERE match_id = ? AND company_name = ?
                    ORDER BY scrape_time DESC
                ''', (str(match_id), company_name))
            else:
                cursor.execute('''
                    SELECT match_id, company_id, company_name, record_id, update_timestamp, scrape_time
                    FROM odds_record_ids 
                    WHERE match_id = ?
                    ORDER BY company_name, scrape_time DESC
                ''', (str(match_id),))
            
            results = cursor.fetchall()
            conn.close()
            
            # 转换为字典格式
            record_data = []
            for row in results:
                record_data.append({
                    'match_id': row[0],
                    'company_id': row[1],
                    'company_name': row[2],
                    'record_id': row[3],
                    'update_timestamp': row[4],
                    'scrape_time': row[5]
                })
            
            return record_data
            
        except Exception as e:
            logging.error(f"从数据库获取记录ID失败: {e}")
            if 'conn' in locals():
                conn.close()
            return []

def test_enhanced_scraper():
    """测试增强版爬虫"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 配置参数
    from config import ODDS_URL_TEMPLATE, ODDS_HEADERS_TEMPLATE, TARGET_COMPANIES
    
    # 创建增强版爬虫
    scraper = EnhancedOddsScraper(
        url_template=ODDS_URL_TEMPLATE,
        headers_template=ODDS_HEADERS_TEMPLATE,
        target_companies=TARGET_COMPANIES
    )
    
    # 测试比赛ID
    test_match_id = "2696009"
    
    print(f"=== 测试增强版爬虫 ===")
    print(f"比赛ID: {test_match_id}")
    
    # 获取数据
    odds_data, record_ids_data = scraper.get_company_odds_enhanced(test_match_id)
    
    if odds_data:
        print(f"\n✅ 成功获取 {len(odds_data)} 家公司的数据")
        
        print(f"\n=== 赔率数据示例 ===")
        for company, data in list(odds_data.items())[:3]:
            print(f"{company}: 即时赔率 {data['Instant_W']}/{data['Instant_D']}/{data['Instant_L']}")
        
        print(f"\n=== 记录ID数据示例 ===")  
        for company, data in list(record_ids_data.items())[:3]:
            print(f"{company}: 公司ID={data['company_id']}, 记录ID={data['record_id']}")
        
        # 测试从数据库读取
        print(f"\n=== 从数据库读取记录ID ===")
        db_records = scraper.get_record_ids_from_db(test_match_id)
        print(f"数据库中找到 {len(db_records)} 条记录ID")
        
        for record in db_records[:3]:
            print(f"{record['company_name']}: 记录ID={record['record_id']}, 更新时间={record['update_timestamp']}")
    
    else:
        print("❌ 未获取到数据")

if __name__ == "__main__":
    test_enhanced_scraper() 