#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GameDetail 字段分析器
"""

import time
import requests
import re
import json
from datetime import datetime

def get_match_js_content(match_id):
    """获取比赛的JS内容"""
    
    # 生成时间戳
    timestamp_part = str(int(time.time() * 1000))
    if len(timestamp_part) > 13:
        timestamp_part = timestamp_part[-13:]
    else:
        timestamp_part = timestamp_part.ljust(13, '0')
    timestamp = "007" + timestamp_part
    
    # 构建URL和请求头
    url = f"https://1x2d.titan007.com/{match_id}.js?r={timestamp}"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Referer': f'https://op1.titan007.com/oddslist/{match_id}.htm',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        if response.status_code == 200:
            print(f"✅ 成功获取比赛 {match_id} 的JS数据")
            return response.text
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 请求出错: {e}")
        return None

def extract_gamedetail_data(js_content):
    """提取gameDetail数据"""
    
    if not js_content:
        return None
    
    # 查找gameDetail数组
    gamedetail_match = re.search(r'var\s+gameDetail\s*=\s*Array\((.*?)\);', js_content, re.DOTALL)
    if not gamedetail_match:
        print("❌ 在JS内容中未找到gameDetail数组")
        return None
    
    gamedetail_data = gamedetail_match.group(1)
    print(f"✅ 找到gameDetail数组，原始长度: {len(gamedetail_data)} 字符")
    
    return gamedetail_data

def parse_gamedetail_records(gamedetail_data):
    """解析gameDetail记录"""
    
    if not gamedetail_data:
        return []
    
    # 提取每个引号包围的字符串
    company_records = re.findall(r'"([^"]*)"', gamedetail_data)
    print(f"✅ 解析出 {len(company_records)} 条gameDetail记录")
    
    parsed_records = []
    
    for i, record in enumerate(company_records):
        try:
            # gameDetail的记录通常用^分隔
            main_parts = record.split('^')
            
            if len(main_parts) < 2:
                continue
            
            company_id = main_parts[0]
            detail_data = main_parts[1] if len(main_parts) > 1 else ""
            
            # 进一步解析detail_data，通常包含多个|分隔的部分
            detail_parts = detail_data.split('|') if detail_data else []
            
            record_info = {
                'record_index': i,
                'company_id': company_id,
                'raw_record': record,
                'main_parts_count': len(main_parts),
                'detail_parts_count': len(detail_parts),
                'main_parts': main_parts,
                'detail_parts': detail_parts
            }
            
            parsed_records.append(record_info)
            
        except Exception as e:
            print(f"解析记录 {i} 时出错: {e}")
            continue
    
    return parsed_records

def analyze_gamedetail_structure(parsed_records):
    """分析gameDetail的数据结构"""
    
    print(f"\n=== GameDetail 数据结构分析 ===")
    
    if not parsed_records:
        print("没有记录可分析")
        return
    
    # 统计信息
    total_records = len(parsed_records)
    main_parts_counts = [r['main_parts_count'] for r in parsed_records]
    detail_parts_counts = [r['detail_parts_count'] for r in parsed_records]
    
    print(f"总记录数: {total_records}")
    print(f"主要部分数量范围: {min(main_parts_counts)} - {max(main_parts_counts)}")
    print(f"详细部分数量范围: {min(detail_parts_counts)} - {max(detail_parts_counts)}")
    
    # 显示前几条记录的详细结构
    print(f"\n=== 前5条记录的详细结构 ===")
    for i, record in enumerate(parsed_records[:5]):
        print(f"\n--- 记录 {i+1} ---")
        print(f"公司ID: {record['company_id']}")
        print(f"原始记录: {record['raw_record'][:100]}{'...' if len(record['raw_record']) > 100 else ''}")
        print(f"主要部分数: {record['main_parts_count']}")
        print(f"详细部分数: {record['detail_parts_count']}")
        
        # 显示主要部分
        print("主要部分:")
        for j, part in enumerate(record['main_parts']):
            display_part = part[:50] + "..." if len(part) > 50 else part
            print(f"  [{j}] {display_part}")
        
        # 显示详细部分（如果存在）
        if record['detail_parts']:
            print("详细部分 (前10个):")
            for j, part in enumerate(record['detail_parts'][:10]):
                print(f"  [{j}] {part}")
            if len(record['detail_parts']) > 10:
                print(f"  ... (还有 {len(record['detail_parts']) - 10} 个部分)")

def save_gamedetail_analysis(match_id, parsed_records, save_raw_data=True):
    """保存gameDetail分析结果"""
    
    try:
        # 保存详细分析
        analysis_file = f"gamedetail_analysis_{match_id}.txt"
        with open(analysis_file, "w", encoding="utf-8") as f:
            f.write(f"比赛ID: {match_id}\n")
            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总记录数: {len(parsed_records)}\n\n")
            
            f.write("=== 所有记录详细信息 ===\n")
            for record in parsed_records:
                f.write(f"\n--- 记录 {record['record_index'] + 1} ---\n")
                f.write(f"公司ID: {record['company_id']}\n")
                f.write(f"原始记录: {record['raw_record']}\n")
                f.write(f"主要部分数: {record['main_parts_count']}\n")
                f.write(f"详细部分数: {record['detail_parts_count']}\n")
                
                f.write("主要部分:\n")
                for j, part in enumerate(record['main_parts']):
                    f.write(f"  [{j}] {part}\n")
                
                if record['detail_parts']:
                    f.write("详细部分:\n")
                    for j, part in enumerate(record['detail_parts']):
                        f.write(f"  [{j}] {part}\n")
                
                f.write("\n" + "-" * 50 + "\n")
        
        print(f"✅ 详细分析已保存到: {analysis_file}")
        
        # 保存JSON格式的结构化数据
        if save_raw_data:
            json_file = f"gamedetail_data_{match_id}.json"
            with open(json_file, "w", encoding="utf-8") as f:
                json.dump(parsed_records, f, ensure_ascii=False, indent=2)
            print(f"✅ 结构化数据已保存到: {json_file}")
        
    except Exception as e:
        print(f"❌ 保存分析结果失败: {e}")

def compare_game_vs_gamedetail(match_id, js_content):
    """比较game和gameDetail的数据差异"""
    
    print(f"\n=== 比较 game vs gameDetail ===")
    
    # 提取game数据
    game_match = re.search(r'var\s+game\s*=\s*Array\((.*?)\);', js_content, re.DOTALL)
    game_companies = set()
    if game_match:
        game_data = game_match.group(1)
        game_records = re.findall(r'"([^"]*)"', game_data)
        for record in game_records:
            fields = record.split('|')
            if len(fields) > 0:
                game_companies.add(fields[0])  # 公司ID
    
    # 提取gameDetail数据
    gamedetail_match = re.search(r'var\s+gameDetail\s*=\s*Array\((.*?)\);', js_content, re.DOTALL)
    gamedetail_companies = set()
    if gamedetail_match:
        gamedetail_data = gamedetail_match.group(1)
        gamedetail_records = re.findall(r'"([^"]*)"', gamedetail_data)
        for record in gamedetail_records:
            parts = record.split('^')
            if len(parts) > 0:
                gamedetail_companies.add(parts[0])  # 公司ID
    
    print(f"game 数组包含公司数: {len(game_companies)}")
    print(f"gameDetail 数组包含公司数: {len(gamedetail_companies)}")
    
    # 找出差异
    only_in_game = game_companies - gamedetail_companies
    only_in_gamedetail = gamedetail_companies - game_companies
    common_companies = game_companies & gamedetail_companies
    
    print(f"共同公司数: {len(common_companies)}")
    print(f"仅在game中的公司数: {len(only_in_game)}")
    print(f"仅在gameDetail中的公司数: {len(only_in_gamedetail)}")
    
    if only_in_game:
        print(f"仅在game中的公司ID: {sorted(list(only_in_game))[:10]}...")
    if only_in_gamedetail:
        print(f"仅在gameDetail中的公司ID: {sorted(list(only_in_gamedetail))[:10]}...")

def main():
    """主函数"""
    
    print("=" * 60)
    print("🔍 GameDetail 字段分析器")
    print("=" * 60)
    
    # 测试比赛ID
    test_match_id = "2696009"
    
    print(f"分析比赛ID: {test_match_id}")
    
    # 1. 获取JS内容
    js_content = get_match_js_content(test_match_id)
    if not js_content:
        print("❌ 无法获取JS内容，退出")
        return
    
    # 2. 提取gameDetail数据
    gamedetail_data = extract_gamedetail_data(js_content)
    if not gamedetail_data:
        print("❌ 无法提取gameDetail数据，退出")
        return
    
    # 3. 解析记录
    parsed_records = parse_gamedetail_records(gamedetail_data)
    if not parsed_records:
        print("❌ 无法解析gameDetail记录，退出")
        return
    
    # 4. 分析数据结构
    analyze_gamedetail_structure(parsed_records)
    
    # 5. 保存分析结果
    save_gamedetail_analysis(test_match_id, parsed_records)
    
    # 6. 比较game和gameDetail
    compare_game_vs_gamedetail(test_match_id, js_content)
    
    print(f"\n✅ GameDetail 分析完成！")
    print(f"请查看生成的文件:")
    print(f"  - gamedetail_analysis_{test_match_id}.txt (详细分析)")
    print(f"  - gamedetail_data_{test_match_id}.json (结构化数据)")

if __name__ == "__main__":
    main() 