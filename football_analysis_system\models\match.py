class Match:
    """比赛数据模型类"""
    
    def __init__(self, match_id=None, league_name=None, start_time=None, 
                 home_team=None, away_team=None, odds=None):
        """
        初始化比赛对象
        
        Args:
            match_id: 比赛ID
            league_name: 联赛名称
            start_time: 开始时间
            home_team: 主队名称
            away_team: 客队名称
            odds: 赔率字典 {博彩公司名: {赔率数据}}
        """
        self.match_id = match_id
        self.league_name = league_name
        self.start_time = start_time
        self.home_team = home_team
        self.away_team = away_team
        self.odds = odds or {}
    
    def to_dict(self):
        """
        将比赛对象转换为字典
        """
        return {
            'match_id': self.match_id,
            'league_name': self.league_name,
            'start_time': self.start_time,
            'home_team': self.home_team,
            'away_team': self.away_team,
            'odds': self.odds
        }
    
    @classmethod
    def from_dict(cls, data):
        """
        从字典创建比赛对象
        
        Args:
            data: 包含比赛数据的字典
            
        Returns:
            Match: 比赛对象
        """
        return cls(
            match_id=data.get('match_id'),
            league_name=data.get('league_name'),
            start_time=data.get('start_time'),
            home_team=data.get('home_team'),
            away_team=data.get('away_team'),
            odds=data.get('odds', {})
        )
    
    def __str__(self):
        """字符串表示"""
        return f"{self.home_team} vs {self.away_team} ({self.league_name})"