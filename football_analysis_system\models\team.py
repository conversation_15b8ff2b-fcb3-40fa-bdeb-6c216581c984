class Team:
    """球队数据模型类"""
    
    def __init__(self, name=None, power=None, rating=None):
        """
        初始化球队对象
        
        Args:
            name: 球队名称
            power: 广义实力
            rating: 评分
        """
        self.name = name
        self.power = power  # 广义实力，如"人强2"、"普强1"等
        self.rating = rating  # 评分，数值型
        self.multi_company_ratings = {}  # 多公司评分数据
    
    def to_dict(self):
        """
        将球队对象转换为字典
        """
        return {
            'name': self.name,
            'power': self.power,
            'rating': self.rating,
            'multi_company_ratings': self.multi_company_ratings
        }
    
    @classmethod
    def from_dict(cls, data):
        """
        从字典创建球队对象
        
        Args:
            data: 包含球队数据的字典
            
        Returns:
            Team: 球队对象
        """
        team = cls(
            name=data.get('name'),
            power=data.get('power'),
            rating=data.get('rating')
        )
        team.multi_company_ratings = data.get('multi_company_ratings', {})
        return team
    
    def get_company_rating(self, company_id):
        """
        获取特定公司的评分数据
        
        Args:
            company_id: 公司ID
            
        Returns:
            dict: 包含公司评分数据的字典，如果找不到则返回None
        """
        return self.multi_company_ratings.get(company_id)
    
    def get_available_companies(self):
        """
        获取所有可用的公司ID和名称
        
        Returns:
            dict: 公司ID到公司名称的映射
        """
        result = {}
        for company_id, data in self.multi_company_ratings.items():
            result[company_id] = data.get('company_name', 'Unknown')
        return result
    
    def __str__(self):
        """字符串表示"""
        rating_str = f", 评分:{self.rating}" if self.rating is not None else ""
        power_str = f", 实力:{self.power}" if self.power is not None else ""
        return f"{self.name}{power_str}{rating_str}"