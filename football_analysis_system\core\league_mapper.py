"""
联赛名称映射模块 - 用于统一项目中的联赛名称识别和转换
"""

class LeagueMapper:
    """联赛名称映射器"""
    
    # 联赛名称映射表 - 将各种可能的联赛名称映射到标准名称
    LEAGUE_NAME_MAPPING = {
        # 主要欧洲联赛
        "英超": "英超",
        "Premier League": "英超",
        "EPL": "英超",
        "英格兰超级联赛": "英超",
        
        "英冠": "英冠", 
        "Championship": "英冠",
        "英格兰冠军联赛": "英冠",
        
        "英甲": "英甲",
        "League One": "英甲",
        "英格兰甲级联赛": "英甲",
        
        "意甲": "意甲",
        "Serie A": "意甲",
        "意大利甲级联赛": "意甲",
        
        "意乙": "意乙",
        "Serie B": "意乙", 
        "意大利乙级联赛": "意乙",
        
        "西甲": "西甲",
        "La Liga": "西甲",
        "西班牙甲级联赛": "西甲",
        
        "西乙": "西乙",
        "Segunda División": "西乙",
        "西班牙乙级联赛": "西乙",
        "西乙升": "西乙",
        
        "德甲": "德甲",
        "Bundesliga": "德甲",
        "德国甲级联赛": "德甲",
        
        "德乙": "德乙",
        "2. Bundesliga": "德乙",
        "德国乙级联赛": "德乙",
        
        "法甲": "法甲",
        "Ligue 1": "法甲",
        "法国甲级联赛": "法甲",
        
        "法乙": "法乙",
        "Ligue 2": "法乙",
        "法国乙级联赛": "法乙",
        
        # 亚洲联赛
        "日职联": "日职联",
        "J1 League": "日职联",
        "日本职业足球联赛": "日职联",
        "日甲": "日职联",
        
        "日职乙": "日职乙",
        "J2 League": "日职乙", 
        "日乙": "日职乙",
        
        "日职丙": "日职丙",
        "J3 League": "日职丙",
        "日丙": "日职丙",
        
        "日职女甲": "日职女甲",
        "WE League": "日职女甲",
        
        "日职女乙": "日职女乙",
        "Nadeshiko League": "日职女乙",
        
        "韩K": "韩K",
        "K League 1": "韩K",
        "韩国K联赛": "韩K",
        
        "韩K2": "韩K2", 
        "K League 2": "韩K2",
        "韩国K2联赛": "韩K2",
        
        "中超": "中超",
        "Chinese Super League": "中超",
        "中国足球超级联赛": "中超",
        
        # 美洲联赛
        "美职业": "美职业",
        "MLS": "美职业",
        "Major League Soccer": "美职业",
        "美国职业大联盟": "美职业",
        "美国职业大联盟联赛": "美职业",
        
        "美甲": "美甲",
        "USL Championship": "美甲",
        "USLC": "美甲",
        
        "巴西甲": "巴西甲",
        "Brasileirão": "巴西甲",
        "巴西足球甲级联赛": "巴西甲",
        
        "巴西乙": "巴西乙",
        "Série B": "巴西乙",
        "巴西足球乙级联赛": "巴西乙",
        
        "阿根廷甲": "阿根廷甲",
        "Primera División": "阿根廷甲",
        "阿根廷足球甲级联赛": "阿根廷甲",
        
        # 澳洲联赛
        "澳超": "澳超",
        "A-League": "澳超",
        "澳大利亚职业足球联赛": "澳超",
        
        "澳昆超": "澳昆超",
        "澳西超": "澳西超", 
        "澳南超": "澳南超",
        "澳布超": "澳布超",
        "澳威超": "澳威超",
        "澳维甲": "澳维甲",
        "澳维超": "澳维超",
        
        # 其他亚洲联赛
        "印度超": "印度超",
        "Indian Super League": "印度超",
        "ISL": "印度超",
        
        "印度甲": "印度甲",
        "I-League": "印度甲",
        
        "越南联": "越南联",
        "V.League 1": "越南联",
        
        "越南甲": "越南甲", 
        "V.League 2": "越南甲",
        
        "马来超": "马来超",
        "Malaysia Super League": "马来超",
        
        "马来甲": "马来甲",
        "Malaysia Premier League": "马来甲",
        
        "泰超": "泰超",
        "Thai League 1": "泰超",
        
        "泰甲": "泰甲",
        "Thai League 2": "泰甲",
        
        "巴林超": "巴林超",
        "Bahraini Premier League": "巴林超",
        
        # 北欧联赛
        "瑞典超": "瑞典超",
        "Allsvenskan": "瑞典超",
        
        "瑞典甲": "瑞典甲",
        "Superettan": "瑞典甲",
        
        "瑞典乙": "瑞典乙",
        "Ettan": "瑞典乙",
        
        "瑞典丙": "瑞典丙",
        "Division 2": "瑞典丙",
        
        "挪超": "挪超",
        "Eliteserien": "挪超",
        "挪威超级联赛": "挪超",
        
        "挪甲": "挪甲",
        "OBOS-ligaen": "挪甲",
        
        "丹麦超": "丹麦超",
        "Danish Superliga": "丹麦超",
        
        "丹麦甲": "丹麦甲",
        "NordicBet Liga": "丹麦甲",
        
        "丹麦乙": "丹麦乙",
        "2. Division": "丹麦乙",
        
        "丹麦丙": "丹麦丙",
        "3. Division": "丹麦丙",
        
        # 非洲联赛
        "南非超": "南非超",
        "Premier Soccer League": "南非超",
        "PSL": "南非超",
        
        "南非甲": "南非甲",
        "GladAfrica Championship": "南非甲",
        
        # 其他常见联赛
        "荷甲": "荷甲",
        "Eredivisie": "荷甲",
        
        "荷乙": "荷乙", 
        "Eerste Divisie": "荷乙",
        
        "葡超": "葡超",
        "Primeira Liga": "葡超",
        
        "葡甲": "葡甲",
        "Liga Portugal 2": "葡甲",
        
        "苏超": "苏超",
        "Scottish Premiership": "苏超",
        
        "苏冠": "苏冠",
        "Scottish Championship": "苏冠",
        
        "瑞士超": "瑞士超",
        "Swiss Super League": "瑞士超",
        
        "瑞士甲": "瑞士甲",
        "Swiss Challenge League": "瑞士甲",
        
        "土耳其超": "土耳其超",
        "Süper Lig": "土耳其超",
        
        "俄超": "俄超",
        "Russian Premier League": "俄超",
        
        "俄甲": "俄甲",
        "FNL": "俄甲",
    }
    
    # 联赛代码映射
    LEAGUE_CODE_MAPPING = {
        "ENG1": "英超",
        "ENG2": "英冠", 
        "ENG3": "英甲",
        "ITA1": "意甲",
        "ITA2": "意乙",
        "ESP1": "西甲",
        "ESP2": "西乙",
        "GER1": "德甲",
        "GER2": "德乙",
        "FRA1": "法甲",
        "FRA2": "法乙",
        "JPN1": "日职联",
        "JPN2": "日职乙",
        "JPN3": "日职丙",
        "JPNW1": "日职女甲",
        "JPNW2": "日职女乙",
        "KOR1": "韩K",
        "KOR2": "韩K2",
        "CHN1": "中超",
        "USA1": "美职业",
        "USA2": "美甲",
        "BRA1": "巴西甲",
        "BRA2": "巴西乙",
        "ARG1": "阿根廷甲",
        "ARG2": "阿根廷乙",
        "AUS1": "澳超",
        "AUS_QLD": "澳昆超",
        "AUS_WA": "澳西超",
        "AUS_SA": "澳南超",
        "AUS_BRI": "澳布超",
        "AUS_NSW": "澳威超",
        "AUS_VIC2": "澳维甲",
        "AUS_VIC1": "澳维超",
        "IND1": "印度超",
        "IND2": "印度甲",
        "VIE1": "越南联",
        "VIE2": "越南甲",
        "MYS1": "马来超",
        "MYS2": "马来甲",
        "THA1": "泰超",
        "THA2": "泰甲",
        "BHR1": "巴林超",
        "SWE1": "瑞典超",
        "SWE2": "瑞典甲",
        "SWE3": "瑞典乙",
        "SWE4": "瑞典丙",
        "NOR1": "挪超",
        "NOR2": "挪甲",
        "DEN1": "丹麦超",
        "DEN2": "丹麦甲",
        "DEN3": "丹麦乙",
        "DEN4": "丹麦丙",
        "NED1": "荷甲",
        "NED2": "荷乙",
        "POR1": "葡超",
        "POR2": "葡甲",
        "SCO1": "苏超",
        "SCO2": "苏冠",
        "SUI1": "瑞士超",
        "SUI2": "瑞士甲",
        "TUR1": "土耳其超",
        "RUS1": "俄超",
        "RUS2": "俄甲",
        "RSA1": "南非超",
        "RSA2": "南非甲",
    }
    
    @classmethod
    def normalize_league_name(cls, league_name: str) -> str:
        """
        标准化联赛名称
        
        Args:
            league_name: 输入的联赛名称
            
        Returns:
            str: 标准化后的联赛名称
        """
        if not league_name:
            return league_name
            
        # 移除多余空格
        normalized = league_name.strip()
        
        # 检查映射表
        if normalized in cls.LEAGUE_NAME_MAPPING:
            return cls.LEAGUE_NAME_MAPPING[normalized]
        
        # 检查代码映射
        if normalized in cls.LEAGUE_CODE_MAPPING:
            return cls.LEAGUE_CODE_MAPPING[normalized]
            
        # 如果没有找到映射，返回原名称
        return normalized
    
    @classmethod
    def get_league_code(cls, league_name: str) -> str:
        """
        根据联赛名称获取联赛代码
        
        Args:
            league_name: 联赛名称
            
        Returns:
            str: 联赛代码，如果没找到返回None
        """
        normalized_name = cls.normalize_league_name(league_name)
        
        # 反向查找代码
        for code, name in cls.LEAGUE_CODE_MAPPING.items():
            if name == normalized_name:
                return code
                
        return None
    
    @classmethod
    def is_supported_league(cls, league_name: str) -> bool:
        """
        检查是否支持该联赛
        
        Args:
            league_name: 联赛名称
            
        Returns:
            bool: 是否支持
        """
        normalized = cls.normalize_league_name(league_name)
        return normalized in cls.LEAGUE_CODE_MAPPING.values()
    
    @classmethod
    def get_all_supported_leagues(cls) -> list:
        """
        获取所有支持的联赛列表
        
        Returns:
            list: 支持的联赛名称列表
        """
        return list(cls.LEAGUE_CODE_MAPPING.values())
    
    @classmethod
    def get_league_info(cls, league_name: str) -> dict:
        """
        获取联赛详细信息
        
        Args:
            league_name: 联赛名称
            
        Returns:
            dict: 联赛信息字典
        """
        normalized_name = cls.normalize_league_name(league_name)
        code = cls.get_league_code(normalized_name)
        
        return {
            'name': normalized_name,
            'code': code,
            'supported': cls.is_supported_league(league_name),
            'original_name': league_name
        } 