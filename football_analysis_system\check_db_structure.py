#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看现有数据库结构
"""

import sqlite3
import os

def check_database_structure():
    """查看数据库结构"""
    
    db_path = "data/matches_and_odds.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📊 数据库: {db_path}")
        print(f"表总数: {len(tables)}")
        print()
        
        for table_name, in tables:
            print(f"=== 表: {table_name} ===")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            print("字段结构:")
            for col_info in columns:
                cid, name, data_type, notnull, default_value, pk = col_info
                pk_str = " (主键)" if pk else ""
                notnull_str = " NOT NULL" if notnull else ""
                default_str = f" DEFAULT {default_value}" if default_value else ""
                print(f"  {name}: {data_type}{notnull_str}{default_str}{pk_str}")
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"记录数: {count}")
            
            # 如果是odds表，显示几条示例数据
            if table_name == 'odds':
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3;")
                sample_data = cursor.fetchall()
                if sample_data:
                    print("示例数据:")
                    for i, row in enumerate(sample_data, 1):
                        print(f"  记录{i}: {row}")
            
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查看数据库结构失败: {e}")

if __name__ == "__main__":
    check_database_structure() 