# 历史赔率区间分析功能使用指南

## 功能概述

历史赔率区间分析功能是足球分析系统的重要组成部分，用于分析和可视化历史赔率数据在不同区间的变化趋势。该功能可以帮助用户：

- 查看特定博彩公司的历史赔率变化
- 分析赔率在不同区间的分布情况
- 通过可视化图表观察趋势变化
- 导出分析结果进行进一步研究

## 功能特性

### 核心功能
- **历史数据获取**: 从odds_history表获取指定比赛和博彩公司的历史赔率数据
- **返还率计算**: 自动计算每个时间点的返还率
- **满水赔率计算**: 使用现有的calculate_true_odds方法计算满水赔率
- **区间分析**: 集成现有的find_gap_for_odds和find_interval_for_gap方法进行区间计算
- **可视化展示**: 通过matplotlib折线图展示主平客三条线的区间变化趋势

### 技术特性
- **高性能处理**: 支持大量历史数据的快速处理
- **内存优化**: 智能内存管理，避免内存泄漏
- **并发安全**: 支持多线程数据处理
- **错误处理**: 完善的错误处理和用户提示机制
- **数据验证**: 自动验证数据完整性和有效性

## 使用方法

### 1. 启动应用程序

启动足球分析系统主应用程序：

```bash
python football_analysis_system/ui/modern_app.py
```

### 2. 选择比赛

1. 在左侧面板的比赛列表中选择要分析的比赛
2. 系统会自动加载比赛的基本信息

### 3. 打开历史区间分析标签页

1. 点击右侧分析面板中的"📊 历史区间"标签页
2. 系统会自动设置当前选择的比赛ID

### 4. 选择博彩公司

1. 在控制面板的"博彩公司"下拉框中选择要分析的公司
2. 系统会显示该公司的历史记录数量和时间范围

### 5. 开始分析

1. 点击"开始分析"按钮
2. 系统会在后台处理历史数据并生成可视化图表
3. 进度条会显示处理进度

### 6. 查看结果

分析完成后，您可以：
- 查看主平客三条折线的区间变化趋势
- 通过图表观察赔率在不同时间点的区间分布
- 使用鼠标在图表上进行交互操作

### 7. 保存和导出

- **保存图表**: 点击"保存图表"按钮将图表保存为PNG格式
- **导出数据**: 使用`export_analysis_data()`方法导出CSV格式的分析数据

## 界面说明

### 控制面板

- **比赛ID**: 显示当前分析的比赛ID
- **博彩公司**: 下拉框选择要分析的博彩公司
- **开始分析**: 开始执行历史数据分析
- **清除图表**: 清除当前显示的图表
- **保存图表**: 将图表保存到本地文件
- **刷新数据**: 重新加载可用的博彩公司数据

### 图表区域

- **主胜区间**: 红色折线，显示主队胜赔率的区间变化
- **平局区间**: 青色折线，显示平局赔率的区间变化
- **客胜区间**: 蓝色折线，显示客队胜赔率的区间变化
- **X轴**: 时间轴，显示历史数据的时间点
- **Y轴**: 区间类型，显示不同的开盘区间

### 状态栏

- **状态信息**: 显示当前操作状态和结果信息
- **进度条**: 显示数据处理进度

## 区间类型说明

### 主客胜区间类型

系统支持以下区间类型（从低到高）：

1. **超深诱盘** - 极端深盘，风险很高
2. **超深盘中下水/中上水** - 深盘区间
3. **超散盘系列** - 散盘区间（低水、中低水、中水、中高水）
4. **超实盘系列** - 实盘区间（超低水到超高水）
5. **低开盘系列** - 低开区间（中低水、中水、中高水）
6. **实开盘系列** - 实开区间（超低水到超高水）
7. **高开盘系列** - 高开区间（中低水、中水、中高水）
8. **韬光盘系列** - 韬光区间（超低水到高水）
9. **超韬盘系列** - 超韬区间（中下水、中上水、利盘）

### 平局区间类型

平局区间基于标准平赔计算：

- **超深平** - 极低平赔
- **超散平** - 较低平赔
- **超实平** - 实际平赔偏低
- **低开平** - 低开平赔
- **平实低/高** - 实际平赔
- **平中庸上/下** - 中庸平赔
- **高开平** - 高开平赔
- **韬光平** - 韬光平赔
- **超韬平** - 超韬平赔

## 数据格式说明

### 输入数据格式

系统从odds_history表读取数据，要求包含以下字段：

```sql
CREATE TABLE odds_history (
    match_id TEXT,           -- 比赛ID
    company_id TEXT,         -- 博彩公司ID
    company_name TEXT,       -- 博彩公司名称
    home_odds REAL,          -- 主胜赔率
    draw_odds REAL,          -- 平局赔率
    away_odds REAL,          -- 客胜赔率
    update_time TEXT,        -- 更新时间
    record_id TEXT,          -- 记录ID（可选）
    kelly_home REAL,         -- 主胜凯利值（可选）
    kelly_draw REAL,         -- 平局凯利值（可选）
    kelly_away REAL          -- 客胜凯利值（可选）
);
```

### 输出数据格式

分析结果包含以下信息：

```python
@dataclass
class IntervalAnalysisResult:
    timestamp: str              # 时间戳
    home_interval: str          # 主胜区间类型
    draw_interval: str          # 平局区间类型
    away_interval: str          # 客胜区间类型
    home_rule_value: float      # 主胜规则值
    away_rule_value: float      # 客胜规则值
    home_true_odds: float       # 主胜满水赔率
    draw_true_odds: float       # 平局满水赔率
    away_true_odds: float       # 客胜满水赔率
    payout_rate: float          # 返还率
```

## 性能优化建议

### 数据量优化

- **大数据集**: 对于超过1000条记录的数据集，系统会自动进行性能优化
- **内存管理**: 系统会自动清理不需要的中间数据
- **分批处理**: 可以考虑分时间段进行分析以提高性能

### 图表优化

- **数据点限制**: 当数据点过多时，图表可能显示较慢，建议筛选时间范围
- **交互性能**: 大量数据点时，图表交互可能有延迟
- **保存优化**: 保存高分辨率图表时需要更多时间

## 故障排除

### 常见问题

1. **没有可用的博彩公司**
   - 检查数据库中是否存在对应比赛的历史数据
   - 确认odds_history表结构正确

2. **数据处理失败**
   - 检查赔率数据是否有效（大于0）
   - 确认时间格式正确
   - 查看日志文件获取详细错误信息

3. **图表显示异常**
   - 检查matplotlib是否正确安装
   - 确认系统支持图形界面
   - 尝试清除图表后重新分析

4. **性能问题**
   - 减少分析的时间范围
   - 关闭其他占用内存的程序
   - 检查系统资源使用情况

### 日志文件

系统会记录详细的操作日志，日志文件位置：
- 主日志: `football_analysis.log`
- 错误日志: 控制台输出

查看日志可以帮助诊断问题：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## API参考

### 主要类和方法

#### HistoricalDataProcessor

```python
class HistoricalDataProcessor:
    def __init__(self, odds_analyzer, interval_analyzer)
    def calculate_payout_rate(self, home_odds, draw_odds, away_odds)
    def process_historical_odds(self, historical_data, gap_difference)
    def validate_historical_data(self, data)
    def process_and_visualize_historical_data(self, historical_data, gap_difference)
```

#### HistoricalDatabaseInterface

```python
class HistoricalDatabaseInterface:
    def __init__(self, db_path)
    def get_available_companies(self, match_id)
    def get_historical_odds(self, match_id, company_id)
    def validate_database_connection(self)
```

#### HistoricalChartVisualizer

```python
class HistoricalChartVisualizer:
    def __init__(self, parent_frame=None)
    def plot_historical_intervals(self, visualization_data, company_name)
    def save_chart(self, filepath, dpi=300)
    def clear_chart(self)
```

#### HistoricalOddsIntervalTab

```python
class HistoricalOddsIntervalTab:
    def __init__(self, parent, odds_analyzer, interval_analyzer, db_path, match_id=None)
    def set_match_id(self, match_id)
    def get_analysis_results(self)
    def export_analysis_data(self, filepath)
```

## 扩展开发

### 添加新的区间类型

1. 在`IntervalVisualizer`中添加新的区间映射
2. 更新`IntervalAnalyzer`的区间计算逻辑
3. 在图表可视化中添加相应的颜色配置

### 自定义分析算法

1. 继承`HistoricalDataProcessor`类
2. 重写相关的计算方法
3. 在UI中使用自定义的处理器

### 添加新的数据源

1. 实现`HistoricalDatabaseInterface`接口
2. 适配数据格式转换
3. 在UI中配置新的数据源

## 版本信息

- **当前版本**: 1.0.0
- **兼容性**: Python 3.7+
- **依赖库**: tkinter, matplotlib, pandas, sqlite3
- **最后更新**: 2024年8月

## 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查日志文件获取详细错误信息
3. 确认系统环境和依赖库版本
4. 联系技术支持团队

---

*本文档随系统更新而更新，请定期查看最新版本。*