import tkinter as tk
from tkinter import ttk
import time
from PIL import Image, ImageTk, ImageDraw
from football_analysis_system.config import COLOR_PRIMARY, FONT_NORMAL

class ModernStatusBar(ttk.Frame):
    """现代化状态栏"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, style="Card.TFrame", **kwargs)
        
        # 初始状态
        self.current_status = "就绪"
        self.matches_loaded = 0
        self.leagues_loaded = 0
        self.is_busy = False
        
        # 创建组件
        self._create_widgets()
        
        # 开始时间更新
        self._update_time()
    
    def _create_widgets(self):
        """创建状态栏组件"""
        # 主容器
        main_container = ttk.Frame(self, style="Card.TFrame")
        main_container.pack(fill=tk.X, padx=20, pady=8)
        
        # 左侧：状态图标和文本
        left_frame = ttk.Frame(main_container, style="Card.TFrame")
        left_frame.pack(side=tk.LEFT)
        
        # 状态指示器（彩色圆点）
        self.status_indicator_canvas = tk.Canvas(
            left_frame, width=10, height=10, 
            bg="white", highlightthickness=0
        )
        self.status_indicator_canvas.pack(side=tk.LEFT, padx=(0, 5))
        self.status_indicator = self.status_indicator_canvas.create_oval(
            2, 2, 8, 8, fill="#10b981", outline=""
        )
        
        # 状态文本
        self.status_label = ttk.Label(
            left_frame, 
            text=self.current_status,
            style="Card.TLabel"
        )
        self.status_label.pack(side=tk.LEFT)
        
        # 分隔符
        separator = ttk.Separator(main_container, orient="vertical")
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=15, pady=5)
        
        # 数据加载信息
        info_frame = ttk.Frame(main_container, style="Card.TFrame")
        info_frame.pack(side=tk.LEFT)
        
        # 使用图标+文本的方式显示加载信息
        match_icon_label = ttk.Label(info_frame, text="🏆", style="Card.TLabel")
        match_icon_label.pack(side=tk.LEFT, padx=(0, 5))
        
        self.match_count_label = ttk.Label(
            info_frame,
            text=f"比赛: {self.matches_loaded}",
            style="Card.TLabel"
        )
        self.match_count_label.pack(side=tk.LEFT, padx=(0, 15))
        
        league_icon_label = ttk.Label(info_frame, text="🌍", style="Card.TLabel")
        league_icon_label.pack(side=tk.LEFT, padx=(0, 5))
        
        self.league_count_label = ttk.Label(
            info_frame,
            text=f"联赛: {self.leagues_loaded}",
            style="Card.TLabel"
        )
        self.league_count_label.pack(side=tk.LEFT)
        
        # 右侧：时间和版本信息
        right_frame = ttk.Frame(main_container, style="Card.TFrame")
        right_frame.pack(side=tk.RIGHT)
        
        # 版本信息 - 可选
        self.version_label = ttk.Label(
            right_frame,
            text="数据库版本: 2023.1",
            style="Card.TLabel"
        )
        self.version_label.pack(side=tk.RIGHT, padx=(15, 0))
        
        # 时间显示
        self.time_label = ttk.Label(
            right_frame,
            text="",
            style="Card.TLabel"
        )
        self.time_label.pack(side=tk.RIGHT)
        
    def _update_time(self):
        """更新时间显示"""
        current_time = time.strftime("%H:%M:%S")
        current_date = time.strftime("%Y-%m-%d")
        self.time_label.configure(text=f"{current_date} {current_time}")
        
        # 每隔1秒更新一次
        self.after(1000, self._update_time)
    
    def set_status(self, status, is_busy=False):
        """设置状态信息"""
        self.current_status = status
        self.is_busy = is_busy
        self.status_label.configure(text=status)
        
        # 更新状态指示器颜色
        if is_busy:
            self.status_indicator_canvas.itemconfig(
                self.status_indicator, fill="#f59e0b"
            )
            # 开始闪烁动画
            self._start_indicator_animation()
        else:
            self.status_indicator_canvas.itemconfig(
                self.status_indicator, fill="#10b981"
            )
            # 停止动画
            try:
                if hasattr(self, "_animation_id"):
                    self.after_cancel(self._animation_id)
            except:
                pass
    
    def _start_indicator_animation(self):
        """开始状态指示器闪烁动画"""
        if not self.is_busy:
            return
            
        # 交替显示繁忙/警告色
        current_color = self.status_indicator_canvas.itemcget(
            self.status_indicator, "fill"
        )
        
        new_color = "#f59e0b" if current_color == "#ef4444" else "#ef4444"
        self.status_indicator_canvas.itemconfig(
            self.status_indicator, fill=new_color
        )
        
        # 设置下一次动画
        self._animation_id = self.after(500, self._start_indicator_animation)
        
    def set_data_counts(self, matches, leagues):
        """设置数据计数"""
        self.matches_loaded = matches
        self.leagues_loaded = leagues
        
        # 更新显示
        self.match_count_label.configure(text=f"比赛: {matches}")
        self.league_count_label.configure(text=f"联赛: {leagues}")
        
    def set_db_version(self, version):
        """设置数据库版本"""
        self.version_label.configure(text=f"数据库版本: {version}")


class StatusBar(tk.Frame):
    """状态栏类（旧版-为兼容保留）"""
    
    def __init__(self, parent):
        super().__init__(parent, bg="#f0f0f0", height=25)
        self._create_widgets()
        self._update_time()
    
    def _create_widgets(self):
        # 状态文本
        self.status_text = tk.StringVar(value="就绪")
        self.status_label = tk.Label(
            self, 
            textvariable=self.status_text,
            bg="#f0f0f0", 
            bd=1, 
            relief=tk.SUNKEN, 
            anchor=tk.W,
            padx=5
        )
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 时间显示
        self.time_var = tk.StringVar()
        self.time_label = tk.Label(
            self, 
            textvariable=self.time_var,
            bg="#f0f0f0", 
            bd=1, 
            relief=tk.SUNKEN,
            padx=5,
            width=20
        )
        self.time_label.pack(side=tk.RIGHT, fill=tk.Y)
    
    def _update_time(self):
        """更新时间显示"""
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)
        self.after(1000, self._update_time)
    
    def set_status(self, text):
        """设置状态文本"""
        self.status_text.set(text)
    
    def set_match_count(self, count):
        """设置比赛计数"""
        pass  # 旧版中未实现，为兼容性保留